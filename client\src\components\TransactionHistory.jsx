import React, { useState, useEffect } from 'react';
import { 
  History, 
  Plus, 
  Minus, 
  Filter, 
  Download, 
  Calendar,
  Coins,
  CreditCard,
  MessageSquare,
  Upload,
  BarChart3,
  Image
} from 'lucide-react';
import { useCredit } from '../context/CreditContext';

const TransactionHistory = () => {
  const { transactions, loadTransactionHistory, isLoading } = useCredit();
  const [filteredTransactions, setFilteredTransactions] = useState([]);
  const [filter, setFilter] = useState('all'); // 'all', 'purchase', 'usage'
  const [dateRange, setDateRange] = useState('all'); // 'all', 'today', 'week', 'month'

  // Load transactions on mount
  useEffect(() => {
    loadTransactionHistory();
  }, []);

  // Apply filters
  useEffect(() => {
    let filtered = [...transactions];

    // Type filter
    if (filter !== 'all') {
      filtered = filtered.filter(t => t.type === filter);
    }

    // Date filter
    const now = new Date();
    if (dateRange !== 'all') {
      const filterDate = new Date();
      
      switch (dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
      }
      
      filtered = filtered.filter(t => new Date(t.timestamp) >= filterDate);
    }

    setFilteredTransactions(filtered);
  }, [transactions, filter, dateRange]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  // Format date
  const formatDate = (date) => {
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  // Get transaction icon
  const getTransactionIcon = (transaction) => {
    if (transaction.type === 'purchase') {
      return <Plus className="w-4 h-4 text-green-500" />;
    }
    
    // Usage icons based on description
    if (transaction.description.includes('tin nhắn')) {
      return <MessageSquare className="w-4 h-4 text-blue-500" />;
    }
    if (transaction.description.includes('file') || transaction.description.includes('upload')) {
      return <Upload className="w-4 h-4 text-purple-500" />;
    }
    if (transaction.description.includes('chart') || transaction.description.includes('biểu đồ')) {
      return <BarChart3 className="w-4 h-4 text-orange-500" />;
    }
    if (transaction.description.includes('image') || transaction.description.includes('hình ảnh')) {
      return <Image className="w-4 h-4 text-pink-500" />;
    }
    
    return <Minus className="w-4 h-4 text-red-500" />;
  };

  // Get transaction color
  const getTransactionColor = (transaction) => {
    return transaction.type === 'purchase' 
      ? 'text-green-600' 
      : 'text-red-600';
  };

  // Export transactions
  const handleExport = () => {
    const csvContent = [
      ['Ngày', 'Loại', 'Số lượng', 'Mô tả', 'Trạng thái'],
      ...filteredTransactions.map(t => [
        formatDate(t.timestamp),
        t.type === 'purchase' ? 'Mua' : 'Sử dụng',
        t.amount,
        t.description,
        t.status === 'completed' ? 'Hoàn thành' : t.status
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `transactions_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <History className="w-5 h-5 text-gray-600" />
            <h2 className="text-lg font-semibold text-gray-900">
              Lịch sử giao dịch
            </h2>
          </div>
          
          <button
            onClick={handleExport}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <Download className="w-4 h-4" />
            Xuất file
          </button>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4">
          {/* Type filter */}
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tất cả</option>
              <option value="purchase">Mua credit</option>
              <option value="usage">Sử dụng</option>
            </select>
          </div>

          {/* Date filter */}
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-500" />
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tất cả thời gian</option>
              <option value="today">Hôm nay</option>
              <option value="week">7 ngày qua</option>
              <option value="month">30 ngày qua</option>
            </select>
          </div>
        </div>
      </div>

      {/* Transaction List */}
      <div className="p-6">
        {filteredTransactions.length === 0 ? (
          <div className="text-center py-8">
            <Coins className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Chưa có giao dịch nào
            </h3>
            <p className="text-gray-600">
              {filter === 'all' 
                ? 'Lịch sử giao dịch sẽ hiển thị ở đây'
                : `Không có giao dịch ${filter === 'purchase' ? 'mua credit' : 'sử dụng'} nào`
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                {/* Left side - Icon and details */}
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0">
                    {getTransactionIcon(transaction)}
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900">
                      {transaction.description}
                    </h4>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span>{formatDate(transaction.timestamp)}</span>
                      {transaction.paymentMethod && (
                        <span className="flex items-center gap-1">
                          <CreditCard className="w-3 h-3" />
                          QR Code
                        </span>
                      )}
                      <span className={`
                        px-2 py-1 rounded-full text-xs font-medium
                        ${transaction.status === 'completed' 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-yellow-100 text-yellow-700'
                        }
                      `}>
                        {transaction.status === 'completed' ? 'Hoàn thành' : 'Đang xử lý'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Right side - Amount */}
                <div className="text-right">
                  <div className={`font-semibold ${getTransactionColor(transaction)}`}>
                    {transaction.amount > 0 ? '+' : ''}{transaction.amount.toLocaleString()} credits
                  </div>
                  {transaction.cost && (
                    <div className="text-sm text-gray-600">
                      {formatCurrency(transaction.cost)}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Summary */}
      {filteredTransactions.length > 0 && (
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">
                +{filteredTransactions
                  .filter(t => t.type === 'purchase')
                  .reduce((sum, t) => sum + t.amount, 0)
                  .toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Credits đã mua</div>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-red-600">
                {filteredTransactions
                  .filter(t => t.type === 'usage')
                  .reduce((sum, t) => sum + t.amount, 0)
                  .toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Credits đã dùng</div>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {filteredTransactions.length}
              </div>
              <div className="text-sm text-gray-600">Tổng giao dịch</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TransactionHistory;
