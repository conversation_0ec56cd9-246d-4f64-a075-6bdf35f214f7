// Chat message analyzer to determine type and metadata

export const analyzeChatContext = (message) => {
  const content = message.toLowerCase().trim();
  
  // Define patterns for different chat types
  const patterns = {
    course_inquiry: [
      'kh<PERSON>a học', 'kho<PERSON> học', 'course', 'học tập', 'giảng dạy', 'bài học',
      'curriculum', 'chương trình học', 'môn học', 'education', 'training'
    ],
    ads_analysis: [
      'quảng cáo', 'ads', 'marketing', 'campaign', 'advertisement', 'promotion',
      'facebook ads', 'google ads', 'tiktok ads', 'chiến dịch', 'brand'
    ],
    content_gen: [
      'viết', 'tạo nội dung', 'content', 'blog', 'article', 'copywriting',
      'sáng tạo', 'generate', 'write', 'script', 'content creation'
    ],
    avatar_analysis: [
      'avatar', 'ảnh đại diện', 'hình <PERSON>nh', 'phân tích ảnh', 'image analysis',
      'photo', 'picture', 'visual', 'design'
    ]
  };

  // Determine chat type based on content
  let chatType = 'general';
  let priority = 'medium';
  let metadata = {};

  // Check for course inquiry
  if (patterns.course_inquiry.some(pattern => content.includes(pattern))) {
    chatType = 'course_inquiry';
    priority = 'high';
    metadata = {
      courseId: 'ai_course_001', // Default course ID
      priority: priority,
      tags: ['education', 'learning']
    };
  }
  // Check for ads analysis
  else if (patterns.ads_analysis.some(pattern => content.includes(pattern))) {
    chatType = 'ads_analysis';
    priority = 'high';
    metadata = {
      priority: priority,
      tags: ['marketing', 'advertising']
    };
  }
  // Check for content generation
  else if (patterns.content_gen.some(pattern => content.includes(pattern))) {
    chatType = 'content_gen';
    priority = 'medium';
    metadata = {
      priority: priority,
      tags: ['content', 'writing']
    };
  }
  // Check for avatar analysis
  else if (patterns.avatar_analysis.some(pattern => content.includes(pattern))) {
    chatType = 'avatar_analysis';
    priority = 'medium';
    metadata = {
      priority: priority,
      tags: ['image', 'analysis']
    };
  }
  // Default to general
  else {
    metadata = {
      priority: priority,
      tags: ['general']
    };
  }

  // Generate appropriate title
  const title = generateChatTitle(message, chatType);

  return {
    type: chatType,
    title: title,
    metadata: metadata
  };
};

export const generateChatTitle = (message, type) => {
  // Clean and truncate message for title
  let title = message.trim();
  
  // Add type-specific prefixes
  const prefixes = {
    course_inquiry: '📚 ',
    ads_analysis: '📈 ',
    content_gen: '✍️ ',
    avatar_analysis: '🖼️ ',
    general: '💬 '
  };

  const prefix = prefixes[type] || prefixes.general;
  
  // Truncate title if too long
  if (title.length > 45) {
    title = title.substring(0, 45) + '...';
  }

  return prefix + title;
};

export const getChatTypeDisplayName = (type) => {
  const displayNames = {
    general: 'Trò chuyện chung',
    course_inquiry: 'Hỏi về khóa học',
    ads_analysis: 'Phân tích quảng cáo',
    content_gen: 'Tạo nội dung',
    avatar_analysis: 'Phân tích ảnh'
  };

  return displayNames[type] || displayNames.general;
};

export const getChatTypeColor = (type) => {
  const colors = {
    general: 'bg-gray-100 text-gray-700',
    course_inquiry: 'bg-blue-100 text-blue-700',
    ads_analysis: 'bg-green-100 text-green-700',
    content_gen: 'bg-purple-100 text-purple-700',
    avatar_analysis: 'bg-pink-100 text-pink-700'
  };

  return colors[type] || colors.general;
};
