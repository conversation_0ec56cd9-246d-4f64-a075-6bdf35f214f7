import React from 'react';
import { X, File, Image, FileText, Download, Eye, Archive, Music, Video } from 'lucide-react';
import ProgressBar from './ProgressBar';
import { formatFileSize, getFileIconName } from './FileValidator';

const FilePreview = ({ fileData, progress, onRemove, onUpload }) => {
  const { id, file, name, size, type, preview } = fileData;
  const isImage = type.startsWith('image/');
  const isUploading = progress !== undefined && progress < 100;
  const isCompleted = progress === 100;

  // Helper function to render file icon
  const renderFileIcon = (fileType, className = 'w-6 h-6 text-gray-500') => {
    const iconName = getFileIconName(fileType);
    const iconProps = { className };

    switch (iconName) {
      case 'Image':
        return <Image {...iconProps} />;
      case 'FileText':
        return <FileText {...iconProps} />;
      case 'Archive':
        return <Archive {...iconProps} />;
      case 'Music':
        return <Music {...iconProps} />;
      case 'Video':
        return <Video {...iconProps} />;
      default:
        return <File {...iconProps} />;
    }
  };

  const handlePreview = () => {
    if (isImage && preview) {
      // Open image in new tab or modal
      const newWindow = window.open();
      newWindow.document.write(`
        <html>
          <head><title>${name}</title></head>
          <body style="margin:0;display:flex;justify-content:center;align-items:center;min-height:100vh;background:#000;">
            <img src="${preview}" style="max-width:100%;max-height:100%;object-fit:contain;" alt="${name}"/>
          </body>
        </html>
      `);
    }
  };

  const handleDownload = () => {
    const url = URL.createObjectURL(file);
    const a = document.createElement('a');
    a.href = url;
    a.download = name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
      {/* File Icon/Preview */}
      <div className="flex-shrink-0">
        {isImage && preview ? (
          <div className="relative">
            <img
              src={preview}
              alt={name}
              className="w-12 h-12 object-cover rounded border cursor-pointer hover:opacity-80 transition-opacity"
              onClick={handlePreview}
            />
            <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-50 rounded cursor-pointer">
              <Eye className="w-4 h-4 text-white" />
            </div>
          </div>
        ) : (
          <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
            {renderFileIcon(type)}
          </div>
        )}
      </div>

      {/* File Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <div className="min-w-0 flex-1">
            <p className="text-sm font-medium text-gray-900 truncate" title={name}>
              {name}
            </p>
            <p className="text-xs text-gray-500">
              {formatFileSize(size)} • {type.split('/')[1]?.toUpperCase() || 'FILE'}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-1 ml-2">
            {isImage && preview && (
              <button
                onClick={handlePreview}
                className="p-1 text-gray-400 hover:text-blue-500 transition-colors"
                title="Xem trước"
              >
                <Eye className="w-4 h-4" />
              </button>
            )}
            
            <button
              onClick={handleDownload}
              className="p-1 text-gray-400 hover:text-green-500 transition-colors"
              title="Tải xuống"
            >
              <Download className="w-4 h-4" />
            </button>

            <button
              onClick={onRemove}
              className="p-1 text-gray-400 hover:text-red-500 transition-colors"
              title="Xóa"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Progress Bar */}
        {isUploading && (
          <div className="mt-2">
            <ProgressBar progress={progress} />
          </div>
        )}

        {/* Status */}
        {isCompleted && (
          <div className="mt-1">
            <span className="text-xs text-green-600 font-medium">
              ✓ Upload hoàn thành
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default FilePreview;
