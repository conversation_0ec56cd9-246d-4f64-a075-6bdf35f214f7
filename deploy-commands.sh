#!/bin/bash

# Commands to run on host.mecode.pro
# Copy and paste these commands one by one in your SSH session

echo "=== IMTA AI Deployment Commands ==="
echo ""
echo "1. Connect to server:"
echo "ssh <EMAIL>"
echo ""
echo "2. Create project directory and clone:"
echo "mkdir -p /home/<USER>/home/<USER>"
echo "git clone https://github.com/dangngocbinh/imta-ai.git ."
echo ""
echo "3. Setup environment files:"
echo "cp server/env.example server/.env"
echo "cp client/env.example client/.env"
echo ""
echo "4. Configure production environment:"
echo "sed -i 's/NODE_ENV=development/NODE_ENV=production/' server/.env"
echo "sed -i 's/MONGODB_URI=.*/MONGODB_URI=mongodb:\/\/imta_user:imta123456@mongodb:27017\/imta-ai/' server/.env"
echo "sed -i 's/REDIS_HOST=.*/REDIS_HOST=redis/' server/.env"
echo "sed -i 's/MINIO_ENDPOINT=.*/MINIO_ENDPOINT=minio/' server/.env"
echo "sed -i 's/CORS_ORIGIN=.*/CORS_ORIGIN=*/' server/.env"
echo "sed -i 's/REACT_APP_API_URL=.*/REACT_APP_API_URL=http:\/\/host.mecode.pro\/api/' client/.env"
echo "sed -i 's/REACT_APP_ENV=.*/REACT_APP_ENV=production/' client/.env"
echo ""
echo "5. Setup permissions and install dependencies:"
echo "chmod +x start.sh deploy-prod.sh check-docker.sh"
echo "npm run install-all"
echo ""
echo "6. Deploy with Docker:"
echo "docker-compose -f docker-compose.prod.yml down --remove-orphans"
echo "docker-compose -f docker-compose.prod.yml up -d --build"
echo ""
echo "7. Check status:"
echo "docker-compose -f docker-compose.prod.yml ps"
echo "curl http://localhost/api/health"
echo ""
echo "=== Access URLs ==="
echo "• Main App: http://host.mecode.pro"
echo "• API Health: http://host.mecode.pro/api/health"
echo "• Mongo Express: http://host.mecode.pro:8081"
echo "• Redis Commander: http://host.mecode.pro:8082"
echo "• MinIO Console: http://host.mecode.pro:9001"
echo ""
echo "=== Management Commands ==="
echo "• View logs: docker-compose -f docker-compose.prod.yml logs -f"
echo "• Restart: docker-compose -f docker-compose.prod.yml restart"
echo "• Stop: docker-compose -f docker-compose.prod.yml down"
echo "• Update: git pull && docker-compose -f docker-compose.prod.yml up -d --build" 