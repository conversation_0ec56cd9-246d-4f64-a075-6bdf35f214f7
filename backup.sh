#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKUP_DIR="/backup"
PROJECT_PATH="/home/<USER>"
RETENTION_DAYS=7
DATE=$(date +%Y%m%d_%H%M%S)

echo -e "${BLUE}🔄 Starting backup process...${NC}"

# Create backup directory
mkdir -p $BACKUP_DIR

# Function to log messages
log_message() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Function to log errors
log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Step 1: MongoDB backup
log_message "📊 Creating MongoDB backup..."
if docker exec imta-mongodb-prod mongodump --out /backup/mongodb_$DATE; then
    log_message "✅ MongoDB backup completed"
else
    log_error "❌ MongoDB backup failed"
    exit 1
fi

# Step 2: Uploads backup
log_message "📁 Creating uploads backup..."
if tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz -C $PROJECT_PATH uploads 2>/dev/null; then
    log_message "✅ Uploads backup completed"
else
    log_message "⚠️  No uploads directory found or empty"
fi

# Step 3: Environment files backup
log_message "⚙️  Creating environment backup..."
if tar -czf $BACKUP_DIR/env_$DATE.tar.gz -C $PROJECT_PATH server/.env client/.env 2>/dev/null; then
    log_message "✅ Environment backup completed"
else
    log_message "⚠️  Environment files not found"
fi

# Step 4: Docker volumes backup
log_message "🐳 Creating Docker volumes backup..."
if docker run --rm -v imta-ai_mongodb_data:/data -v $BACKUP_DIR:/backup alpine tar -czf /backup/mongodb_volume_$DATE.tar.gz -C /data .; then
    log_message "✅ MongoDB volume backup completed"
else
    log_error "❌ MongoDB volume backup failed"
fi

if docker run --rm -v imta-ai_redis_data:/data -v $BACKUP_DIR:/backup alpine tar -czf /backup/redis_volume_$DATE.tar.gz -C /data .; then
    log_message "✅ Redis volume backup completed"
else
    log_error "❌ Redis volume backup failed"
fi

if docker run --rm -v imta-ai_minio_data:/data -v $BACKUP_DIR:/backup alpine tar -czf /backup/minio_volume_$DATE.tar.gz -C /data .; then
    log_message "✅ MinIO volume backup completed"
else
    log_error "❌ MinIO volume backup failed"
fi

# Step 5: Create backup manifest
log_message "📋 Creating backup manifest..."
cat > $BACKUP_DIR/manifest_$DATE.txt << EOF
IMTA AI Backup Manifest
=======================
Date: $(date)
Backup ID: $DATE
Components:
- MongoDB: mongodb_$DATE/
- Uploads: uploads_$DATE.tar.gz
- Environment: env_$DATE.tar.gz
- MongoDB Volume: mongodb_volume_$DATE.tar.gz
- Redis Volume: redis_volume_$DATE.tar.gz
- MinIO Volume: minio_volume_$DATE.tar.gz

System Info:
- Docker Version: $(docker --version)
- Disk Usage: $(df -h $BACKUP_DIR | tail -1)
- Total Backup Size: $(du -sh $BACKUP_DIR/*_$DATE* 2>/dev/null | awk '{sum+=$1} END {print sum "B"}')

Restore Instructions:
1. Stop application: docker-compose -f docker-compose.prod.yml down
2. Restore volumes: docker volume restore commands
3. Restore database: docker exec -it imta-mongodb-prod mongorestore /backup/mongodb_$DATE
4. Restore uploads: tar -xzf uploads_$DATE.tar.gz -C $PROJECT_PATH
5. Start application: docker-compose -f docker-compose.prod.yml up -d
EOF

log_message "✅ Backup manifest created"

# Step 6: Cleanup old backups
log_message "🧹 Cleaning up old backups (older than $RETENTION_DAYS days)..."
find $BACKUP_DIR -name "*_$(date -d "$RETENTION_DAYS days ago" +%Y%m%d)*" -type f -delete 2>/dev/null
find $BACKUP_DIR -name "*_$(date -d "$RETENTION_DAYS days ago" +%Y%m%d)*" -type d -exec rm -rf {} + 2>/dev/null

# Step 7: Show backup summary
log_message "📊 Backup Summary:"
echo -e "${BLUE}Backup Location:${NC} $BACKUP_DIR"
echo -e "${BLUE}Backup Date:${NC} $DATE"
echo -e "${BLUE}Total Size:${NC} $(du -sh $BACKUP_DIR/*_$DATE* 2>/dev/null | awk '{sum+=$1} END {print sum "B"}')"
echo -e "${BLUE}Retention:${NC} $RETENTION_DAYS days"

# Step 8: Optional: Upload to cloud storage
if [ -n "$CLOUD_BACKUP_ENABLED" ] && [ "$CLOUD_BACKUP_ENABLED" = "true" ]; then
    log_message "☁️  Uploading to cloud storage..."
    # Add your cloud storage upload commands here
    # Example for AWS S3:
    # aws s3 sync $BACKUP_DIR s3://your-bucket/imta-ai-backups/$DATE/
fi

log_message "🎉 Backup process completed successfully!"

# Optional: Send notification
if [ -n "$NOTIFICATION_WEBHOOK" ]; then
    curl -X POST -H "Content-Type: application/json" \
         -d "{\"text\":\"✅ IMTA AI backup completed: $DATE\"}" \
         $NOTIFICATION_WEBHOOK
fi 