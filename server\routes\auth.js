const express = require('express');
const router = express.Router();
const { User } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { sendLocalizedSuccess, sendLocalizedError, sendLocalizedValidationError } = require('../utils/i18n');

// POST /api/auth/register - Đăng ký tà<PERSON> k<PERSON>ản
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, fullName, phone } = req.body;

    // Validation
    if (!username || !email || !password || !fullName) {
      return sendLocalizedError(res, 400, 'auth.register.missing_fields');
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email: email.toLowerCase() }, { username }]
    });

    if (existingUser) {
      return sendLocalizedError(res, 400, 'auth.register.user_exists');
    }

    // Create new user
    const user = new User({
      username,
      email,
      password,
      fullName,
      phone
    });

    await user.save();

    // Generate token
    const token = user.generateAuthToken();

    sendLocalizedSuccess(res, 201, 'auth.register.success', {
      user: user.profile,
      token
    });

  } catch (error) {
    console.error('Registration error:', error);
    
    if (error.code === 11000) {
      return sendLocalizedError(res, 400, 'auth.register.user_exists');
    }

    // Handle Mongoose validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = {};
      const errorParams = {};
      
      // Extract specific field errors
      Object.keys(error.errors).forEach(field => {
        const fieldError = error.errors[field];
        switch (field) {
          case 'email':
            if (fieldError.kind === 'regexp') {
              validationErrors.email = 'auth.validation.invalid_email';
            } else {
              validationErrors.email = 'auth.validation.required_field';
            }
            break;
          case 'username':
            if (fieldError.kind === 'minlength') {
              validationErrors.username = 'auth.validation.invalid_username';
            } else if (fieldError.kind === 'maxlength') {
              validationErrors.username = 'auth.validation.invalid_username';
            } else {
              validationErrors.username = 'auth.validation.required_field';
            }
            break;
          case 'password':
            if (fieldError.kind === 'minlength') {
              validationErrors.password = 'auth.validation.invalid_password';
            } else {
              validationErrors.password = 'auth.validation.required_field';
            }
            break;
          case 'fullName':
            if (fieldError.kind === 'maxlength') {
              validationErrors.fullName = 'auth.validation.invalid_fullname';
            } else {
              validationErrors.fullName = 'auth.validation.required_field';
            }
            break;
          case 'phone':
            if (fieldError.kind === 'regexp') {
              validationErrors.phone = 'auth.validation.invalid_phone';
            }
            break;
          default:
            validationErrors[field] = 'auth.validation.required_field';
        }
      });

      return sendLocalizedValidationError(res, 400, 'auth.register.validation_failed', validationErrors, errorParams);
    }

    sendLocalizedError(res, 500, 'auth.register.failed');
  }
});

// POST /api/auth/login - Đăng nhập
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validation
    if (!email || !password) {
      return sendLocalizedError(res, 400, 'auth.login.missing_fields');
    }

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      return sendLocalizedError(res, 401, 'auth.login.invalid_credentials');
    }

    // Check if account is active
    if (!user.isActive()) {
      return sendLocalizedError(res, 401, 'auth.login.account_inactive');
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return sendLocalizedError(res, 401, 'auth.login.invalid_credentials');
    }

    // Update last login
    await user.updateLastLogin();

    // Generate token
    const token = user.generateAuthToken();

    sendLocalizedSuccess(res, 200, 'auth.login.success', {
      user: user.profile,
      token
    });

  } catch (error) {
    console.error('Login error:', error);
    sendLocalizedError(res, 500, 'auth.login.failed');
  }
});

// GET /api/auth/me - Lấy thông tin tài khoản hiện tại
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('-password');
    
    sendLocalizedSuccess(res, 200, 'auth.profile.get_success', {
      user: user.profile
    });

  } catch (error) {
    console.error('Get user info error:', error);
    sendLocalizedError(res, 500, 'auth.profile.get_failed');
  }
});

// PUT /api/auth/me - Cập nhật thông tin tài khoản
router.put('/me', authenticateToken, async (req, res) => {
  try {
    const { fullName, phone, avatar, preferences } = req.body;
    
    const updateData = {};
    if (fullName) updateData.fullName = fullName;
    if (phone) updateData.phone = phone;
    if (avatar) updateData.avatar = avatar;
    if (preferences) updateData.preferences = { ...req.user.preferences, ...preferences };

    const user = await User.findByIdAndUpdate(
      req.user._id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    sendLocalizedSuccess(res, 200, 'auth.profile.update_success', {
      user: user.profile
    });

  } catch (error) {
    console.error('Update profile error:', error);
    sendLocalizedError(res, 500, 'auth.profile.update_failed');
  }
});

// POST /api/auth/change-password - Đổi mật khẩu
router.post('/change-password', authenticateToken, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return sendLocalizedError(res, 400, 'auth.password.missing_fields');
    }

    if (newPassword.length < 6) {
      return sendLocalizedError(res, 400, 'auth.register.password_length');
    }

    const user = await User.findById(req.user._id);
    
    // Verify current password
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return sendLocalizedError(res, 400, 'auth.password.invalid_current');
    }

    // Update password
    user.password = newPassword;
    await user.save();

    sendLocalizedSuccess(res, 200, 'auth.password.change_success');

  } catch (error) {
    console.error('Change password error:', error);
    sendLocalizedError(res, 500, 'auth.password.change_failed');
  }
});

// POST /api/auth/logout - Đăng xuất
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    // In a JWT-based system, logout is typically handled client-side
    // by removing the token. However, we can implement server-side logout
    // by maintaining a blacklist of tokens or updating user's last logout time
    
    // Update user's last logout time (optional)
    await User.findByIdAndUpdate(req.user._id, {
      lastLogoutAt: new Date()
    });

    sendLocalizedSuccess(res, 200, 'auth.logout.success');

  } catch (error) {
    console.error('Logout error:', error);
    sendLocalizedError(res, 500, 'auth.logout.failed');
  }
});

module.exports = router; 