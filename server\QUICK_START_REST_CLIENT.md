# ⚡ Quick Start - REST Client

## 🚀 Bắt đầu nhanh trong 3 bước

### 1. Cài đặt Extension
- Mở VS Code
- Tìm "REST Client" extension của <PERSON>
- Cài đặt và restart VS Code

### 2. Mở file test
```bash
cd server
code test-api.http
```

### 3. <PERSON><PERSON><PERSON> request đầu tiên
- Click "Send Request" phía trên `GET {{baseUrl}}/health`
- Xem response trong tab mới

## 🎯 Các request quan trọng

### Health Check
```http
GET {{baseUrl}}/health
Accept-Language: vi
```

### Đăng ký tài khoản
```http
POST {{baseUrl}}/auth/register
Content-Type: application/json
Accept-Language: vi

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "Test User"
}
```

### Đăng nhập
```http
# @name login
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Sử dụng token
```http
GET {{baseUrl}}/auth/me
Authorization: Bearer {{login.response.body.data.token}}
```

## 💡 Tips nhanh

- **Chạy request**: Click "Send Request" hoặc `Ctrl+Alt+R`
- **Variables**: Sử dụng `{{variable}}` để tái sử dụng
- **Response variables**: `{{login.response.body.data.token}}`
- **Headers**: Thêm `Accept-Language: vi` để test i18n

## 🔧 Troubleshooting

**Server không chạy?**
```bash
npm run dev
```

**Token hết hạn?**
Chạy lại request login

**Lỗi validation?**
Xem `response.errors` để biết lỗi cụ thể

---

📖 **Xem hướng dẫn chi tiết**: [REST_CLIENT_GUIDE.md](REST_CLIENT_GUIDE.md) 