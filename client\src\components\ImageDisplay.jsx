import React, { useState, useRef, useEffect } from 'react';
import { Eye, Download, AlertCircle } from 'lucide-react';
import ImagePreview from './ImagePreview';

const ImageDisplay = ({ 
  src, 
  alt = '', 
  className = '',
  maxWidth = '300px',
  maxHeight = '200px',
  showControls = true,
  lazy = true
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [isInView, setIsInView] = useState(!lazy);
  
  const imageRef = useRef(null);
  const containerRef = useRef(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [lazy]);

  // Handle image load
  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  // Handle image error
  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  // Open preview
  const handlePreview = () => {
    setShowPreview(true);
  };

  // Close preview
  const handleClosePreview = () => {
    setShowPreview(false);
  };

  // Download image
  const handleDownload = async (e) => {
    e.stopPropagation();
    try {
      const response = await fetch(src);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = alt || 'image';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  return (
    <>
      <div 
        ref={containerRef}
        className={`relative group cursor-pointer ${className}`}
        style={{ maxWidth, maxHeight }}
        onClick={handlePreview}
      >
        {/* Loading State */}
        {isInView && isLoading && (
          <div 
            className="flex items-center justify-center bg-gray-200 rounded border"
            style={{ width: maxWidth, height: maxHeight }}
          >
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          </div>
        )}

        {/* Error State */}
        {isInView && hasError && (
          <div 
            className="flex flex-col items-center justify-center bg-gray-100 rounded border text-gray-500"
            style={{ width: maxWidth, height: maxHeight }}
          >
            <AlertCircle className="w-8 h-8 mb-2" />
            <p className="text-xs text-center">Không thể tải hình ảnh</p>
          </div>
        )}

        {/* Lazy Loading Placeholder */}
        {!isInView && (
          <div 
            className="flex items-center justify-center bg-gray-100 rounded border text-gray-400"
            style={{ width: maxWidth, height: maxHeight }}
          >
            <Eye className="w-6 h-6" />
          </div>
        )}

        {/* Image */}
        {isInView && !hasError && (
          <img
            ref={imageRef}
            src={src}
            alt={alt}
            onLoad={handleImageLoad}
            onError={handleImageError}
            className={`
              w-full h-full object-cover rounded border transition-opacity duration-200
              ${isLoading ? 'opacity-0' : 'opacity-100'}
            `}
            style={{ display: isLoading ? 'none' : 'block' }}
          />
        )}

        {/* Overlay Controls */}
        {showControls && isInView && !isLoading && !hasError && (
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded flex items-center justify-center">
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
              <button
                onClick={handlePreview}
                className="p-2 bg-white bg-opacity-90 text-gray-800 rounded-full hover:bg-opacity-100 transition-all"
                title="Xem toàn màn hình"
              >
                <Eye className="w-4 h-4" />
              </button>
              <button
                onClick={handleDownload}
                className="p-2 bg-white bg-opacity-90 text-gray-800 rounded-full hover:bg-opacity-100 transition-all"
                title="Tải xuống"
              >
                <Download className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        {/* Image Info Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-b">
          <p className="text-white text-xs truncate">{alt}</p>
        </div>
      </div>

      {/* Full Screen Preview */}
      {showPreview && (
        <ImagePreview
          src={src}
          alt={alt}
          onClose={handleClosePreview}
          showControls={true}
        />
      )}
    </>
  );
};

export default ImageDisplay;
