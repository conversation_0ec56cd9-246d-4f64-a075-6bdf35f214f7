import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const ProtectedRoute = ({ children, requireAuth = true }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  // Debug logging
  console.log('ProtectedRoute debug:', {
    pathname: location.pathname,
    requireAuth,
    isAuthenticated,
    isLoading
  });

  // Show loading spinner while checking auth state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang kiểm tra trạng thái đăng nhập...</p>
        </div>
      </div>
    );
  }

  // If route requires authentication but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    console.log('Redirecting to login - user not authenticated');
    // Redirect to login page with return url
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If route requires no authentication but user is authenticated (e.g., login page)
  // Only redirect if user is on login/register pages, not other public routes
  if (!requireAuth && isAuthenticated && (location.pathname === '/login' || location.pathname === '/register')) {
    console.log('Redirecting authenticated user away from auth pages');
    // Redirect to chat page or the page they were trying to access
    const from = location.state?.from?.pathname || '/chat';
    return <Navigate to={from} replace />;
  }

  console.log('Rendering component normally');
  // Render the protected component
  return children;
};

export default ProtectedRoute;
