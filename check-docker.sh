#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🐳 Checking Docker containers...${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker is running${NC}"

# Check if required containers are running
containers=("imta-mongodb" "imta-redis" "imta-minio")
missing_containers=()

for container in "${containers[@]}"; do
    if ! docker ps --format "table {{.Names}}" | grep -q "^${container}$"; then
        missing_containers+=("$container")
    fi
done

if [ ${#missing_containers[@]} -eq 0 ]; then
    echo -e "${GREEN}✅ All required containers are running${NC}"
    echo -e "${BLUE}📊 Container status:${NC}"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep imta
else
    echo -e "${YELLOW}⚠️  Missing containers: ${missing_containers[*]}${NC}"
    echo -e "${BLUE}🚀 Starting missing containers...${NC}"
    docker-compose up -d "${missing_containers[@]}"
    
    echo -e "${YELLOW}⏳ Waiting for containers to be ready...${NC}"
    sleep 10
    
    echo -e "${GREEN}✅ Containers started!${NC}"
    echo -e "${BLUE}📊 Container status:${NC}"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep imta
fi

echo -e "${GREEN}🎉 Docker setup complete!${NC}" 