# 🚀 CI/CD Setup Guide for IMTA AI (Ubuntu)

Hướng dẫn thiết lập Continuous Integration/Continuous Deployment (CI/CD) pipeline cho VPS imta.ai sử dụng GitHub Actions trên Ubuntu với Nginx có sẵn.

## 📋 Prerequisites

### 1. VPS Requirements
- **OS**: Ubuntu 20.04+ (khuyến nghị Ubuntu 22.04 LTS)
- **RAM**: Tối thiểu 4GB (khuyến nghị 8GB)
- **Storage**: Tối thiểu 20GB
- **Domain**: imta.ai (đã được cấu hình DNS)
- **Nginx**: Đ<PERSON> được cài đặt và cấu hình

### 2. Software Requirements
- Docker & Docker Compose
- Git
- Nginx (đã có sẵn)
- SSL Certificate (Let's Encrypt hoặc tự cung cấp)

## 🔧 Ubuntu Setup

### 1. Initial Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Install Docker (Ubuntu)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Node.js (for build process)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 2. SSL Certificate Setup

```bash
# Install Certbot (if using Let's Encrypt)
sudo apt install -y certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d imta.ai -d www.imta.ai

# Auto-renewal
sudo crontab -e
# Add this line:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. SSH Key Setup

```bash
# Generate SSH key pair
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Add public key to authorized_keys
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys

# Copy private key for GitHub Secrets
cat ~/.ssh/id_rsa
```

### 4. Quick Setup Script

Sử dụng script tự động cho Ubuntu:

```bash
# Download and run setup script
curl -fsSL https://raw.githubusercontent.com/dangngocbinh/imta-ai/main/ubuntu-setup.sh | bash
```

## 🔐 GitHub Repository Setup

### 1. Repository Secrets

Vào **Settings > Secrets and variables > Actions** và thêm các secrets sau:

```bash
# SSH Configuration
SSH_PRIVATE_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
[Your private key content here]
-----END OPENSSH PRIVATE KEY-----

# Server Configuration
SERVER_HOST=imta.ai
SERVER_USER=root
PROJECT_PATH=/home/<USER>
REPO_URL=https://github.com/dangngocbinh/imta-ai.git

# Optional: Environment Variables
NODE_ENV=production
MONGODB_URI=****************************************************
REDIS_HOST=redis
MINIO_ENDPOINT=minio
LANGFLOW_API_URL=https://langflow.mecode.pro/api/v1/run/your-flow-id
LANGFLOW_API_KEY=your-api-key-here
```

### 2. Repository Variables

Thêm các variables sau:

```bash
# Build Configuration
NODE_VERSION=18
DOCKER_REGISTRY=ghcr.io
IMAGE_NAME=imta-ai
```

## 🚀 Deployment Process

### 1. Manual Deployment

```bash
# Clone repository
git clone https://github.com/dangngocbinh/imta-ai.git
cd imta-ai

# Setup environment
cp server/env.example server/.env
cp client/env.example client/.env

# Configure production environment
sed -i 's/NODE_ENV=development/NODE_ENV=production/' server/.env
sed -i 's/MONGODB_URI=.*/MONGODB_URI=mongodb:\/\/imta_user:imta123456@mongodb:27017\/imta-ai/' server/.env
sed -i 's/REDIS_HOST=.*/REDIS_HOST=redis/' server/.env
sed -i 's/MINIO_ENDPOINT=.*/MINIO_ENDPOINT=minio/' server/.env
sed -i 's/CORS_ORIGIN=.*/CORS_ORIGIN=*/' server/.env

# Update client environment
sed -i 's/REACT_APP_API_URL=.*/REACT_APP_API_URL=https:\/\/imta.ai\/api/' client/.env
sed -i 's/REACT_APP_ENV=.*/REACT_APP_ENV=production/' client/.env

# Setup Nginx configuration
sudo cp nginx-ubuntu.conf /etc/nginx/sites-available/imta-ai
sudo ln -sf /etc/nginx/sites-available/imta-ai /etc/nginx/sites-enabled/imta-ai
sudo nginx -t
sudo systemctl reload nginx

# Install dependencies
npm run install-all

# Deploy with Docker
docker-compose -f docker-compose.prod.yml up -d --build
```

### 2. Automated Deployment

Khi push code lên branch `main`, GitHub Actions sẽ tự động:

1. **Test Stage**:
   - Install dependencies
   - Run tests
   - Build client

2. **Deploy Stage**:
   - Connect to Ubuntu VPS via SSH
   - Pull latest code
   - Update environment configuration
   - Update Nginx configuration
   - Build and deploy Docker containers
   - Reload Nginx
   - Verify deployment

## 📊 Monitoring & Maintenance

### 1. Health Checks

```bash
# Check application health
curl -f https://imta.ai/api/health

# Check container status
docker-compose -f docker-compose.prod.yml ps

# Check logs
docker-compose -f docker-compose.prod.yml logs -f

# Check Nginx status
sudo systemctl status nginx
sudo nginx -t
```

### 2. Backup Strategy

```bash
# Database backup
docker exec imta-mongodb-prod mongodump --out /backup/$(date +%Y%m%d)

# File backup
tar -czf /backup/uploads-$(date +%Y%m%d).tar.gz /home/<USER>/uploads

# Automated backup script
chmod +x backup.sh
./backup.sh

# Setup cron job for daily backup
(crontab -l 2>/dev/null; echo "0 2 * * * /home/<USER>/backup.sh") | crontab -
```

### 3. Log Management

```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs -f app

# View Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Log rotation (already configured in setup)
sudo logrotate /etc/logrotate.d/imta-ai
```

## 🔧 Troubleshooting

### Common Issues

1. **SSL Certificate Issues**
   ```bash
   # Renew certificate
   sudo certbot renew --force-renewal
   
   # Check certificate status
   sudo certbot certificates
   ```

2. **Docker Build Failures**
   ```bash
   # Clean up Docker
   docker system prune -a
   docker volume prune
   
   # Rebuild without cache
   docker-compose -f docker-compose.prod.yml build --no-cache
   ```

3. **Database Connection Issues**
   ```bash
   # Check MongoDB status
   docker exec imta-mongodb-prod mongosh --eval "db.adminCommand('ping')"
   
   # Check Redis status
   docker exec imta-redis-prod redis-cli ping
   ```

4. **Nginx Issues**
   ```bash
   # Test Nginx configuration
   sudo nginx -t
   
   # Check Nginx status
   sudo systemctl status nginx
   
   # Reload Nginx
   sudo systemctl reload nginx
   
   # Restart Nginx
   sudo systemctl restart nginx
   ```

5. **Firewall Issues**
   ```bash
   # Check firewall status
   sudo ufw status
   
   # Add required ports
   sudo ufw allow 5001/tcp
   sudo ufw reload
   ```

6. **Permission Issues**
   ```bash
   # Fix Docker permissions
   sudo usermod -aG docker $USER
   newgrp docker
   
   # Fix file permissions
   sudo chown -R $USER:$USER /home/<USER>
   ```

## 📈 Performance Optimization

### 1. Nginx Optimization

```nginx
# Add to /etc/nginx/nginx.conf
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 65535;
    use epoll;
    multi_accept on;
}

http {
    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript;
    
    # Cache static files
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 2. Docker Optimization

```yaml
# Add to docker-compose.prod.yml
services:
  app:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

### 3. System Optimization

```bash
# Increase file descriptors
echo "* soft nofile 65535" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65535" | sudo tee -a /etc/security/limits.conf

# Optimize kernel parameters
echo "net.core.somaxconn = 65535" | sudo tee -a /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## 🎯 Success Metrics

- ✅ Zero-downtime deployments
- ✅ Automated testing before deployment
- ✅ Health checks after deployment
- ✅ SSL certificate auto-renewal
- ✅ Automated backups
- ✅ Monitoring and alerting
- ✅ Ubuntu compatibility
- ✅ Existing Nginx integration

## 📞 Support

Nếu gặp vấn đề, hãy kiểm tra:

1. **GitHub Actions logs**: Repository > Actions > Workflow runs
2. **Server logs**: `docker-compose -f docker-compose.prod.yml logs`
3. **Nginx logs**: `/var/log/nginx/access.log` và `/var/log/nginx/error.log`
4. **Health endpoint**: https://imta.ai/api/health
5. **SSL status**: https://www.ssllabs.com/ssltest/analyze.html?d=imta.ai

## 🔄 Update Process

Để cập nhật ứng dụng:

```bash
# Manual update
cd /home/<USER>
git pull origin main
docker-compose -f docker-compose.prod.yml up -d --build
sudo systemctl reload nginx

# Or use systemd service
sudo systemctl restart imta-ai
```

---

**🎉 Chúc mừng! Bạn đã thiết lập thành công CI/CD pipeline cho IMTA AI trên Ubuntu!** 