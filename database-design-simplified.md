# 🗄️ THIẾT KẾ DATABASE ĐƠN GIẢN - IMTA AI

## 📊 COLLECTIONS (4 collections chính)

### 1. **users** - Qu<PERSON><PERSON> lý học viên
### 2. **credits** - Quản lý credit/token  
### 3. **chats** - <PERSON><PERSON><PERSON> chat
### 4. **messages** - <PERSON> (bao gồm tất cả phân tích)

---

## 📋 CHI TIẾT COLLECTIONS

### 1. **users** - <PERSON><PERSON><PERSON> viên
```javascript
{
  _id: ObjectId,
  username: String,           // Tên đăng nhập
  email: String,              // Email
  fullName: String,           // Họ tên đầy đủ
  phone: String,              // Số điện thoại
  avatar: String,             // URL avatar
  role: String,               // "student", "admin", "teacher"
  status: String,             // "active", "inactive", "suspended"
  registrationDate: Date,     // Ngày đăng ký
  lastLogin: Date,            // Lần đăng nhập cuối
  preferences: {
    language: String,         // "vi", "en"
    theme: String,            // "light", "dark"
    notifications: Boolean
  },
  metadata: {
    source: String,           // Nguồn đăng ký
    referrer: String,         // Người giới thiệu
    tags: [String]            // Tags phân loại
  }
}
```

### 2. **credits** - Quản lý credit
```javascript
{
  _id: ObjectId,
  userId: ObjectId,           // Reference to users
  balance: Number,            // Số credit hiện tại
  totalEarned: Number,        // Tổng credit đã nhận
  totalSpent: Number,         // Tổng credit đã sử dụng
  transactions: [{
    type: String,             // "earn", "spend", "refund"
    amount: Number,           // Số lượng
    description: String,      // Mô tả
    service: String,          // "chat", "ads_analysis", "content_gen", "avatar_analysis"
    timestamp: Date,
    metadata: Object          // Thông tin bổ sung
  }],
  lastUpdated: Date
}
```

### 3. **chats** - Phiên chat
```javascript
{
  _id: ObjectId,
  userId: ObjectId,           // Reference to users
  title: String,              // Tiêu đề chat
  type: String,               // "general", "course_inquiry", "ads_analysis", "content_gen", "avatar_analysis"
  status: String,             // "active", "completed", "archived"
  createdAt: Date,
  updatedAt: Date,
  lastMessageAt: Date,
  metadata: {
    courseId: ObjectId,       // Nếu liên quan đến khóa học
    policyId: ObjectId,       // Nếu liên quan đến chính sách
    tags: [String],           // Tags phân loại
    priority: String          // "low", "medium", "high"
  },
  analytics: {
    messageCount: Number,     // Số tin nhắn
    duration: Number,         // Thời gian chat (phút)
    creditUsed: Number        // Credit đã sử dụng
  }
}
```

### 4. **messages** - Tin nhắn (tích hợp tất cả)
```javascript
{
  _id: ObjectId,
  chatId: ObjectId,           // Reference to chats
  sender: String,             // "user", "bot"
  content: String,            // Nội dung tin nhắn
  contentType: String,        // "text", "image", "file", "chart", "analysis"
  timestamp: Date,
  metadata: {
    // Cho file upload
    attachments: [{
      type: String,           // "image", "pdf", "excel", "csv"
      url: String,            // URL file
      filename: String,       // Tên file
      size: Number            // Kích thước (bytes)
    }],
    
    // Cho phân tích ads
    adsAnalysis: {
      summary: String,        // Tóm tắt phân tích
      insights: [String],     // Các insight chính
      recommendations: [String], // Khuyến nghị
      charts: [{
        type: String,         // "line", "bar", "pie", "scatter"
        title: String,
        data: Object,         // Dữ liệu chart
        imageUrl: String      // URL hình ảnh chart
      }],
      metrics: {
        totalSpend: Number,
        impressions: Number,
        clicks: Number,
        conversions: Number,
        roas: Number
      }
    },
    
    // Cho bài quảng cáo
    contentGeneration: {
      title: String,          // Tiêu đề
      body: String,           // Nội dung chính
      hashtags: [String],     // Hashtags
      callToAction: String,   // CTA
      platform: String,       // "facebook", "instagram", "tiktok", "google"
      tone: String,           // "professional", "casual", "friendly"
      length: String          // "short", "medium", "long"
    },
    
    // Cho phân tích avatar
    avatarAnalysis: {
      demographics: {
        age: String,          // "18-25", "26-35", etc.
        gender: String,       // "male", "female", "other"
        ethnicity: String
      },
      personality: {
        traits: [String],     // ["extroverted", "creative", "professional"]
        style: String,        // "casual", "business", "creative"
        mood: String          // "friendly", "serious", "energetic"
      },
      recommendations: [String], // Khuyến nghị về avatar
      score: Number           // Điểm đánh giá (1-10)
    },
    
    // Thông tin AI
    aiResponse: {
      model: String,          // Model AI sử dụng
      confidence: Number,     // Độ tin cậy
      processingTime: Number  // Thời gian xử lý (ms)
    },
    
    creditCost: Number        // Credit tiêu tốn
  }
}
```

---

## 🔗 RELATIONSHIPS & INDEXES

### **Indexes cần thiết:**
```javascript
// users
db.users.createIndex({ "email": 1 }, { unique: true })
db.users.createIndex({ "username": 1 }, { unique: true })
db.users.createIndex({ "status": 1 })

// credits
db.credits.createIndex({ "userId": 1 }, { unique: true })
db.credits.createIndex({ "balance": 1 })

// chats
db.chats.createIndex({ "userId": 1 })
db.chats.createIndex({ "type": 1 })
db.chats.createIndex({ "status": 1 })
db.chats.createIndex({ "createdAt": -1 })

// messages
db.messages.createIndex({ "chatId": 1 })
db.messages.createIndex({ "timestamp": -1 })
db.messages.createIndex({ "sender": 1 })
db.messages.createIndex({ "contentType": 1 })
```

---

## 💡 IMPLEMENTATION NOTES

### **Credit System:**
- Chat cơ bản: 1 credit/message
- Phân tích ads: 5 credits/analysis
- Tạo content: 3 credits/content
- Phân tích avatar: 2 credits/analysis

### **Message Types:**
- `text`: Tin nhắn thường
- `image`: Hình ảnh
- `file`: File upload
- `chart`: Biểu đồ
- `analysis`: Kết quả phân tích

### **File Storage:**
- Sử dụng cloud storage (AWS S3, Google Cloud Storage)
- Lưu metadata trong MongoDB, file thực tế trong cloud

### **Real-time Features:**
- WebSocket cho chat real-time
- Server-sent events cho progress updates 