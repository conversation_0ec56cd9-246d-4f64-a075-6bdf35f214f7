#!/bin/bash

echo "🔄 Syncing assets from Docker container to CloudPanel..."

# Wait for container to be ready
echo "⏳ Waiting for container to be ready..."
sleep 10

# Check if container is running
if ! docker ps | grep -q imta-app-prod; then
    echo "❌ Container imta-app-prod is not running!"
    exit 1
fi

# Create directory if not exists
mkdir -p /home/<USER>/htdocs/imta.ai

# Copy assets from container
echo "📁 Copying dist files from container..."
docker cp imta-app-prod:/app/dist/. /home/<USER>/htdocs/imta.ai/

# Set proper permissions
echo "🔧 Setting proper permissions..."
chown -R imta:imta /home/<USER>/htdocs/imta.ai/
chmod -R 755 /home/<USER>/htdocs/imta.ai/

# Verify files
echo "✅ Verification:"
ls -la /home/<USER>/htdocs/imta.ai/
echo ""
ls -la /home/<USER>/htdocs/imta.ai/assets/ | head -5

echo "🎉 Assets sync completed!"

