<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>Test Chat API Integration</h1>
    
    <div>
        <h2>Authentication Status</h2>
        <div id="auth-status"></div>
    </div>
    
    <div>
        <h2>Test Create Chat</h2>
        <button onclick="testCreateChat()">Test Create Chat API</button>
        <div id="create-result"></div>
    </div>
    
    <div>
        <h2>Console Logs</h2>
        <div id="logs" style="background: #f0f0f0; padding: 10px; height: 300px; overflow-y: scroll;"></div>
    </div>

    <script>
        // Override console.log to also display in the page
        const originalLog = console.log;
        const originalError = console.error;
        
        function addLog(type, ...args) {
            const logs = document.getElementById('logs');
            const div = document.createElement('div');
            div.textContent = `[${type}] ${args.join(' ')}`;
            div.style.color = type === 'ERROR' ? 'red' : 'black';
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog('LOG', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog('ERROR', ...args);
        };
        
        // Check authentication status
        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const userData = localStorage.getItem('userData');
            
            const authStatus = document.getElementById('auth-status');
            if (token && userData) {
                authStatus.innerHTML = `<p style="color: green;">✅ Authenticated</p><p>Token: ${token.substring(0, 20)}...</p>`;
            } else {
                authStatus.innerHTML = `<p style="color: red;">❌ Not authenticated</p><p>Please login first</p>`;
            }
        }
        
        // Test create chat API
        async function testCreateChat() {
            console.log('🧪 Testing chat creation...');
            
            const API_BASE_URL = 'http://localhost:5001/api';
            const token = localStorage.getItem('authToken');
            
            if (!token) {
                console.error('❌ No auth token found');
                document.getElementById('create-result').innerHTML = '<p style="color: red;">❌ Please login first</p>';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/chat/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        title: '📚 Test chat about AI course',
                        type: 'course_inquiry',
                        metadata: {
                            courseId: 'ai_course_001',
                            priority: 'high',
                            tags: ['education', 'learning']
                        }
                    })
                });
                
                const data = await response.json();
                console.log('✅ Create chat response:', data);
                
                if (response.ok) {
                    document.getElementById('create-result').innerHTML = `<p style="color: green;">✅ Chat created successfully!</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    document.getElementById('create-result').innerHTML = `<p style="color: red;">❌ Error: ${data.message || 'Unknown error'}</p>`;
                }
            } catch (error) {
                console.error('❌ Test failed:', error);
                document.getElementById('create-result').innerHTML = `<p style="color: red;">❌ Network error: ${error.message}</p>`;
            }
        }
        
        // Initialize
        checkAuthStatus();
        console.log('🧪 API Test page loaded');
    </script>
</body>
</html>
