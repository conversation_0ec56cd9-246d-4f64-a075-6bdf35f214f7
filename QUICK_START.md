# 🚀 Quick Start - IMTA AI Project

## Clone và chạy ngay (1 command line)

```bash
git clone <repository-url> && cd imta-ai && npm start
```

## Hoặc từng bước:

### 1. Clone repository
```bash
git clone <repository-url>
cd imta-ai
```

### 2. <PERSON><PERSON><PERSON> ngay (tự động setup tất cả)
```bash
npm start
```

## ✅ Command `npm start` sẽ tự động:

1. **Ki<PERSON>m tra Docker** - Đảm bảo Docker đang chạy
2. **Khởi động containers** - MongoDB, Redis, MinIO
3. **Cài đặt dependencies** - Nếu chưa có
4. **Khởi động ứng dụng** - Server + Client

## 🎯 Kết quả sau khi chạy:

- **Frontend**: http://localhost:5173
- **Backend**: http://localhost:5001
- **MongoDB**: localhost:27017
- **Redis**: localhost:6379
- **MinIO**: localhost:9000
- **Mongo Express**: http://localhost:8081
- **Redis Commander**: http://localhost:8082

## 📋 Prerequisites:

- Node.js (v14+)
- Docker Desktop
- Git

## 🐛 Nếu gặp lỗi:

### Docker không chạy:
```bash
# Khởi động Docker Desktop trước
# Sau đó chạy:
npm start
```

### Port đã được sử dụng:
```bash
# Dừng tất cả processes
pkill -f "node.*app.js" && pkill -f "react-scripts"
# Sau đó chạy:
npm start
```

### Chỉ muốn kiểm tra Docker:
```bash
./check-docker.sh
```

## 💡 Tips:

- **Lần đầu**: Dùng `npm start` (setup đầy đủ)
- **Sau đó**: Có thể dùng `npm run dev` (nhanh hơn)
- **Development**: Dùng `npm run dev` (auto-restart)
- **Production**: Dùng `npm run start-server`

## 🔄 Các lệnh hữu ích:

```bash
# Khởi động đầy đủ (recommended)
npm start

# Chỉ chạy app (nhanh hơn)
npm run dev

# Chỉ kiểm tra Docker
./check-docker.sh

# Dừng tất cả
pkill -f "node.*app.js" && pkill -f "react-scripts"

# Reset Docker
docker-compose down -v && docker-compose up -d
``` 