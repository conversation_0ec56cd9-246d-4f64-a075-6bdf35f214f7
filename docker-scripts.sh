#!/bin/bash

# Docker management scripts for Imta AI
# Usage: ./docker-scripts.sh [command]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Start all services
start() {
    print_header "Starting Imta AI Services"
    check_docker
    
    print_status "Starting MongoDB, <PERSON><PERSON>, <PERSON><PERSON>, and management UIs..."
    docker-compose up -d
    
    print_status "Waiting for services to be ready..."
    sleep 10
    
    print_status "Services started successfully!"
    print_status "MongoDB: http://localhost:27017"
    print_status "MongoDB Express UI: http://localhost:8081 (admin/imta123456)"
    print_status "Redis: localhost:6379"
    print_status "Redis Commander UI: http://localhost:8082"
    print_status "MinIO: http://localhost:9000"
    print_status "MinIO Console: http://localhost:9001 (minioadmin/imta123456)"
    print_status "Nginx: http://localhost:80"
}

# Stop all services
stop() {
    print_header "Stopping Imta AI Services"
    docker-compose down
    print_status "All services stopped."
}

# Restart all services
restart() {
    print_header "Restarting Imta AI Services"
    stop
    sleep 2
    start
}

# Show service status
status() {
    print_header "Service Status"
    docker-compose ps
    
    echo ""
    print_status "Service URLs:"
    echo "  MongoDB: localhost:27017"
    echo "  MongoDB Express: http://localhost:8081"
    echo "  Redis: localhost:6379"
    echo "  Redis Commander: http://localhost:8082"
    echo "  MinIO: http://localhost:9000"
    echo "  MinIO Console: http://localhost:9001"
    echo "  Nginx: http://localhost:80"
}

# Show logs
logs() {
    if [ -z "$2" ]; then
        print_header "All Services Logs"
        docker-compose logs -f
    else
        print_header "Logs for $2"
        docker-compose logs -f "$2"
    fi
}

# Clean up (remove containers, volumes, networks)
clean() {
    print_header "Cleaning Up Docker Resources"
    print_warning "This will remove all containers, volumes, and networks!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down -v --remove-orphans
        docker system prune -f
        print_status "Cleanup completed."
    else
        print_status "Cleanup cancelled."
    fi
}

# Initialize database
init_db() {
    print_header "Initializing Database"
    print_status "Waiting for MongoDB to be ready..."
    sleep 15
    
    print_status "Database should be initialized automatically."
    print_status "Check MongoDB Express UI: http://localhost:8081"
    print_status "Login: admin/imta123456"
}

# Health check
health() {
    print_header "Health Check"
    
    # Check MongoDB
    if docker-compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
        print_status "MongoDB: ✅ Healthy"
    else
        print_error "MongoDB: ❌ Unhealthy"
    fi
    
    # Check Redis
    if docker-compose exec -T redis redis-cli -a imta123456 ping > /dev/null 2>&1; then
        print_status "Redis: ✅ Healthy"
    else
        print_error "Redis: ❌ Unhealthy"
    fi
    
    # Check MinIO
    if curl -f http://localhost:9000/minio/health/live > /dev/null 2>&1; then
        print_status "MinIO: ✅ Healthy"
    else
        print_error "MinIO: ❌ Unhealthy"
    fi
}

# Backup database
backup() {
    print_header "Backing Up Database"
    BACKUP_DIR="./backups"
    BACKUP_FILE="imta-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    mkdir -p "$BACKUP_DIR"
    
    print_status "Creating backup: $BACKUP_FILE"
    docker-compose exec -T mongodb mongodump --out /tmp/backup
    docker cp imta-mongodb:/tmp/backup "$BACKUP_DIR"
    tar -czf "$BACKUP_DIR/$BACKUP_FILE" -C "$BACKUP_DIR" backup
    rm -rf "$BACKUP_DIR/backup"
    
    print_status "Backup created: $BACKUP_DIR/$BACKUP_FILE"
}

# Restore database
restore() {
    if [ -z "$1" ]; then
        print_error "Please specify backup file: ./docker-scripts.sh restore <backup-file>"
        exit 1
    fi
    
    print_header "Restoring Database"
    BACKUP_FILE="$1"
    
    if [ ! -f "$BACKUP_FILE" ]; then
        print_error "Backup file not found: $BACKUP_FILE"
        exit 1
    fi
    
    print_warning "This will overwrite the current database!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Restoring from: $BACKUP_FILE"
        tar -xzf "$BACKUP_FILE"
        docker cp backup imta-mongodb:/tmp/restore
        docker-compose exec -T mongodb mongorestore /tmp/restore
        rm -rf backup
        print_status "Database restored successfully."
    else
        print_status "Restore cancelled."
    fi
}

# Show help
help() {
    print_header "Docker Management Scripts"
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start     - Start all services"
    echo "  stop      - Stop all services"
    echo "  restart   - Restart all services"
    echo "  status    - Show service status"
    echo "  logs      - Show logs (all or specific service)"
    echo "  health    - Check service health"
    echo "  init-db   - Initialize database"
    echo "  backup    - Create database backup"
    echo "  restore   - Restore database from backup"
    echo "  clean     - Clean up Docker resources"
    echo "  help      - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs mongodb"
    echo "  $0 backup"
    echo "  $0 restore ./backups/imta-backup-20241201-120000.tar.gz"
}

# Main script logic
case "${1:-help}" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs "$@"
        ;;
    health)
        health
        ;;
    init-db)
        init_db
        ;;
    backup)
        backup
        ;;
    restore)
        restore "$2"
        ;;
    clean)
        clean
        ;;
    help|--help|-h)
        help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        help
        exit 1
        ;;
esac 