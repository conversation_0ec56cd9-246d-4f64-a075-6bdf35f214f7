# Server Configuration
PORT=5001
NODE_ENV=development
BASE_URL=http://localhost:5001

# MongoDB Configuration
MONGODB_URI=******************************************************
# For Docker: MONGODB_URI=****************************************************

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_PASSWORD=imta123456
# For Docker: REDIS_HOST=redis

# MinIO Configuration (S3 compatible)
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=imta123456
MINIO_USE_SSL=false
MINIO_BUCKET=imta-ai
# For Docker: MINIO_ENDPOINT=minio

# Langflow API Configuration
LANGFLOW_API_URL=https://langflow.mecode.pro/api/v1/run/your-flow-id
LANGFLOW_API_KEY=your-api-key-here

# JWT Configuration (for authentication)
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=7d

# Sepay.vn Payment Gateway Configuration
SEPAY_API_URL=https://api.sepay.vn/v1/payment/create
SEPAY_MERCHANT_ID=your-merchant-id
SEPAY_SECRET_KEY=your-secret-key

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Credit System Configuration
DEFAULT_CREDITS=100
CHAT_CREDIT_COST=1
ADS_ANALYSIS_CREDIT_COST=5
CONTENT_GENERATION_CREDIT_COST=3
AVATAR_ANALYSIS_CREDIT_COST=2

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# Session Configuration
SESSION_SECRET=your-session-secret-here
SESSION_MAX_AGE=86400000

# Logging Configuration
LOG_LEVEL=info 