#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Deploying IMTA AI to host.mecode.pro...${NC}"

# Server configuration
SERVER_HOST="host.mecode.pro"
SERVER_USER="root"
PROJECT_PATH="/home/<USER>"
REPO_URL="https://github.com/dangngocbinh/imta-ai.git"

# Function to execute command on remote server
execute_remote() {
    local command="$1"
    echo -e "${YELLOW}Executing: $command${NC}"
    ssh $SERVER_USER@$SERVER_HOST "$command"
}

# Function to copy file to remote server
copy_to_server() {
    local local_file="$1"
    local remote_path="$2"
    echo -e "${YELLOW}Copying $local_file to $remote_path${NC}"
    scp "$local_file" "$SERVER_USER@$SERVER_HOST:$remote_path"
}

# Step 1: Connect and prepare server
echo -e "${BLUE}📋 Step 1: Preparing server...${NC}"

# Create project directory and clone repository
execute_remote "mkdir -p $PROJECT_PATH && cd $PROJECT_PATH && git clone $REPO_URL . 2>/dev/null || (git pull)"

# Step 2: Setup environment files
echo -e "${BLUE}📋 Step 2: Setting up environment...${NC}"

# Create environment files
execute_remote "cd $PROJECT_PATH && cp server/env.example server/.env"
execute_remote "cd $PROJECT_PATH && cp client/env.example client/.env"

# Step 3: Configure production environment
echo -e "${BLUE}📋 Step 3: Configuring production environment...${NC}"

# Update server environment
execute_remote "cd $PROJECT_PATH && sed -i 's/NODE_ENV=development/NODE_ENV=production/' server/.env"
execute_remote "cd $PROJECT_PATH && sed -i 's/MONGODB_URI=.*/MONGODB_URI=mongodb:\/\/imta_user:imta123456@mongodb:27017\/imta-ai/' server/.env"
execute_remote "cd $PROJECT_PATH && sed -i 's/REDIS_HOST=.*/REDIS_HOST=redis/' server/.env"
execute_remote "cd $PROJECT_PATH && sed -i 's/MINIO_ENDPOINT=.*/MINIO_ENDPOINT=minio/' server/.env"
execute_remote "cd $PROJECT_PATH && sed -i 's/CORS_ORIGIN=.*/CORS_ORIGIN=*/' server/.env"

# Update client environment
execute_remote "cd $PROJECT_PATH && sed -i 's/REACT_APP_API_URL=.*/REACT_APP_API_URL=http:\/\/host.mecode.pro\/api/' client/.env"
execute_remote "cd $PROJECT_PATH && sed -i 's/REACT_APP_ENV=.*/REACT_APP_ENV=production/' client/.env"

# Step 4: Setup permissions and dependencies
echo -e "${BLUE}📋 Step 4: Setting up permissions and dependencies...${NC}"

execute_remote "cd $PROJECT_PATH && chmod +x start.sh deploy-prod.sh check-docker.sh"
execute_remote "cd $PROJECT_PATH && npm run install-all"

# Step 5: Deploy with Docker
echo -e "${BLUE}📋 Step 5: Deploying with Docker...${NC}"

# Stop any existing containers
execute_remote "cd $PROJECT_PATH && docker-compose -f docker-compose.prod.yml down --remove-orphans 2>/dev/null || true"

# Build and start production
execute_remote "cd $PROJECT_PATH && docker-compose -f docker-compose.prod.yml up -d --build"

# Step 6: Verify deployment
echo -e "${BLUE}📋 Step 6: Verifying deployment...${NC}"

# Wait for services to start
sleep 15

# Check container status
execute_remote "cd $PROJECT_PATH && docker-compose -f docker-compose.prod.yml ps"

# Test health endpoint
echo -e "${BLUE}📋 Testing health endpoint...${NC}"
execute_remote "curl -f http://localhost/api/health || echo 'Health check failed'"

# Step 7: Show access information
echo -e "${GREEN}🎉 Deployment completed!${NC}"
echo -e "${BLUE}🌐 Access URLs:${NC}"
echo -e "${GREEN}  • Main Application: http://host.mecode.pro${NC}"
echo -e "${GREEN}  • API Health Check: http://host.mecode.pro/api/health${NC}"
echo -e "${GREEN}  • Mongo Express: http://host.mecode.pro:8081${NC}"
echo -e "${GREEN}  • Redis Commander: http://host.mecode.pro:8082${NC}"
echo -e "${GREEN}  • MinIO Console: http://host.mecode.pro:9001${NC}"

echo -e "${BLUE}💡 Management commands:${NC}"
echo -e "${YELLOW}  • View logs: ssh $SERVER_USER@$SERVER_HOST 'cd $PROJECT_PATH && docker-compose -f docker-compose.prod.yml logs -f'${NC}"
echo -e "${YELLOW}  • Restart: ssh $SERVER_USER@$SERVER_HOST 'cd $PROJECT_PATH && docker-compose -f docker-compose.prod.yml restart'${NC}"
echo -e "${YELLOW}  • Stop: ssh $SERVER_USER@$SERVER_HOST 'cd $PROJECT_PATH && docker-compose -f docker-compose.prod.yml down'${NC}" 