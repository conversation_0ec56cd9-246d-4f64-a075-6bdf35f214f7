import React, { useState, useEffect } from 'react';

// Simple version without complex animation for debugging
const SimpleStreamingText = ({ text, isStreaming = true }) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    console.log('🔍 SimpleStreamingText: text changed', { 
      text: text?.substring(0, 50),
      textLength: text?.length,
      currentIndex,
      isStreaming 
    });
    
    if (!text) return;
    
    // Reset for new text
    if (text.length < currentIndex) {
      setCurrentIndex(0);
      setDisplayText('');
    }
    
    // Simple character by character animation
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        const newIndex = currentIndex + 1;
        const newText = text.slice(0, newIndex);
        console.log('📝 SimpleStreamingText: updating', { newIndex, newText: newText.slice(-10) });
        setDisplayText(newText);
        setCurrentIndex(newIndex);
      }, 100); // 100ms per character
      
      return () => clearTimeout(timer);
    }
  }, [text, currentIndex, isStreaming]);

  console.log('🎭 SimpleStreamingText render:', { displayText: displayText.slice(0, 50), textLength: text?.length });

  return (
    <div style={{ border: '2px solid red', padding: '10px', margin: '10px' }}>
      <div><strong>SimpleStreamingText Debug:</strong></div>
      <div>Input text: {text?.substring(0, 50)}...</div>
      <div>Display text: {displayText}</div>
      <div>Progress: {currentIndex}/{text?.length || 0}</div>
      <div style={{ marginTop: '10px', minHeight: '50px', background: '#f0f0f0', padding: '10px' }}>
        {displayText}
        {isStreaming && currentIndex < (text?.length || 0) && (
          <span style={{ animation: 'blink 1s infinite', color: 'blue' }}>▋</span>
        )}
      </div>
    </div>
  );
};

export default SimpleStreamingText;
