/**
 * Streaming Simulator Utility
 * Simulates real-time API streaming for bot responses
 */

export class StreamingSimulator {
  constructor(options = {}) {
    this.speed = options.speed || 50; // characters per second
    this.chunkSize = options.chunkSize || 1;
    this.delay = options.delay || 200;
    this.mode = options.mode || 'character'; // 'character' or 'word'
    this.onChunk = options.onChunk || null;
    this.onComplete = options.onComplete || null;
    this.onStart = options.onStart || null;
    
    this.isActive = false;
    this.isPaused = false;
    this.currentIndex = 0;
    this.chunks = [];
    this.timeoutId = null;
  }

  /**
   * Start streaming simulation
   * @param {string} text - Text to stream
   * @returns {Promise} - Resolves when streaming is complete
   */
  async startStreaming(text) {
    return new Promise((resolve, reject) => {
      try {
        this.prepareChunks(text);
        this.isActive = true;
        this.currentIndex = 0;
        
        this.onStart?.(text);
        
        // Start with initial delay
        this.timeoutId = setTimeout(() => {
          this.processNextChunk(resolve);
        }, this.delay);
        
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Prepare text chunks based on streaming mode
   * @param {string} text - Text to chunk
   */
  prepareChunks(text) {
    if (!text) {
      this.chunks = [];
      return;
    }

    if (this.mode === 'word') {
      // Split by words but preserve spaces
      this.chunks = text.split(/(\s+)/);
    } else {
      // Character by character
      this.chunks = text.split('');
    }
  }

  /**
   * Process next chunk in the streaming sequence
   * @param {Function} resolve - Promise resolve function
   */
  processNextChunk(resolve) {
    if (!this.isActive || this.isPaused) {
      return;
    }

    if (this.currentIndex < this.chunks.length) {
      const nextIndex = Math.min(this.currentIndex + this.chunkSize, this.chunks.length);
      const currentText = this.chunks.slice(0, nextIndex).join('');
      const progress = nextIndex / this.chunks.length;
      
      // Call chunk callback
      this.onChunk?.(currentText, progress, nextIndex);
      
      this.currentIndex = nextIndex;
      
      // Calculate delay based on speed
      const chunkDelay = this.mode === 'word' 
        ? Math.max(100, 1000 / this.speed) // Minimum 100ms for words
        : 1000 / this.speed; // Characters per second
      
      // Schedule next chunk
      this.timeoutId = setTimeout(() => {
        this.processNextChunk(resolve);
      }, chunkDelay);
      
    } else {
      // Streaming complete
      this.complete(resolve);
    }
  }

  /**
   * Complete the streaming process
   * @param {Function} resolve - Promise resolve function
   */
  complete(resolve) {
    this.isActive = false;
    this.isPaused = false;
    
    const finalText = this.chunks.join('');
    this.onComplete?.(finalText);
    resolve(finalText);
  }

  /**
   * Stop streaming immediately
   */
  stop() {
    this.isActive = false;
    this.isPaused = false;
    
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * Pause streaming
   */
  pause() {
    this.isPaused = true;
    
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * Resume streaming
   */
  resume() {
    if (this.isActive && this.isPaused) {
      this.isPaused = false;
      
      // Resume processing
      this.timeoutId = setTimeout(() => {
        this.processNextChunk(() => {});
      }, 50);
    }
  }

  /**
   * Skip to end of streaming
   * @returns {string} - Complete text
   */
  skip() {
    this.stop();
    const finalText = this.chunks.join('');
    this.onComplete?.(finalText);
    return finalText;
  }

  /**
   * Get current streaming status
   * @returns {Object} - Status object
   */
  getStatus() {
    return {
      isActive: this.isActive,
      isPaused: this.isPaused,
      progress: this.chunks.length > 0 ? this.currentIndex / this.chunks.length : 0,
      currentIndex: this.currentIndex,
      totalChunks: this.chunks.length
    };
  }

  /**
   * Update streaming configuration
   * @param {Object} options - New options
   */
  updateConfig(options) {
    this.speed = options.speed ?? this.speed;
    this.chunkSize = options.chunkSize ?? this.chunkSize;
    this.delay = options.delay ?? this.delay;
    this.mode = options.mode ?? this.mode;
  }
}

/**
 * Create a streaming simulator instance
 * @param {Object} options - Configuration options
 * @returns {StreamingSimulator} - Simulator instance
 */
export const createStreamingSimulator = (options = {}) => {
  return new StreamingSimulator(options);
};

/**
 * Simulate streaming for a single text
 * @param {string} text - Text to stream
 * @param {Object} options - Streaming options
 * @returns {Promise} - Resolves when streaming is complete
 */
export const simulateTextStreaming = async (text, options = {}) => {
  const simulator = createStreamingSimulator(options);
  return simulator.startStreaming(text);
};

/**
 * Default streaming configurations
 */
export const STREAMING_PRESETS = {
  fast: {
    speed: 100,
    chunkSize: 1,
    delay: 100,
    mode: 'character'
  },
  normal: {
    speed: 50,
    chunkSize: 1,
    delay: 200,
    mode: 'character'
  },
  slow: {
    speed: 25,
    chunkSize: 1,
    delay: 300,
    mode: 'character'
  },
  wordByWord: {
    speed: 30,
    chunkSize: 1,
    delay: 200,
    mode: 'word'
  },
  realistic: {
    speed: 60,
    chunkSize: 1,
    delay: 150,
    mode: 'character'
  }
};
