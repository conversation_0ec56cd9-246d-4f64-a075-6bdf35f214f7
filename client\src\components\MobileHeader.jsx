import React from 'react';
import { Menu, MessageSquare } from 'lucide-react';
import { useSidebar } from '../context/SidebarContext';

const MobileHeader = () => {
  const { isMobile, toggleMobileMenu } = useSidebar();

  // Only show on mobile
  if (!isMobile) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between z-30 sm:hidden">
      {/* Hamburger Menu Button */}
      <button
        onClick={toggleMobileMenu}
        className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
        aria-label="Toggle menu"
      >
        <Menu className="w-6 h-6 text-gray-700" />
      </button>

      {/* App Title */}
      <div className="flex items-center gap-2">
        <MessageSquare className="w-6 h-6 text-blue-500" />
        <h1 className="text-lg font-semibold text-gray-800">Imta AI</h1>
      </div>

      {/* Placeholder for balance */}
      <div className="w-10"></div>
    </div>
  );
};

export default MobileHeader;
