@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-height: 100vh;
}

/* Custom Scrollbar Styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.8);
}

/* Hide scrollbar by default, show on hover */
.custom-scrollbar::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.custom-scrollbar:hover::-webkit-scrollbar {
  width: 6px;
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: none;
}

.custom-scrollbar:hover {
  scrollbar-width: thin;
}

/* Message Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out forwards;
}

/* Smooth transitions for better UX */
.transition-smooth {
  transition: all 0.2s ease-in-out;
}

/* Custom Scrollbar for Sidebar */
.sidebar-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

.sidebar-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* Hide scrollbar by default, show on hover */
.sidebar-scrollbar::-webkit-scrollbar {
  width: 0px;
  transition: width 0.2s ease;
}

.sidebar-scrollbar:hover::-webkit-scrollbar {
  width: 6px;
}

/* For Firefox - show thin scrollbar on hover */
.sidebar-scrollbar {
  scrollbar-width: none;
}

.sidebar-scrollbar:hover {
  scrollbar-width: thin;
}

/* Streaming Text Animations */
.streaming-cursor {
  animation: cursor-blink 1.2s infinite;
}

@keyframes cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Streaming dots animation */
@keyframes streamingDots {
  0%, 20% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 1; }
  80%, 100% { transform: scale(1); opacity: 0.5; }
}

.streaming-dot {
  animation: streamingDots 1.5s infinite;
}

.streaming-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.streaming-dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* Smooth text reveal animation */
@keyframes textReveal {
  from {
    opacity: 0;
    transform: translateY(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.text-reveal {
  animation: textReveal 0.2s ease-out;
}

/* Chat streaming animations */
.message.streaming .message-content {
  transition: all 0.2s ease-in-out;
}

.message.streaming {
  opacity: 0.8;
}

.message-content {
  transition: all 0.2s ease-in-out;
}

.typing-indicator {
  display: inline-block;
  animation: blink 1s step-end infinite;
  margin-left: 2px;
  color: #3b82f6;
  font-weight: bold;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.message-metadata {
  font-size: 0.75em;
  color: #666;
  margin-top: 4px;
}

.text-xxs {
  font-size: 0.65rem;
}

/* Markdown content styling */
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.markdown-content p {
  margin-bottom: 0.75rem;
}

.markdown-content code {
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-content pre {
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.markdown-content blockquote {
  margin: 0.5rem 0;
}

.markdown-content a {
  text-decoration: underline;
}

.markdown-content a:hover {
  text-decoration: none;
}

/* Animation for messages */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out;
}

/* Message streaming effects */
.message.streaming .message-content {
  opacity: 0.95;
  transition: opacity 0.3s ease;
}

.streaming-content {
  line-height: 1.6;
  word-wrap: break-word;
}

/* Typewriter Streaming Text Styles */
.typewriter-container {
  display: inline-block;
  width: 100%;
}

.typewriter-text {
  display: inline;
  position: relative;
}

.typewriter-cursor {
  display: inline-block;
  background-color: currentColor;
  width: 2px;
  margin-left: 1px;
  animation: typewriter-blink 1s infinite;
  opacity: 1;
}

@keyframes typewriter-blink {
  0%, 50% { 
    opacity: 1; 
  }
  51%, 100% { 
    opacity: 0; 
  }
}

/* Alternative cursor styles */
.typewriter-cursor-block {
  display: inline-block;
  background-color: currentColor;
  width: 8px;
  height: 1.2em;
  margin-left: 2px;
  animation: typewriter-blink 1s infinite;
}

.typewriter-cursor-underscore {
  display: inline-block;
  border-bottom: 2px solid currentColor;
  width: 8px;
  margin-left: 1px;
  animation: typewriter-blink 1s infinite;
}

/* Smooth character reveal animation */
.typewriter-text .char-reveal {
  opacity: 0;
  animation: char-reveal 0.1s ease-out forwards;
}

@keyframes char-reveal {
  from {
    opacity: 0;
    transform: translateY(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
