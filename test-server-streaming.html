<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Server Streaming</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .streaming-output {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            min-height: 100px;
            font-size: 18px;
            line-height: 1.6;
            white-space: pre-wrap;
            position: relative;
        }
        .streaming-output::after {
            content: '▋';
            color: #007bff;
            animation: blink 1s infinite;
            margin-left: 2px;
        }
        .streaming-output.completed::after {
            display: none;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            text-align: center;
            margin: 15px 0;
            font-weight: bold;
        }
        .status.streaming {
            color: #28a745;
        }
        .status.completed {
            color: #007bff;
        }
        .status.error {
            color: #dc3545;
        }
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🐌 Test Server SIÊU CHẬM Streaming (3 giây/ký tự)</h1>
        
        <div class="controls">
            <button class="btn" id="startBtn" onclick="startStreaming()">Bắt đầu Test</button>
            <button class="btn" id="stopBtn" onclick="stopStreaming()" disabled>Dừng</button>
            <button class="btn" onclick="clearOutput()">Xóa</button>
        </div>
        
        <div class="status" id="status">Sẵn sàng test streaming</div>
        
        <div class="streaming-output" id="output"></div>
        
        <div class="log" id="log">
            <div><strong>Server Log:</strong></div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let isStreaming = false;

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('vi-VN');
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(message, className = '') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${className}`;
        }

        function startStreaming() {
            if (isStreaming) return;

            const outputElement = document.getElementById('output');
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');

            outputElement.textContent = '';
            outputElement.className = 'streaming-output';
            
            startBtn.disabled = true;
            stopBtn.disabled = false;
            isStreaming = true;

            updateStatus('Đang kết nối server...', 'streaming');
            log('🚀 Bắt đầu kết nối EventSource...');

            eventSource = new EventSource('http://localhost:3001/test-stream');

            eventSource.onopen = function(event) {
                updateStatus('Đang streaming từ server (SIÊU CHẬM: 3 giây/ký tự - hãy kiên nhẫn!)', 'streaming');
                log('✅ Kết nối thành công, bắt đầu nhận stream');
            };

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    
                    if (data.completed) {
                        log('🎉 Stream hoàn tất!');
                        updateStatus('Streaming hoàn tất!', 'completed');
                        outputElement.className = 'streaming-output completed';
                        stopStreaming();
                        return;
                    }

                    if (data.chunk && data.chunk !== '[DONE]') {
                        outputElement.textContent += data.chunk;
                        log(`🔤 Nhận ký tự ${data.index}/${data.total}: "${data.char}"`);
                    }
                } catch (error) {
                    log(`❌ Lỗi parse data: ${error.message}`);
                    console.error('Parse error:', error, 'Raw data:', event.data);
                }
            };

            eventSource.onerror = function(event) {
                log('❌ Lỗi kết nối EventSource');
                updateStatus('Lỗi kết nối server. Vui lòng kiểm tra server có chạy?', 'error');
                stopStreaming();
            };
        }

        function stopStreaming() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }

            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            
            startBtn.disabled = false;
            stopBtn.disabled = true;
            isStreaming = false;

            if (document.getElementById('status').className.includes('streaming')) {
                updateStatus('Đã dừng streaming', '');
            }
            
            log('🛑 Đã dừng streaming');
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
            document.getElementById('output').className = 'streaming-output';
            document.getElementById('log').innerHTML = '<div><strong>Server Log:</strong></div>';
            updateStatus('Đã xóa kết quả', '');
        }        // Initialize
        log('🐌 SIÊU CHẬM Test client sẵn sàng!');
        log('💡 Trước khi test, hãy chạy: node test-ultra-slow-server.js');
        log('⚠️  LƯU Ý: Mỗi ký tự sẽ xuất hiện sau 3 giây - hãy kiên nhẫn chờ đợi!');
    </script>
</body>
</html>
