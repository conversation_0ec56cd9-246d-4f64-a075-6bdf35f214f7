#!/bin/bash

echo "🔄 Updating NGINX config with Docker container IP..."

# Get container IP
CONTAINER_IP=$(docker inspect imta-app-prod | grep -Po '"IPAddress": "\K[^"]*' | grep -v "^$" | head -1)

if [ -z "$CONTAINER_IP" ]; then
    echo "❌ Could not get container IP"
    exit 1
fi

echo "📍 Container IP: $CONTAINER_IP"

# Update nginx config
sed -i "s/proxy_pass http:\/\/[0-9.]*:5001/proxy_pass http:\/\/$CONTAINER_IP:5001/" /etc/nginx/sites-enabled/imta.ai.conf

# Test and reload nginx
nginx -t
if [ $? -eq 0 ]; then
    systemctl reload nginx
    echo "✅ NGINX updated with IP: $CONTAINER_IP"
else
    echo "❌ NGINX config error"
    exit 1
fi

