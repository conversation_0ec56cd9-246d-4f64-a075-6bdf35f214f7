import React, { useState, useRef, useCallback } from 'react';
import { Upload, X, File, Image, FileText, AlertCircle } from 'lucide-react';
import FilePreview from './FilePreview';
import ProgressBar from './ProgressBar';
import { validateFile, formatFileSize } from './FileValidator';

const FileUpload = ({ onFileSelect, onFileRemove, disabled = false, maxFiles = 5 }) => {
  const [files, setFiles] = useState([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [errors, setErrors] = useState({});
  const fileInputRef = useRef(null);

  // Handle file selection
  const handleFileSelect = useCallback((selectedFiles) => {
    const fileArray = Array.from(selectedFiles);
    const newFiles = [];
    const newErrors = {};

    fileArray.forEach((file) => {
      const validation = validateFile(file);
      if (validation.isValid) {
        const fileWithId = {
          id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          file,
          name: file.name,
          size: file.size,
          type: file.type,
          preview: null,
        };

        // Create preview for images
        if (file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = (e) => {
            setFiles(prev => prev.map(f => 
              f.id === fileWithId.id 
                ? { ...f, preview: e.target.result }
                : f
            ));
          };
          reader.readAsDataURL(file);
        }

        newFiles.push(fileWithId);
      } else {
        newErrors[file.name] = validation.error;
      }
    });

    // Check total file limit
    if (files.length + newFiles.length > maxFiles) {
      newErrors['limit'] = `Chỉ có thể upload tối đa ${maxFiles} files`;
      return;
    }

    setFiles(prev => [...prev, ...newFiles]);
    setErrors(newErrors);

    // Notify parent component
    newFiles.forEach(fileData => {
      onFileSelect?.(fileData);
    });

    // Clear input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [files.length, maxFiles, onFileSelect]);

  // Handle drag events
  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (disabled) return;

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      handleFileSelect(droppedFiles);
    }
  }, [disabled, handleFileSelect]);

  // Handle click upload
  const handleClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled]);

  // Handle file input change
  const handleInputChange = useCallback((e) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      handleFileSelect(selectedFiles);
    }
  }, [handleFileSelect]);

  // Remove file
  const handleRemoveFile = useCallback((fileId) => {
    setFiles(prev => {
      const updatedFiles = prev.filter(f => f.id !== fileId);
      const removedFile = prev.find(f => f.id === fileId);
      if (removedFile) {
        onFileRemove?.(removedFile);
      }
      return updatedFiles;
    });
    
    // Remove any errors for this file
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fileId];
      return newErrors;
    });
  }, [onFileRemove]);

  // Simulate upload progress (replace with real upload logic)
  const simulateUpload = useCallback((fileId) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 30;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
      }
      setUploadProgress(prev => ({
        ...prev,
        [fileId]: progress
      }));
    }, 200);
  }, []);

  return (
    <div className="w-full">
      {/* Upload Area */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
          transition-all duration-200 ease-in-out
          ${isDragOver 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleInputChange}
          className="hidden"
          accept="image/*,.pdf,.doc,.docx,.txt,.csv,.xlsx"
          disabled={disabled}
        />

        <div className="flex flex-col items-center space-y-2">
          <Upload className={`w-8 h-8 ${isDragOver ? 'text-blue-500' : 'text-gray-400'}`} />
          <div className="text-sm">
            <span className="font-medium text-gray-700">
              {isDragOver ? 'Thả files vào đây' : 'Click để upload hoặc kéo thả files'}
            </span>
            <p className="text-gray-500 mt-1">
              Hỗ trợ: Images, PDF, DOC, TXT (tối đa 10MB mỗi file)
            </p>
          </div>
        </div>
      </div>

      {/* Error Messages */}
      {Object.keys(errors).length > 0 && (
        <div className="mt-3 space-y-1">
          {Object.entries(errors).map(([key, error]) => (
            <div key={key} className="flex items-center gap-2 text-red-600 text-sm">
              <AlertCircle className="w-4 h-4" />
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}

      {/* File List */}
      {files.length > 0 && (
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium text-gray-700">
            Files đã chọn ({files.length}/{maxFiles})
          </h4>
          <div className="space-y-2">
            {files.map((fileData) => (
              <FilePreview
                key={fileData.id}
                fileData={fileData}
                progress={uploadProgress[fileData.id]}
                onRemove={() => handleRemoveFile(fileData.id)}
                onUpload={() => simulateUpload(fileData.id)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
