// Test script to verify ultra-slow streaming
const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

// Simple test endpoint for streaming
app.get('/test-stream', (req, res) => {
  console.log('🚀 Starting ultra-slow streaming test...');
  
  // Set headers for streaming
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('Access-Control-Allow-Origin', '*');
  const testMessage = "Test siêu chậm: mỗi ký tự 3 giây.";
  
  let index = 0;
  
  const streamInterval = setInterval(() => {
    if (index < testMessage.length) {
      const char = testMessage[index];
      console.log(`🔤 SIÊU CHẬM: Streaming char ${index + 1}/${testMessage.length}: "${char}" (3 giây/ký tự)`);
      
      res.write(`data: ${JSON.stringify({ 
        chunk: char,
        index: index + 1,
        total: testMessage.length,
        char: char,
        timestamp: new Date().toISOString()
      })}\n\n`);
      
      index++;
    } else {
      res.write(`data: ${JSON.stringify({ 
        chunk: '[DONE]',
        completed: true,
        timestamp: new Date().toISOString()
      })}\n\n`);
      
      clearInterval(streamInterval);
      res.end();
      console.log('✅ SIÊU CHẬM: Streaming test completed!');
    }
  }, 3000); // 3 seconds per character - EXTREMELY SLOW
  
  // Handle client disconnect
  req.on('close', () => {
    console.log('🔌 Client disconnected');
    clearInterval(streamInterval);
  });
});

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`🌟 SIÊU CHẬM: Streaming test server running on http://localhost:${PORT}`);
  console.log(`📡 Test streaming SIÊU CHẬM (3 giây/ký tự): http://localhost:${PORT}/test-stream`);
  console.log(`⚠️  CHÚ Ý: Mỗi ký tự sẽ xuất hiện sau 3 giây - hãy kiên nhẫn!`);
});
