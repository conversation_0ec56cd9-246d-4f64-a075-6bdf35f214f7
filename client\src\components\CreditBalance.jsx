import React, { useState } from 'react';
import { Coins, Plus, AlertTriangle, TrendingUp } from 'lucide-react';
import { useCredit } from '../context/CreditContext';

const CreditBalance = ({ 
  showDetails = false, 
  showTopUpButton = true, 
  size = 'normal',
  onTopUpClick 
}) => {
  const { creditBalance, isLoading, getCreditStats } = useCredit();
  const [showStats, setShowStats] = useState(false);

  // Determine warning level based on credit balance
  const getWarningLevel = () => {
    if (creditBalance <= 5) return 'critical';
    if (creditBalance <= 20) return 'warning';
    return 'normal';
  };

  const warningLevel = getWarningLevel();
  const stats = getCreditStats();

  // Size variants - Enhanced for better touch accessibility
  const sizeClasses = {
    small: {
      container: 'text-sm',
      icon: 'w-4 h-4',
      button: 'px-3 py-2 text-sm min-h-[44px] min-w-[44px]'
    },
    normal: {
      container: 'text-base',
      icon: 'w-5 h-5',
      button: 'px-4 py-3 text-base min-h-[48px] min-w-[48px]'
    },
    large: {
      container: 'text-lg',
      icon: 'w-6 h-6',
      button: 'px-5 py-4 text-lg min-h-[52px] min-w-[52px]'
    }
  };

  const classes = sizeClasses[size];

  // Warning colors
  const warningColors = {
    normal: 'text-green-600 bg-green-50 border-green-200',
    warning: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    critical: 'text-red-600 bg-red-50 border-red-200'
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${classes.container}`}>
        <div className="animate-pulse flex items-center gap-2">
          <div className={`${classes.icon} bg-gray-300 rounded`}></div>
          <div className="w-12 h-4 bg-gray-300 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${classes.container}`}>
      {/* Credit Balance Display - Enhanced for touch accessibility */}
      <div
        className={`
          flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors cursor-pointer min-h-[44px]
          ${warningColors[warningLevel]}
          ${showDetails ? 'hover:opacity-80' : ''}
          touch-manipulation
        `}
        onClick={showDetails ? () => setShowStats(!showStats) : undefined}
        title={showDetails ? 'Click để xem thống kê' : `${formatNumber(creditBalance)} credits`}
      >
        {/* Warning icon for low credits */}
        {warningLevel === 'critical' && (
          <AlertTriangle className={`${classes.icon} animate-pulse`} />
        )}
        
        {/* Coins icon */}
        <Coins className={classes.icon} />
        
        {/* Balance amount */}
        <span className="font-medium">
          {formatNumber(creditBalance)}
        </span>
        
        {/* Stats icon if details enabled */}
        {showDetails && (
          <TrendingUp className={`${classes.icon} opacity-60`} />
        )}
      </div>

      {/* Top-up button - Enhanced for better touch accessibility */}
      {showTopUpButton && (
        <button
          onClick={onTopUpClick}
          className={`
            flex items-center justify-center gap-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600
            transition-colors shadow-sm hover:shadow-md active:scale-95 touch-manipulation
            ${classes.button}
          `}
          title="Nạp thêm credit"
        >
          <Plus className={classes.icon} />
          <span className="hidden sm:inline font-medium">Nạp</span>
        </button>
      )}

      {/* Detailed Stats Popup */}
      {showStats && showDetails && (
        <div className="absolute top-full left-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50 min-w-64">
          <h3 className="font-semibold text-gray-900 mb-3">Thống kê Credit</h3>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Số dư hiện tại:</span>
              <span className="font-medium text-green-600">
                {formatNumber(stats.currentBalance)}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Đã dùng hôm nay:</span>
              <span className="font-medium text-blue-600">
                {formatNumber(stats.todayUsage)}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Đã dùng tháng này:</span>
              <span className="font-medium text-purple-600">
                {formatNumber(stats.monthlyUsage)}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Tổng đã mua:</span>
              <span className="font-medium text-gray-900">
                {formatNumber(stats.totalPurchased)}
              </span>
            </div>
          </div>

          {/* Usage Rate */}
          <div className="mt-3 pt-3 border-t border-gray-100">
            <div className="text-xs text-gray-500">
              Tỷ lệ sử dụng: {stats.monthlyUsage > 0 ? 
                `${((stats.monthlyUsage / stats.totalPurchased) * 100).toFixed(1)}%` : 
                '0%'
              }
            </div>
          </div>
        </div>
      )}

      {/* Low Credit Warning */}
      {warningLevel === 'critical' && (
        <div className="absolute top-full left-0 mt-1 bg-red-100 border border-red-200 rounded-lg p-2 z-40 min-w-48">
          <div className="flex items-center gap-2 text-red-700 text-xs">
            <AlertTriangle className="w-3 h-3" />
            <span>Credit sắp hết! Vui lòng nạp thêm.</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreditBalance;
