<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultra Slow Streaming Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .demo-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
        }

        .title {
            text-align: center;
            color: #333;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
        }

        .message-bubble {
            background: #f0f0f0;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            min-height: 60px;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .bot-icon {
            width: 40px;
            height: 40px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            flex-shrink: 0;
        }

        .streaming-text {
            font-size: 16px;
            line-height: 1.6;
            color: #333;
            position: relative;
        }

        .streaming-text::after {
            content: '▋';
            color: #667eea;
            animation: blink 1s infinite;
            margin-left: 2px;
        }

        .streaming-text.completed::after {
            display: none;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .controls {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .speed-info {
            text-align: center;
            color: #666;
            margin: 15px 0;
            font-style: italic;
        }

        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="title">🚀 Ultra Slow Streaming Demo</h1>
        <div class="speed-info">Tốc độ: 0.5 ký tự/giây (SIÊU CHẬM: 2 giây mỗi ký tự)</div>
        
        <div class="message-bubble">
            <div class="bot-icon">🤖</div>
            <div id="streamingText" class="streaming-text"></div>
        </div>

        <div class="controls">
            <button class="btn" onclick="startDemo()">Bắt đầu Demo</button>
            <button class="btn" onclick="resetDemo()">Reset</button>
            <button class="btn" onclick="toggleSpeed()">Chuyển tốc độ</button>
        </div>

        <div class="log" id="logContainer">
            <div><strong>Log hoạt động:</strong></div>
        </div>
    </div>

    <script>
        let currentSpeed = 0.5; // characters per second - EXTREMELY SLOW
        let isRunning = false;
        let currentInterval = null;        const sampleTexts = [
            "SIÊU CHẬM: Mỗi ký tự 2 giây.",
            "Test streaming cực chậm để dễ quan sát.",
            "Bạn sẽ thấy từng ký tự xuất hiện rất chậm.",
        ];

        let currentTextIndex = 0;

        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString('vi-VN');
            logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function startDemo() {
            if (isRunning) {
                log('❌ Demo đang chạy, vui lòng chờ hoặc reset');
                return;
            }

            const textElement = document.getElementById('streamingText');
            const text = sampleTexts[currentTextIndex];
            currentTextIndex = (currentTextIndex + 1) % sampleTexts.length;

            textElement.textContent = '';
            textElement.className = 'streaming-text';
            isRunning = true;

            log(`🚀 SIÊU CHẬM: Bắt đầu streaming "${text.substring(0, 20)}..." với tốc độ ${currentSpeed} ký tự/giây (${2/currentSpeed} giây/ký tự)`);

            let index = 0;
            const interval = 1000 / currentSpeed; // milliseconds per character

            currentInterval = setInterval(() => {
                if (index < text.length) {
                    textElement.textContent += text[index];
                    log(`🔤 Ký tự ${index + 1}/${text.length}: "${text[index]}"`);
                    index++;
                } else {
                    clearInterval(currentInterval);
                    textElement.className = 'streaming-text completed';
                    isRunning = false;
                    log('✅ Streaming hoàn tất!');
                }
            }, interval);
        }

        function resetDemo() {
            if (currentInterval) {
                clearInterval(currentInterval);
            }
            isRunning = false;
            document.getElementById('streamingText').textContent = '';
            document.getElementById('streamingText').className = 'streaming-text';
            document.getElementById('logContainer').innerHTML = '<div><strong>Log hoạt động:</strong></div>';
            log('🔄 Demo đã được reset');
        }        function toggleSpeed() {
            const speeds = [0.25, 0.5, 1, 2]; // Even slower options
            const currentIndex = speeds.indexOf(currentSpeed);
            currentSpeed = speeds[(currentIndex + 1) % speeds.length];
            
            const secondsPerChar = (1/currentSpeed).toFixed(1);
            document.querySelector('.speed-info').textContent = 
                `Tốc độ: ${currentSpeed} ký tự/giây (${secondsPerChar} giây/ký tự - ${currentSpeed <= 0.5 ? 'SIÊU CHẬM' : currentSpeed <= 1 ? 'cực chậm' : 'chậm'})`;
            
            log(`⚡ Đã chuyển tốc độ streaming thành ${currentSpeed} ký tự/giây (${secondsPerChar} giây/ký tự)`);
        }

        // Initialize
        log('🎯 Ultra Slow Streaming Demo sẵn sàng!');
        log('💡 Nhấn "Bắt đầu Demo" để xem hiệu ứng streaming từ từ');
    </script>
</body>
</html>
