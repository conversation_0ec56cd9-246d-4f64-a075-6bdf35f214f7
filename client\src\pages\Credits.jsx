import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Coins, Plus, Gift, ArrowLeft, MessageSquare } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useCredit } from '../context/CreditContext';
import PaymentModal from '../components/PaymentModal';
import TopBar from '../components/TopBar';

const Credits = () => {
  const { user } = useAuth();
  const { creditBalance, CREDIT_COSTS } = useCredit();
  const navigate = useNavigate();
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(null);

  // Handle payment success
  const handlePaymentSuccess = (result) => {
    setPaymentSuccess(result);
    setTimeout(() => setPaymentSuccess(null), 5000);
  };

  // Handle back to chat navigation
  const handleBackToChat = () => {
    navigate('/chat');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* TopBar */}
      <TopBar onTopUpClick={() => setShowPaymentModal(true)} />

      <div className="py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header with Back Button */}
          <div className="mb-8">
            {/* Back to Chat Button */}
            <div className="mb-6">
              <button
                onClick={handleBackToChat}
                className="inline-flex items-center gap-2 px-4 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-blue-600 hover:border-blue-300 transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 min-h-[48px] touch-manipulation"
                title="Quay về giao diện chat chính"
              >
                <ArrowLeft className="w-4 h-4 flex-shrink-0" />
                <MessageSquare className="w-4 h-4 flex-shrink-0" />
                <span className="hidden sm:inline">Quay về Chat</span>
                <span className="sm:hidden">Chat</span>
              </button>
            </div>

            {/* Page Title */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Quản lý Credit
              </h1>
              <p className="text-gray-600">
                Theo dõi và nạp thêm credit cho tài khoản của bạn
              </p>
            </div>
          </div>

        {/* Success notification */}
        {paymentSuccess && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-700">
              <Gift className="w-5 h-5" />
              <span className="font-medium">
                Nạp credit thành công! +{paymentSuccess.transaction.amount} credits đã được thêm vào tài khoản.
              </span>
            </div>
          </div>
        )}

        {/* Credit Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-6 mb-8">
          {/* Current Balance */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Số dư hiện tại
              </h3>
              <Coins className="w-6 h-6 text-yellow-500" />
            </div>

            <div className="text-3xl font-bold text-gray-900 mb-2">
              {creditBalance.toLocaleString()}
            </div>

            <div className="text-sm text-gray-600 mb-4">
              credits có sẵn
            </div>

            <button
              onClick={() => setShowPaymentModal(true)}
              className="w-full flex items-center justify-center gap-2 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
            >
              <Plus className="w-4 h-4" />
              Nạp thêm credit
            </button>
          </div>
        </div>

        {/* Credit Costs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Bảng giá Credit
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-2">
                {CREDIT_COSTS.message}
              </div>
              <div className="text-sm text-gray-600">
                credit / tin nhắn
              </div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-2">
                {CREDIT_COSTS.fileUpload}
              </div>
              <div className="text-sm text-gray-600">
                credit / file upload
              </div>
            </div>
            
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600 mb-2">
                {CREDIT_COSTS.chartGeneration}
              </div>
              <div className="text-sm text-gray-600">
                credit / biểu đồ
              </div>
            </div>
            
            <div className="text-center p-4 bg-pink-50 rounded-lg">
              <div className="text-2xl font-bold text-pink-600 mb-2">
                {CREDIT_COSTS.imageGeneration}
              </div>
              <div className="text-sm text-gray-600">
                credit / hình ảnh
              </div>
            </div>
          </div>
        </div>

          {/* Payment Modal */}
          <PaymentModal
            isOpen={showPaymentModal}
            onClose={() => setShowPaymentModal(false)}
            onSuccess={handlePaymentSuccess}
          />
        </div>
      </div>
    </div>
  );
};

export default Credits;
