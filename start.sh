#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting IMTA AI Project...${NC}"

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker is running${NC}"
}

# Function to check if required containers are running
check_containers() {
    local containers=("imta-mongodb" "imta-redis" "imta-minio")
    local missing_containers=()
    
    for container in "${containers[@]}"; do
        if ! docker ps --format "table {{.Names}}" | grep -q "^${container}$"; then
            missing_containers+=("$container")
        fi
    done
    
    if [ ${#missing_containers[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ All required containers are running${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  Missing containers: ${missing_containers[*]}${NC}"
        return 1
    fi
}

# Function to start Docker containers
start_containers() {
    echo -e "${BLUE}🐳 Starting Docker containers...${NC}"
    docker-compose up -d mongodb redis minio
    
    # Wait for containers to be ready
    echo -e "${YELLOW}⏳ Waiting for containers to be ready...${NC}"
    sleep 10
    
    # Check if containers are healthy
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if check_containers; then
            echo -e "${GREEN}✅ All containers are ready!${NC}"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            echo -e "${RED}❌ Containers failed to start properly${NC}"
            exit 1
        fi
        
        echo -e "${YELLOW}⏳ Attempt $attempt/$max_attempts - Waiting for containers...${NC}"
        sleep 5
        ((attempt++))
    done
}

# Function to install dependencies
install_deps() {
    echo -e "${BLUE}📦 Installing dependencies...${NC}"
    
    # Install root dependencies
    if [ -f "package.json" ]; then
        echo -e "${YELLOW}Installing root dependencies...${NC}"
        npm install
    fi
    
    # Install server dependencies
    if [ -d "server" ] && [ -f "server/package.json" ]; then
        echo -e "${YELLOW}Installing server dependencies...${NC}"
        cd server && npm install && cd ..
    fi
    
    # Install client dependencies
    if [ -d "client" ] && [ -f "client/package.json" ]; then
        echo -e "${YELLOW}Installing client dependencies...${NC}"
        cd client && npm install && cd ..
    fi
    
    echo -e "${GREEN}✅ Dependencies installed${NC}"
}

# Function to start the application
start_app() {
    echo -e "${BLUE}🎯 Starting application...${NC}"
    
    # Check if concurrently is available
    if [ -f "package.json" ] && grep -q "concurrently" package.json; then
        echo -e "${GREEN}🚀 Starting with concurrently (server + client)${NC}"
        
        # Display URLs before starting
        echo -e "${BLUE}📋 Application URLs:${NC}"
        echo -e "${GREEN}   Frontend: ${YELLOW}http://localhost:5173${NC}"
        echo -e "${GREEN}   Backend API: ${YELLOW}http://localhost:5001/api${NC}"
        echo -e "${GREEN}   Health Check: ${YELLOW}http://localhost:5001/api/health${NC}"
        echo -e "${GREEN}   MongoDB: ${YELLOW}localhost:27017${NC}"
        echo -e "${GREEN}   Redis: ${YELLOW}localhost:6380${NC}"
        echo -e "${GREEN}   MinIO: ${YELLOW}localhost:9000${NC}"
        echo -e "${BLUE}   MongoDB Admin: ${YELLOW}http://localhost:8081${NC}"
        echo -e "${BLUE}   Redis Admin: ${YELLOW}http://localhost:8082${NC}"
        echo ""
        echo -e "${GREEN}🎉 Ready to use! Open ${YELLOW}http://localhost:5173${GREEN} in your browser${NC}"
        echo ""
        
        npm run dev
    else
        echo -e "${YELLOW}⚠️  Starting server and client separately${NC}"
        
        # Display URLs before starting
        echo -e "${BLUE}📋 Application URLs:${NC}"
        echo -e "${GREEN}   Frontend: ${YELLOW}http://localhost:5173${NC}"
        echo -e "${GREEN}   Backend API: ${YELLOW}http://localhost:5001/api${NC}"
        echo -e "${GREEN}   Health Check: ${YELLOW}http://localhost:5001/api/health${NC}"
        echo ""
        echo -e "${GREEN}🎉 Ready to use! Open ${YELLOW}http://localhost:5173${GREEN} in your browser${NC}"
        echo ""
        
        # Start server in background
        echo -e "${BLUE}🔧 Starting server...${NC}"
        cd server && npm start &
        SERVER_PID=$!
        cd ..
        
        # Wait a bit for server to start
        sleep 3
        
        # Start client
        echo -e "${BLUE}🎨 Starting client...${NC}"
        cd client && npm start &
        CLIENT_PID=$!
        cd ..
        
        # Wait for both processes
        wait $SERVER_PID $CLIENT_PID
    fi
}

# Main execution
main() {
    # Check Docker
    check_docker
    
    # Check if containers are running, start if needed
    if ! check_containers; then
        start_containers
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ] || [ ! -d "server/node_modules" ] || [ ! -d "client/node_modules" ]; then
        install_deps
    fi
    
    # Start the application
    start_app
}

# Run main function
main 