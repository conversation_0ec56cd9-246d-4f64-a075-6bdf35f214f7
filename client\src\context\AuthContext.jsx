import React, { createContext, useContext, useReducer, useEffect, useState, useCallback } from 'react';
import axios from 'axios';
import { authAPI } from '../utils/api';

// Auth action types
const authActionTypes = {
  LOGIN_START: 'LOGIN_START',
  LOGI<PERSON>_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  REGISTER_START: 'REGISTER_START',
  REGISTER_SUCCESS: 'REGISTER_SUCCESS',
  REGISTER_FAILURE: 'REGISTER_FAILURE',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
  FETCH_PROFILE_START: 'FETCH_PROFILE_START',
  FETCH_PROFILE_SUCCESS: 'FETCH_PROFILE_SUCCESS',
  FET<PERSON>_PROFILE_FAILURE: 'FETCH_PROFILE_FAILURE',
  <PERSON><PERSON><PERSON>_PASSWORD_START: 'CHANGE_PASSWORD_START',
  <PERSON>AN<PERSON>_PASSWORD_SUCCESS: 'CHANGE_PASSWORD_SUCCESS',
  CHANGE_PASSWORD_FAILURE: 'CHANGE_PASSWORD_FAILURE',
  CLEAR_ERROR: 'CLEAR_ERROR',
  SET_LOADING: 'SET_LOADING'
};

// Initial auth state
const initialAuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  loginLoading: false,
  registerLoading: false,
  changePasswordLoading: false,
  profileLoading: false
};

// Auth reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case authActionTypes.LOGIN_START:
      return {
        ...state,
        loginLoading: true,
        error: null
      };

    case authActionTypes.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        loginLoading: false,
        error: null
      };

    case authActionTypes.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loginLoading: false,
        error: action.payload
      };

    case authActionTypes.REGISTER_START:
      return {
        ...state,
        registerLoading: true,
        error: null
      };

    case authActionTypes.REGISTER_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        registerLoading: false,
        error: null
      };

    case authActionTypes.REGISTER_FAILURE:
      return {
        ...state,
        registerLoading: false,
        error: action.payload
      };

    case authActionTypes.LOGOUT:
      return {
        ...initialAuthState
      };

    case authActionTypes.UPDATE_PROFILE:
      return {
        ...state,
        user: { ...state.user, ...action.payload }
      };

    case authActionTypes.FETCH_PROFILE_START:
      return {
        ...state,
        profileLoading: true,
        error: null
      };

    case authActionTypes.FETCH_PROFILE_SUCCESS:
      return {
        ...state,
        user: action.payload,
        profileLoading: false,
        error: null
      };

    case authActionTypes.FETCH_PROFILE_FAILURE:
      return {
        ...state,
        profileLoading: false,
        error: action.payload
      };

    case authActionTypes.CHANGE_PASSWORD_START:
      return {
        ...state,
        changePasswordLoading: true,
        error: null
      };

    case authActionTypes.CHANGE_PASSWORD_SUCCESS:
      return {
        ...state,
        changePasswordLoading: false,
        error: null
      };

    case authActionTypes.CHANGE_PASSWORD_FAILURE:
      return {
        ...state,
        changePasswordLoading: false,
        error: action.payload
      };

    case authActionTypes.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case authActionTypes.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };

    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept-Language': 'vi'
  }
});

// Add request interceptor to include auth token
api.interceptors.request.use(config => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Real API functions
const realAPI = {
  login: async (email, password) => {
    try {
      const response = await api.post('/auth/login', {
        email,
        password
      });
      
      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Đăng nhập thất bại');
      }
    } catch (error) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Đăng nhập thất bại');
      }
      throw new Error('Lỗi kết nối đến server');
    }
  },

  register: async (userData) => {
    try {
      const response = await api.post('/auth/register', {
        username: userData.username,
        email: userData.email,
        password: userData.password,
        fullName: userData.fullName,
        phone: userData.phone || ''
      });
      
      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Đăng ký thất bại');
      }
    } catch (error) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Đăng ký thất bại');
      }
      throw new Error('Lỗi kết nối đến server');
    }
  },  changePassword: async (currentPassword, newPassword) => {
    try {
      console.log('🔄 Calling changePassword API with Axios...');
      
      // Use same Axios API as login/register for consistency
      const response = await api.post('/auth/change-password', {
        currentPassword,
        newPassword
      });
      
      console.log('✅ Full response received:', response);
      console.log('📦 Response data:', response.data);
      console.log('✅ Response success:', response.data?.success);
      
      if (response.data && response.data.success) {
        console.log('✅ Password change successful!');
        return { success: true };
      } else {
        console.log('❌ Password change failed, response.data:', response.data);
        throw new Error(response.data?.message || 'Đổi mật khẩu thất bại');
      }
    } catch (error) {
      console.log('❌ Exception caught:', error);
      console.log('❌ Error response:', error.response);
      console.log('❌ Error response data:', error.response?.data);
      
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      throw new Error(error.message || 'Lỗi kết nối đến server');
    }
  },

  updateProfile: async (profileData) => {
    try {
      const response = await api.put('/auth/me', profileData);
      
      if (response.data.success) {
        return response.data.data.user;
      } else {
        throw new Error(response.data.message || 'Cập nhật profile thất bại');
      }
    } catch (error) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Cập nhật profile thất bại');
      }
      throw new Error('Lỗi kết nối đến server');
    }
  }
};

// Auth Provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialAuthState);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load auth state from localStorage on mount - only once
  useEffect(() => {
    const loadAuthState = () => {
      try {
        const token = localStorage.getItem('authToken');
        const userData = localStorage.getItem('userData');

        if (token && userData) {
          const user = JSON.parse(userData);
          dispatch({
            type: authActionTypes.LOGIN_SUCCESS,
            payload: { user, token }
          });
        }
      } catch (error) {
        console.error('Error loading auth state:', error);
        // Clear invalid data
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
      } finally {
        setIsInitialized(true);
      }
    };

    // Load immediately without delay
    loadAuthState();
  }, []); // Empty dependency array - only run once

  // Login function
  const login = async (email, password) => {
    dispatch({ type: authActionTypes.LOGIN_START });
    
    try {
      const response = await realAPI.login(email, password);
      
      // Store in localStorage
      localStorage.setItem('authToken', response.token);
      localStorage.setItem('userData', JSON.stringify(response.user));
      
      dispatch({
        type: authActionTypes.LOGIN_SUCCESS,
        payload: response
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: authActionTypes.LOGIN_FAILURE,
        payload: error.message
      });
      throw error;
    }
  };

  // Register function
  const register = async (userData) => {
    dispatch({ type: authActionTypes.REGISTER_START });
    
    try {
      const response = await realAPI.register(userData);
      
      // Store in localStorage
      localStorage.setItem('authToken', response.token);
      localStorage.setItem('userData', JSON.stringify(response.user));
      
      dispatch({
        type: authActionTypes.REGISTER_SUCCESS,
        payload: response
      });
      
      return response;
    } catch (error) {
      dispatch({
        type: authActionTypes.REGISTER_FAILURE,
        payload: error.message
      });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Call logout API if user is authenticated
      if (state.token) {
        await authAPI.logout();
      }
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // Clear localStorage
      localStorage.removeItem('authToken');
      localStorage.removeItem('userData');
      
      dispatch({ type: authActionTypes.LOGOUT });
    }
  };

  // Update profile function
  const updateProfile = async (profileData) => {
    try {
      const updatedData = await realAPI.updateProfile(profileData);
      
      dispatch({
        type: authActionTypes.UPDATE_PROFILE,
        payload: updatedData
      });
      
      // Update localStorage after state update
      const updatedUser = { ...state.user, ...updatedData };
      localStorage.setItem('userData', JSON.stringify(updatedUser));
      
      return updatedData;
    } catch (error) {
      throw error;
    }
  };

  // Refresh user profile from backend
  const refreshUserProfile = async () => {
    dispatch({ type: authActionTypes.FETCH_PROFILE_START });
    
    try {
      console.log('🔄 Fetching user profile from backend...');
      
      const response = await authAPI.getCurrentUser();
      console.log('✅ Profile fetched successfully:', response);
      
      if (response.success && response.data?.user) {
        const userData = response.data.user;
        
        dispatch({
          type: authActionTypes.FETCH_PROFILE_SUCCESS,
          payload: userData
        });
        
        // Update localStorage with fresh data
        localStorage.setItem('userData', JSON.stringify(userData));
        
        console.log('✅ Profile updated in state and localStorage');
        return userData;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error fetching user profile:', error);
      
      dispatch({
        type: authActionTypes.FETCH_PROFILE_FAILURE,
        payload: error.message || 'Không thể tải thông tin người dùng'
      });
      
      throw error;
    }
  };
  // Change password function
  const changePassword = async (currentPassword, newPassword) => {
    dispatch({ type: authActionTypes.CHANGE_PASSWORD_START });
    
    try {
      console.log('🔄 Changing password...');
      
      const result = await realAPI.changePassword(currentPassword, newPassword);
      
      console.log('✅ Password changed successfully:', result);
      
      dispatch({ type: authActionTypes.CHANGE_PASSWORD_SUCCESS });
      
      return { success: true };
    } catch (error) {
      console.error('❌ Change password error:', error);
      
      dispatch({
        type: authActionTypes.CHANGE_PASSWORD_FAILURE,
        payload: error.message
      });
      throw error;
    }
  };

  // Clear error function
  const clearError = useCallback(() => {
    dispatch({ type: authActionTypes.CLEAR_ERROR });
  }, []);
  const value = {
    ...state,
    isLoading: !isInitialized, // Use isInitialized to determine loading state
    login,
    register,
    logout,
    updateProfile,
    refreshUserProfile,
    changePassword,
    clearError
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};