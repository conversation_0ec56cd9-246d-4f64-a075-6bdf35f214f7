### ========================================
### IMTA AI API TESTING - REST CLIENT GUIDE
### ========================================
### 
### 🚀 HƯỚNG DẪN SỬ DỤNG:
### 1. Cài đặt REST Client extension trong VS Code
### 2. Mở file này và click "Send Request" phía trên mỗi request
### 3. Response sẽ hiển thị trong tab mới
### 4. Sử dụng Ctrl+Alt+R (Windows/Linux) hoặc Cmd+Alt+R (Mac) để chạy request
### 
### 📝 CÁCH SỬ DỤNG VARIABLES:
### - @baseUrl: URL cơ sở của API
### - @authToken: Token xác thực (sẽ được cập nhật sau khi login)
### - {{loginVi.response.body.data.token}}: Sử dụng token từ response trước
### 
### 🌐 INTERNATIONALIZATION (i18n):
### - Accept-Language: vi (Tiếng Việt)
### - Accept-Language: en (English)
### - X-Language: vi/en (Custom header)
### - ?lang=vi/en (Query parameter)
### 
### 🔧 TROUBLESHOOTING:
### - Nếu gặp lỗi connection: Kiểm tra server có đang chạy không
### - Nếu gặp lỗi 401: Chạy lại request login để lấy token mới
### - Nếu gặp lỗi 400: Kiểm tra dữ liệu gửi lên có đúng format không
### 
### ========================================

### Base URL và Variables
@baseUrl = http://localhost:5001/api
@authToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.R032y4pod_l1pEaaEqVIa0oS7UKtJwMqbHzGeXbdidE

### ========================================
### HEALTH CHECK - KIỂM TRA SERVER
### ========================================

### Health Check - Tiếng Việt
GET {{baseUrl}}/health
Accept-Language: vi

###

### Health Check - English
GET {{baseUrl}}/health
Accept-Language: en

### ========================================
### AUTHENTICATION - XÁC THỰC - VIETNAMESE
### ========================================

### Đăng ký tài khoản - Tiếng Việt
### Test với dữ liệu hợp lệ
POST {{baseUrl}}/auth/register
Content-Type: application/json
Accept-Language: vi

{
  "username": "testuser_vi",
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "Test User Việt Nam",
  "phone": "**********"
}

###

### Đăng ký tài khoản - Test validation errors
### Sẽ trả về lỗi cụ thể cho từng trường
POST {{baseUrl}}/auth/register
Content-Type: application/json
Accept-Language: vi

{
  "username": "ab",
  "email": "invalid-email",
  "password": "123",
  "fullName": "Test User"
}

###

### Đăng nhập - Tiếng Việt
# @name loginVi
POST {{baseUrl}}/auth/login
Content-Type: application/json
Accept-Language: vi

{
  "email": "<EMAIL>",
  "password": "password123"
}

###

### Lấy thông tin tài khoản - Tiếng Việt
GET {{baseUrl}}/auth/me
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

###

### Cập nhật thông tin tài khoản - Tiếng Việt
PUT {{baseUrl}}/auth/me
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "fullName": "Updated Test User Việt Nam",
  "phone": "0987654321",
  "preferences": {
    "language": "en",
    "theme": "dark"
  }
}

###

### Đổi mật khẩu - Tiếng Việt
POST {{baseUrl}}/auth/change-password
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "currentPassword": "password123",
  "newPassword": "newpassword123"
}

### ========================================
### AUTHENTICATION - ENGLISH
### ========================================

### Register User - English
POST {{baseUrl}}/auth/register
Content-Type: application/json
Accept-Language: en

{
  "username": "testuser_en",
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "Test User English",
  "phone": "0987654321"
}

###

### Login User - English
# @name loginEn
POST {{baseUrl}}/auth/login
Content-Type: application/json
Accept-Language: en

{
  "email": "<EMAIL>",
  "password": "password123"
}

###

### Get Profile - English
GET {{baseUrl}}/auth/me
Authorization: Bearer {{loginEn.response.body.data.token}}
Accept-Language: en

### ========================================
### PAYMENT - VIETNAMESE
### ========================================

### Create Deposit (Vietnamese)
# @name createDepositVi
POST {{baseUrl}}/payment/create-deposit
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "amount": 100000,
  "paymentMethod": "bank_transfer",
  "description": "Nạp tiền test"
}

###

### Get Payment History (Vietnamese)
GET {{baseUrl}}/payment/history?page=1&limit=10
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

###

### Get Payment Details (Vietnamese)
GET {{baseUrl}}/payment/{{createDepositVi.response.body.data.payment.id}}
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

### ========================================
### PAYMENT - ENGLISH
### ========================================

### Create Deposit (English)
POST {{baseUrl}}/payment/create-deposit
Content-Type: application/json
Authorization: Bearer {{loginEn.response.body.data.token}}
Accept-Language: en

{
  "amount": 50000,
  "paymentMethod": "e_wallet",
  "description": "Test deposit"
}

###

### Get Payment History (English)
GET {{baseUrl}}/payment/history?page=1&limit=10
Authorization: Bearer {{loginEn.response.body.data.token}}
Accept-Language: en

### ========================================
### CHAT - VIETNAMESE
### ========================================

### Send Message - Create New Chat (Vietnamese)
# @name createChatVi
POST {{baseUrl}}/chat
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "message": "Xin chào, tôi cần hỗ trợ về khóa học AI"
}

###

### Send Message - Existing Chat (Vietnamese)
POST {{baseUrl}}/chat
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "message": "Tôi muốn hỏi thêm về khóa học này",
  "chatId": "{{createChatVi.response.body.data.chatId}}"
}

###

### Get Chat History (Vietnamese)
GET {{baseUrl}}/chat/history?page=1&limit=10
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

###

### Get Chat Details (Vietnamese)
GET {{baseUrl}}/chat/{{createChatVi.response.body.data.chatId}}?page=1&limit=50
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

###

### Create New Chat (Vietnamese)
# @name createNewChatVi
POST {{baseUrl}}/chat/create
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "title": "Hỗ trợ về khóa học AI",
  "type": "course_inquiry",
  "metadata": {
    "courseId": "ai_course_001",
    "priority": "high"
  }
}

###

### Send Message to Specific Chat (Vietnamese)
POST {{baseUrl}}/chat/{{createNewChatVi.response.body.data.chat.id}}/messages
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "message": "Tôi muốn đăng ký khóa học này"
}

###

### Update Chat (Vietnamese)
PUT {{baseUrl}}/chat/{{createNewChatVi.response.body.data.chat.id}}
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "title": "Hỗ trợ về khóa học AI - Đã cập nhật",
  "status": "completed"
}

###

### Delete Chat (Vietnamese)
DELETE {{baseUrl}}/chat/{{createNewChatVi.response.body.data.chat.id}}
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

### ========================================
### CHAT - ENGLISH
### ========================================

### Send Message - Create New Chat (English)
# @name createChatEn
POST {{baseUrl}}/chat
Content-Type: application/json
Authorization: Bearer {{loginEn.response.body.data.token}}
Accept-Language: en

{
  "message": "Hello, I need help with AI course"
}

###

### Get Chat History (English)
GET {{baseUrl}}/chat/history?page=1&limit=10
Authorization: Bearer {{loginEn.response.body.data.token}}
Accept-Language: en

###

### Get Chat Details (English)
GET {{baseUrl}}/chat/{{createChatEn.response.body.data.chatId}}?page=1&limit=50
Authorization: Bearer {{loginEn.response.body.data.token}}
Accept-Language: en

### ========================================
### USERS
### ========================================

### Get All Users (Vietnamese)
GET {{baseUrl}}/users
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

###

### Get All Users (English)
GET {{baseUrl}}/users
Authorization: Bearer {{loginEn.response.body.data.token}}
Accept-Language: en

### ========================================
### LANGUAGE TESTING
### ========================================

### Test Invalid Login - Vietnamese
POST {{baseUrl}}/auth/login
Content-Type: application/json
Accept-Language: vi

{
  "email": "<EMAIL>",
  "password": "wrongpassword"
}

###

### Test Invalid Login - English
POST {{baseUrl}}/auth/login
Content-Type: application/json
Accept-Language: en

{
  "email": "<EMAIL>",
  "password": "wrongpassword"
}

###

### Test Query Parameter Language
GET {{baseUrl}}/chat/history?lang=en
Authorization: Bearer {{loginEn.response.body.data.token}}

###

### Test Custom Header Language
GET {{baseUrl}}/chat/history
Authorization: Bearer {{loginEn.response.body.data.token}}
X-Language: vi

### ========================================
### ERROR TESTING
### ========================================

### Test Missing Required Fields (Vietnamese)
POST {{baseUrl}}/auth/register
Content-Type: application/json
Accept-Language: vi

{
  "username": "test",
  "email": "<EMAIL>"
}

###

### Test Invalid Payment Amount (Vietnamese)
POST {{baseUrl}}/payment/create-deposit
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "amount": 500,
  "paymentMethod": "bank_transfer"
}

###

### Test Invalid Payment Method (Vietnamese)
POST {{baseUrl}}/payment/create-deposit
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "amount": 100000,
  "paymentMethod": "invalid_method"
}

###

### Test Missing Message (Vietnamese)
POST {{baseUrl}}/chat
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "chatId": "{{createChatVi.response.body.data.chatId}}"
}

###

### Test Invalid Chat ID (Vietnamese)
GET {{baseUrl}}/chat/invalid_chat_id
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

### ========================================
### PERFORMANCE TESTING
### ========================================

### Multiple Chat Messages (Vietnamese)
POST {{baseUrl}}/chat
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "message": "Tin nhắn test 1"
}

###

POST {{baseUrl}}/chat
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "message": "Tin nhắn test 2"
}

###

POST {{baseUrl}}/chat
Content-Type: application/json
Authorization: Bearer {{loginVi.response.body.data.token}}
Accept-Language: vi

{
  "message": "Tin nhắn test 3"
}

###

### Test Stream API with Authentication
POST {{baseUrl}}/chat/stream
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
    "message": "Xin chào, bạn có thể giúp tôi phân tích quảng cáo không?"
}

### Test Stream API with Authentication and Chat ID
POST {{baseUrl}}/chat/stream
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
    "message": "Tiếp tục cuộc trò chuyện",
    "chatId": "YOUR_CHAT_ID_HERE"
}

### Test Stream API with Invalid Token
POST {{baseUrl}}/chat/stream
Content-Type: application/json
Authorization: Bearer invalid_token

{
    "message": "Test message"
}

### Test Stream API without Token
POST {{baseUrl}}/chat/stream
Content-Type: application/json

{
    "message": "Test message"
}

### Test Stream API with Empty Message
POST {{baseUrl}}/chat/stream
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
    "message": ""
}

### Test Stream API with Long Message
POST {{baseUrl}}/chat/stream
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
    "message": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
}

### Test Stream API with Special Characters
POST {{baseUrl}}/chat/stream
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
    "message": "Test với các ký tự đặc biệt: !@#$%^&*()_+{}|:\"<>?[]\\;',./~`"
}

### Test Stream API with Emojis
POST {{baseUrl}}/chat/stream
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
    "message": "Test với emoji 😊 🎉 🚀"
}

### Test Stream API with Markdown
POST {{baseUrl}}/chat/stream
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
    "message": "Test với markdown:\n# Heading 1\n## Heading 2\n- List item 1\n- List item 2\n\n**Bold text** and *italic text*"
}

### Test Stream API with Code
POST {{baseUrl}}/chat/stream
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
    "message": "Test với code:\n```javascript\nconst hello = 'world';\nconsole.log(hello);\n```"
}

### Test Stream API with URLs
POST {{baseUrl}}/chat/stream
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
    "message": "Test với URLs:\nhttps://example.com\nhttp://test.com/path?query=123"
}

### Test Stream API with Mixed Languages
POST {{baseUrl}}/chat/stream
Content-Type: application/json
Authorization: Bearer {{authToken}}

{
    "message": "Test với nhiều ngôn ngữ:\nTiếng Việt\nEnglish\n日本語\n한국어"
}
</rewritten_file> 