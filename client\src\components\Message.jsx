import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Bo<PERSON> } from 'lucide-react';
import ChartDisplay from './ChartDisplay';
import TypewriterText from './TypewriterText';
import StreamingText from './StreamingText';
import TypewriterStreamingText from './TypewriterStreamingText';
import ImageDisplay from './ImageDisplay';
import { parseContentWithImages } from '../utils/imageDetector';

// Simple Markdown Component
const MarkdownContent = ({ content }) => {  const renderMarkdown = (text) => {
    // Clean up any typing indicators and control characters that might be in the content
    const cleanText = text
      .replace(/▋+/g, '') // Remove typing indicators
      .replace(/C#\d+/g, '') // Remove C#1, C#2, etc.
      .replace(/\x1b\[[0-9;]*m/g, '') // Remove ANSI escape codes
      .trim();
    
    // Simple markdown parsing for common elements
    let html = cleanText
      // Headers
      .replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>')
      .replace(/^## (.*$)/gm, '<h2 class="text-xl font-bold mt-4 mb-3">$1</h2>')
      .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mt-4 mb-3">$1</h1>')
      // Bold and italic
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      // Code blocks
      .replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 p-3 rounded mt-2 mb-2 text-sm overflow-x-auto"><code>$1</code></pre>')
      .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-500 hover:underline" target="_blank" rel="noopener noreferrer">$1</a>')
      // Lists
      .replace(/^- (.*$)/gm, '<li class="ml-4">• $1</li>')
      .replace(/^(\d+)\. (.*$)/gm, '<li class="ml-4">$1. $2</li>')
      // Blockquotes
      .replace(/^> (.*$)/gm, '<blockquote class="border-l-4 border-gray-300 pl-4 py-2 my-2 bg-gray-50 italic">$1</blockquote>')
      // Line breaks
      .replace(/\n\n/g, '</p><p class="mb-2">')
      .replace(/\n/g, '<br/>');

    // Wrap in paragraph if not already wrapped
    if (!html.includes('<h1>') && !html.includes('<h2>') && !html.includes('<h3>') && !html.includes('<p>')) {
      html = `<p class="mb-2">${html}</p>`;
    }

    return html;
  };

  return (
    <div 
      className="markdown-content"
      dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
    />
  );
};

const Message = ({ message, enableTyping = true, enableStreaming = true }) => {
  const isUser = message.role === 'user';
  const isBot = message.role === 'assistant';
  const [parsedContent, setParsedContent] = useState({ text: '', images: [] });

  const { content, role, isStreaming, type = 'text', metadata, files, hasFiles, timestamp } = message;

  // Don't render if message has no content and is not streaming
  if (!content && !isStreaming) {
    return null;
  }

  const formatTime = (ts) => {
    if (!ts) return '';
    return new Date(ts).toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Parse content for images
  useEffect(() => {
    if (content) {
      const parsed = parseContentWithImages(content);
      setParsedContent(parsed);
    }
  }, [content]);  const renderContent = () => {
    // Don't clean content for streaming - keep original text but clean control characters
    const originalContent = content || '';
    const cleanContent = originalContent
      .replace(/▋+/g, '') // Remove typing indicators
      .replace(/C#\d+/g, '') // Remove C#1, C#2, etc.
      .replace(/\x1b\[[0-9;]*m/g, '') // Remove ANSI escape codes
      .trim();
    const cleanParsedText = (parsedContent?.text || '')
      .replace(/▋+/g, '') // Remove typing indicators
      .replace(/C#\d+/g, '') // Remove C#1, C#2, etc.
      .replace(/\x1b\[[0-9;]*m/g, '') // Remove ANSI escape codes
      .trim();
    
    if (isBot && isStreaming) {
      // Use TypewriterStreamingText for streaming effect with cursor
      return (
        <TypewriterStreamingText
          text={originalContent || 'Đang phản hồi...'}
          speed={30}
          showCursor={true}
          cursorChar="|"
          className="streaming-content"
        />
      );
    } else if (type === 'markdown' || (isBot && !isStreaming)) {
      // For completed bot messages, always use MarkdownContent (no streaming animation)
      return <MarkdownContent content={cleanParsedText || cleanContent} />;
    } else if (type === 'chart') {
      try {
        const chartData = typeof cleanContent === 'string' ? JSON.parse(cleanContent) : cleanContent;
        return <ChartDisplay data={chartData} />;
      } catch (e) {
        console.error("Error parsing chart data:", e);
        return <p className="text-red-500">Lỗi hiển thị biểu đồ.</p>;
      }
    } else if (parsedContent?.images?.length > 0 && !isUser) {
        return (
          <>
            {cleanParsedText && <p className="mb-2">{cleanParsedText}</p>}
            {parsedContent.images.map((img, idx) => (
              <ImageDisplay key={idx} src={img.url} alt={img.alt || `Uploaded Image ${idx + 1}`} />
            ))}
          </>
        );
    }
    // Default to plain text
    return <p>{cleanParsedText || cleanContent}</p>; 
  };

  const renderFiles = () => {
    if (!hasFiles || !files || files.length === 0) return null;
    return (
      <div className="mt-2 pt-2 border-t border-gray-200">
        <p className="text-xs font-medium text-gray-500 mb-1">Tệp đính kèm:</p>
        <ul className="space-y-1">
          {files.map((file, index) => (
            <li key={index} className="text-xs">
              <a 
                href={file.url} 
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-blue-600 hover:underline truncate block"
                title={file.name}
              >
                {file.name} ({file.size ? (file.size / 1024).toFixed(1) + ' KB' : 'N/A'})
              </a>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className={`
      flex gap-2 sm:gap-3 p-2 sm:p-4
      ${isUser ? 'justify-end' : 'justify-start'}
      animate-fade-in-up message-container
      ${role} ${isStreaming ? 'streaming' : ''} // Added for CSS targeting from chatstream.md
    `}>
      {/* Avatar */}
      {isBot && (
        <div className="flex-shrink-0">
          <div className="w-6 h-6 sm:w-8 sm:h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <Bot className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
          </div>
        </div>
      )}

      {/* Message Bubble */}
      <div className={`
        max-w-xs sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl
        p-3 sm:p-4 rounded-xl shadow-sm message-bubble
        ${isUser ? 'bg-blue-500 text-white rounded-br-none' : 'bg-gray-100 text-gray-800 rounded-bl-none'}
      `}>
        <div className="message-content">
          {renderContent()}
        </div>
        {renderFiles()} 
        <div className="text-xs mt-2 flex justify-between items-center message-metadata">
          <span className={`${isUser ? 'text-blue-200' : 'text-gray-500'}`}>
            {formatTime(timestamp || metadata?.timestamp)} 
          </span>
           {isBot && !isStreaming && metadata?.completedAt && (
            <span className="text-gray-400 text-xxs ml-2">Hoàn tất</span>
          )}
        </div>
      </div>

      {/* User Avatar */}
      {isUser && (
        <div className="flex-shrink-0">
          <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
          </div>
        </div>
      )}
    </div>
  );
};

export default Message;

// TODO: Add back memo optimization later after fixing streaming issues

// Memoization to prevent unnecessary re-renders of completed messages

// Basic CSS for streaming effects (can be moved to a .css file)
// Ensure these are added to your global CSS or a relevant CSS module if not already present
/*
.message.streaming .message-content {
  opacity: 0.9; // Example: slightly transparent while streaming
}

.streaming-content .streaming-cursor {
  animation: blink 1s step-end infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.message-metadata {
  font-size: 0.75em; // Adjusted for potentially smaller text
  color: #666;
  margin-top: 4px;
}
*/
