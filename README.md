# AI Chatbot Monorepo

A modern chatbot application with React frontend and Express backend that integrates with Langflow for AI-powered conversations.

## 🚀 Quick Start (1 Command)

```bash
git clone <repository-url> && cd imta-ai && npm start
```

**Chỉ cần 1 lệnh duy nhất!** Script sẽ tự động:
- ✅ Kiểm tra và khởi động Docker
- ✅ Khởi động MongoDB, Redis, MinIO
- ✅ Cài đặt dependencies
- ✅ Khởi động server và client

**Kết quả**: http://localhost:5173 (Frontend) + http://localhost:5001 (Backend)

---

## 🚀 Features

- **Modern UI**: Beautiful, responsive chat interface with smooth animations
- **Real-time Chat**: Instant message sending and receiving with typing indicators
- **Langflow Integration**: Seamless integration with Langflow API for AI responses
- **Chat History**: Persistent chat history with clear functionality
- **Error Handling**: Robust error handling and user feedback
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Docker Support**: Complete Docker setup with MongoDB, Redis, and MinIO
- **Auto Setup**: Smart startup scripts that check and start required services
- **API Testing**: Complete REST Client setup for easy API testing and debugging
- **CI/CD Pipeline**: Automated deployment to production with GitHub Actions
- **Production Ready**: Optimized for production with SSL, monitoring, and backups

## 📁 Project Structure

```
chatbot-monorepo/
├── client/                 # React frontend
│   ├── public/            # Static files
│   ├── src/               # React source code
│   │   ├── components/    # React components
│   │   ├── services/      # API services
│   │   ├── App.js         # Main app component
│   │   └── index.js       # React entry point
│   └── package.json       # Frontend dependencies
├── server/                # Express backend
│   ├── routes/           # API routes
│   ├── models/           # MongoDB models
│   ├── middleware/       # Express middleware
│   ├── app.js           # Main server file
│   └── package.json     # Backend dependencies
├── shared/               # Shared utilities
│   └── utils.js         # Common helper functions
├── docker/               # Docker configuration files
│   ├── nginx/           # Nginx configurations
│   ├── mongodb/         # MongoDB configurations
│   └── redis/           # Redis configurations
├── .github/             # GitHub Actions workflows
│   └── workflows/       # CI/CD pipelines
├── docker-compose.yml    # Development Docker services
├── docker-compose.prod.yml # Production Docker services
├── Dockerfile.prod      # Production Dockerfile
├── start.sh             # Smart startup script
├── backup.sh            # Automated backup script
├── monitor.sh           # System monitoring script
├── package.json          # Root package.json with dev scripts
└── README.md            # This file
```

## 🛠️ Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Docker and Docker Compose
- Langflow instance running (for AI responses)

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chatbot-monorepo
   ```

2. **Set up environment variables**

   **Server Configuration:**
   ```bash
   cd server
   cp env.example .env
   ```
   
   Edit `server/.env` and configure:
   ```env
   PORT=5001
   MONGODB_URI=******************************************************
   LANGFLOW_API_URL=https://langflow.mecode.pro/api/v1/run/your-flow-id
   LANGFLOW_API_KEY=your-api-key-here
   ```

   **Client Configuration:**
   ```bash
   cd client
   cp env.example .env
   ```
   
   Edit `client/.env` and configure:
   ```env
   REACT_APP_API_URL=http://localhost:5001/api
   ```

3. **Install all dependencies**
   ```bash
   npm run install-all
   ```

## 🚀 Running the Application

### Quick Start (Recommended)

Use the smart startup script that automatically:
- Checks if Docker is running
- Starts required Docker containers (MongoDB, Redis, MinIO)
- Installs dependencies if needed
- Starts both server and client

```bash
npm start
```

### Docker Setup Only

If you only want to check and start Docker containers:

```bash
chmod +x check-docker.sh && ./check-docker.sh
```

### Manual Setup

1. **Start Docker containers:**
   ```bash
   docker-compose up -d mongodb redis minio
   ```

2. **Wait for containers to be ready (about 10-15 seconds)**

3. **Start the application:**
   ```bash
   npm run dev
   ```

### Development Mode

Run both frontend and backend simultaneously:
```bash
npm run dev
```

This will start:
- **Frontend**: http://localhost:5173
- **Backend**: http://localhost:5001
- **MongoDB**: localhost:27017
- **Redis**: localhost:6379
- **MinIO**: localhost:9000

### Individual Services

**Run only the backend:**
```bash
npm run server
```

**Run only the frontend:**
```bash
npm run client
```

**Run only the server (production mode):**
```bash
npm run start-server
```

### Production Build

1. **Build the React app:**
   ```bash
   npm run build
   ```

2. **Start the production server:**
   ```bash
   npm run start-server
   ```

## 🚀 CI/CD Deployment

### Automated Deployment

This project includes a complete CI/CD pipeline using GitHub Actions:

1. **Automatic Testing**: Runs tests on every push
2. **Production Deployment**: Automatically deploys to imta.ai on main branch
3. **Health Checks**: Verifies deployment success
4. **Monitoring**: Continuous system monitoring

### Manual Deployment

```bash
# Deploy to production server
chmod +x deploy-to-server.sh && ./deploy-to-server.sh
```

### Production URLs

- **Main Application**: https://imta.ai
- **API Health Check**: https://imta.ai/api/health
- **Mongo Express**: https://imta.ai:8081
- **Redis Commander**: https://imta.ai:8082
- **MinIO Console**: https://imta.ai:9001

### Monitoring & Maintenance

```bash
# Check system health
chmod +x monitor.sh && ./monitor.sh

# Create backup
chmod +x backup.sh && ./backup.sh

# View logs
docker-compose -f docker-compose.prod.yml logs -f
```

For detailed CI/CD setup instructions, see [CI_CD_SETUP.md](./CI_CD_SETUP.md)

## 🐳 Docker Services

The project includes the following Docker services:

- **MongoDB**: Database for storing users, chats, and messages
- **Redis**: Caching and session storage
- **MinIO**: S3-compatible object storage for file uploads
- **Mongo Express**: Web-based MongoDB admin interface (http://localhost:8081)
- **Redis Commander**: Web-based Redis admin interface (http://localhost:8082)

### Docker Management

**Start all services:**
```bash
docker-compose up -d
```

**Stop all services:**
```bash
docker-compose down
```

**View logs:**
```bash
docker-compose logs -f
```

**Rebuild services:**
```bash
docker-compose up -d --build
```

## 🔧 Development

### API Testing

The project includes comprehensive API testing setup:

1. **REST Client Files**: `.http` files for VS Code REST Client extension
2. **Postman Collection**: Complete API collection for Postman
3. **Automated Tests**: Node.js test scripts

### Code Structure

- **Frontend**: React with Vite for fast development
- **Backend**: Express.js with MongoDB, Redis, and MinIO
- **Authentication**: JWT-based authentication
- **Internationalization**: i18n support for Vietnamese and English
- **Payment Integration**: Sepay.vn payment gateway
- **Chat System**: Real-time chat with history

### Environment Variables

Key environment variables:

```env
# Server
NODE_ENV=development
PORT=5001
MONGODB_URI=mongodb://localhost:27017/imta-ai
REDIS_HOST=localhost
MINIO_ENDPOINT=localhost
JWT_SECRET=your-jwt-secret
LANGFLOW_API_URL=https://langflow.mecode.pro/api/v1/run/your-flow-id
LANGFLOW_API_KEY=your-api-key

# Client
REACT_APP_API_URL=http://localhost:5001/api
REACT_APP_ENV=development
```

## 📊 Monitoring & Analytics

- **Health Checks**: Automated health monitoring
- **Log Management**: Centralized logging with rotation
- **Performance Monitoring**: Resource usage tracking
- **Backup System**: Automated daily backups
- **SSL Monitoring**: Certificate expiry tracking

## 🔒 Security

- **HTTPS**: SSL/TLS encryption
- **JWT Authentication**: Secure token-based auth
- **Rate Limiting**: API rate limiting
- **Input Validation**: Comprehensive input sanitization
- **CORS**: Proper CORS configuration
- **Security Headers**: Security headers in Nginx

## 🚀 Performance

- **CDN Ready**: Static asset optimization
- **Caching**: Redis caching layer
- **Compression**: Gzip compression
- **Load Balancing**: Nginx load balancing
- **Database Optimization**: MongoDB indexing
- **Image Optimization**: MinIO for file storage

## 📞 Support

### Troubleshooting

1. **Check Docker status:**
   ```bash
   docker ps
   docker-compose ps
   ```

2. **View logs:**
   ```bash
   docker-compose logs -f [service-name]
   ```

3. **Health check:**
   ```bash
   curl http://localhost:5001/api/health
   ```

4. **Reset everything:**
   ```bash
   docker-compose down -v
   docker system prune -a
   npm run install-all
   ```

### Getting Help

- **Documentation**: Check the docs folder
- **Issues**: Create an issue on GitHub
- **Discussions**: Use GitHub Discussions
- **Email**: Contact the development team

---

**🎉 Ready to build amazing AI-powered conversations!**

## 📚 Documentation

- **[API Documentation](server/API_DOCUMENTATION.md)**: Complete API reference with examples
- **[REST Client Guide](server/REST_CLIENT_GUIDE.md)**: How to test APIs using REST Client extension
- **[Postman Collection](server/postman/)**: Ready-to-use Postman collection for API testing 