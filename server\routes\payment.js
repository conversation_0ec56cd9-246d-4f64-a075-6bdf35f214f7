const express = require('express');
const router = express.Router();
const axios = require('axios');
const crypto = require('crypto');
const { User, Payment } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { sendLocalizedSuccess, sendLocalizedError } = require('../utils/i18n');

// POST /api/payment/create-deposit - Tạo lệnh nạp tiền qua sepay.vn
router.post('/create-deposit', authenticateToken, async (req, res) => {
  try {
    const { amount, paymentMethod, description } = req.body;

    // Validation
    if (!amount || amount < 1000) {
      return sendLocalizedError(res, 400, 'payment.create.invalid_amount');
    }

    if (!paymentMethod || !['bank_transfer', 'credit_card', 'e_wallet', 'qr_code'].includes(paymentMethod)) {
      return sendLocalizedError(res, 400, 'payment.create.invalid_method');
    }

    // Create payment record
    const payment = new Payment({
      userId: req.user._id,
      amount: amount,
      paymentMethod: paymentMethod,
      description: description || req.t('payment.create.description', { user: req.user.fullName }),
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        referrer: req.get('Referer')
      }
    });

    await payment.save();

    // Prepare sepay.vn payment data
    const sepayData = {
      merchant_id: process.env.SEPAY_MERCHANT_ID,
      order_id: payment.transactionId,
      amount: amount,
      currency: 'VND',
      return_url: `${process.env.BASE_URL}/api/payment/callback`,
      cancel_url: `${process.env.BASE_URL}/api/payment/cancel`,
      notify_url: `${process.env.BASE_URL}/api/payment/webhook`,
      description: payment.description,
      customer_email: req.user.email,
      customer_name: req.user.fullName,
      customer_phone: req.user.phone || '',
      payment_method: paymentMethod
    };

    // Generate signature for sepay.vn
    const signature = generateSepaySignature(sepayData, process.env.SEPAY_SECRET_KEY);
    sepayData.signature = signature;

    // Call sepay.vn API
    const sepayResponse = await axios.post(process.env.SEPAY_API_URL, sepayData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (sepayResponse.data.success) {
      // Update payment with gateway transaction ID
      payment.gatewayTransactionId = sepayResponse.data.transaction_id;
      payment.gatewayResponse = sepayResponse.data;
      await payment.save();

      sendLocalizedSuccess(res, 200, 'payment.create.success', {
        payment: {
          id: payment._id,
          transactionId: payment.transactionId,
          amount: payment.amount,
          status: payment.status,
          paymentUrl: sepayResponse.data.payment_url,
          qrCode: sepayResponse.data.qr_code || null
        }
      });
    } else {
      // Mark payment as failed
      await payment.markAsFailed(sepayResponse.data.message || req.t('payment.create.sepay_error'));
      
      sendLocalizedError(res, 400, 'payment.create.failed');
    }

  } catch (error) {
    console.error('Create deposit error:', error);
    sendLocalizedError(res, 500, 'payment.create.failed');
  }
});

// GET /api/payment/callback - Callback từ sepay.vn sau khi thanh toán
router.get('/callback', async (req, res) => {
  try {
    const { order_id, status, signature } = req.query;

    if (!order_id || !status || !signature) {
      return sendLocalizedError(res, 400, 'payment.callback.missing_params');
    }

    // Verify signature
    const expectedSignature = generateSepaySignature(req.query, process.env.SEPAY_SECRET_KEY);
    if (signature !== expectedSignature) {
      return sendLocalizedError(res, 400, 'payment.callback.invalid_signature');
    }

    // Find payment
    const payment = await Payment.findOne({ transactionId: order_id });
    if (!payment) {
      return sendLocalizedError(res, 404, 'payment.callback.payment_not_found');
    }

    if (status === 'success') {
      // Update payment status
      await payment.markAsCompleted(req.query);
      
      // Update user balance
      const user = await User.findById(payment.userId);
      await user.updateBalance(payment.amount);
      
      // Add to user's payment history
      await user.addPaymentTransaction({
        transactionId: payment.transactionId,
        amount: payment.amount,
        type: 'deposit',
        status: 'completed',
        paymentMethod: payment.paymentMethod,
        description: payment.description,
        createdAt: new Date()
      });

      sendLocalizedSuccess(res, 200, 'payment.callback.success', {
        payment: {
          id: payment._id,
          transactionId: payment.transactionId,
          amount: payment.amount,
          status: payment.status
        }
      });
    } else {
      await payment.markAsFailed(req.t('payment.callback.failed_reason'));
      
      sendLocalizedError(res, 400, 'payment.callback.failed', {
        payment: {
          id: payment._id,
          transactionId: payment.transactionId,
          status: payment.status
        }
      });
    }

  } catch (error) {
    console.error('Payment callback error:', error);
    sendLocalizedError(res, 500, 'payment.callback.callback_failed');
  }
});

// POST /api/payment/webhook - Webhook từ sepay.vn
router.post('/webhook', async (req, res) => {
  try {
    const { order_id, status, signature } = req.body;

    if (!order_id || !status || !signature) {
      return sendLocalizedError(res, 400, 'payment.callback.missing_params');
    }

    // Verify signature
    const expectedSignature = generateSepaySignature(req.body, process.env.SEPAY_SECRET_KEY);
    if (signature !== expectedSignature) {
      return sendLocalizedError(res, 400, 'payment.callback.invalid_signature');
    }

    // Find payment
    const payment = await Payment.findOne({ transactionId: order_id });
    if (!payment) {
      return sendLocalizedError(res, 404, 'payment.callback.payment_not_found');
    }

    if (status === 'success' && payment.status === 'pending') {
      // Update payment status
      await payment.markAsCompleted(req.body);
      
      // Update user balance
      const user = await User.findById(payment.userId);
      await user.updateBalance(payment.amount);
      
      // Add to user's payment history
      await user.addPaymentTransaction({
        transactionId: payment.transactionId,
        amount: payment.amount,
        type: 'deposit',
        status: 'completed',
        paymentMethod: payment.paymentMethod,
        description: payment.description,
        createdAt: new Date()
      });
    } else if (status === 'failed' && payment.status === 'pending') {
      await payment.markAsFailed(req.t('payment.callback.failed_reason'));
    }

    res.json({ success: true });

  } catch (error) {
    console.error('Payment webhook error:', error);
    sendLocalizedError(res, 500, 'payment.webhook.processing_failed');
  }
});

// GET /api/payment/history - Lấy lịch sử thanh toán
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    
    const query = { userId: req.user._id };
    if (status) {
      query.status = status;
    }

    const payments = await Payment.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('userId', 'username email fullName');

    const total = await Payment.countDocuments(query);

    sendLocalizedSuccess(res, 200, null, {
      payments,
      pagination: {
        current: page,
        total: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Get payment history error:', error);
    sendLocalizedError(res, 500, 'payment.history.get_failed');
  }
});

// GET /api/payment/:id - Lấy chi tiết thanh toán
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const payment = await Payment.findOne({
      _id: req.params.id,
      userId: req.user._id
    }).populate('userId', 'username email fullName');

    if (!payment) {
      return sendLocalizedError(res, 404, 'payment.details.not_found');
    }

    sendLocalizedSuccess(res, 200, null, {
      payment
    });

  } catch (error) {
    console.error('Get payment details error:', error);
    sendLocalizedError(res, 500, 'payment.details.get_failed');
  }
});

// Helper function to generate sepay.vn signature
function generateSepaySignature(data, secretKey) {
  const sortedKeys = Object.keys(data).sort();
  const signString = sortedKeys
    .filter(key => key !== 'signature')
    .map(key => `${key}=${data[key]}`)
    .join('&');
  
  return crypto
    .createHmac('sha256', secretKey)
    .update(signString)
    .digest('hex');
}

module.exports = router; 