import React, { useState, useEffect } from 'react';
import StreamingText from './StreamingText';

const StreamingTestComponent = () => {
  const [testText, setTestText] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);

  const runTest = () => {
    console.log('🧪 Running StreamingText test');
    setTestText('');
    setIsStreaming(true);
    
    const fullText = 'Đây là test streaming text trong React component. Mỗi từ sẽ xuất hiện từ từ để kiểm tra animation có hoạt động không.';
    const words = fullText.split(' ');
    let currentText = '';
    let wordIndex = 0;
    
    const addWord = () => {
      if (wordIndex < words.length) {
        currentText += (wordIndex > 0 ? ' ' : '') + words[wordIndex];
        console.log(`🧪 Adding word ${wordIndex + 1}/${words.length}: "${words[wordIndex]}"`);
        setTestText(currentText);
        wordIndex++;
        setTimeout(addWord, 300);
      } else {
        console.log('🧪 Test streaming complete');
        setIsStreaming(false);
      }
    };
    
    addWord();
  };

  return (
    <div style={{ 
      padding: '20px', 
      border: '2px solid #3b82f6', 
      borderRadius: '8px', 
      margin: '20px',
      backgroundColor: '#f8fafc' 
    }}>
      <h3>🧪 StreamingText Component Test</h3>
      
      <div style={{ 
        minHeight: '100px', 
        padding: '15px', 
        backgroundColor: 'white', 
        border: '1px solid #e2e8f0',
        borderRadius: '6px',
        marginBottom: '15px'
      }}>
        <StreamingText
          text={testText}
          speed={80}
          showCursor={true}
          cursorChar="▋"
          isStreaming={isStreaming}
          onComplete={() => {
            console.log('✅ StreamingText component test completed');
          }}
          className="test-streaming"
        />
      </div>
      
      <button 
        onClick={runTest}
        disabled={isStreaming}
        style={{
          backgroundColor: isStreaming ? '#9ca3af' : '#3b82f6',
          color: 'white',
          border: 'none',
          padding: '10px 20px',
          borderRadius: '6px',
          cursor: isStreaming ? 'not-allowed' : 'pointer'
        }}
      >
        {isStreaming ? '🔄 Testing...' : '🧪 Run Test'}
      </button>
      
      <div style={{ 
        marginTop: '10px', 
        fontSize: '12px', 
        color: '#64748b' 
      }}>
        Current text length: {testText.length} | Is streaming: {isStreaming ? 'Yes' : 'No'}
      </div>
    </div>
  );
};

export default StreamingTestComponent;
