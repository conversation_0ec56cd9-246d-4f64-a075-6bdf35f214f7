#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Deploying IMTA AI to Production...${NC}"

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker is running${NC}"
}

# Function to create production environment files
setup_env() {
    echo -e "${BLUE}📝 Setting up environment files...${NC}"
    
    # Create server .env for production
    if [ ! -f "server/.env" ]; then
        echo -e "${YELLOW}Creating server/.env from env.example...${NC}"
        cp server/env.example server/.env
        echo -e "${GREEN}✅ Server environment file created${NC}"
    fi
    
    # Create client .env for production
    if [ ! -f "client/.env" ]; then
        echo -e "${YELLOW}Creating client/.env from env.example...${NC}"
        cp client/env.example client/.env
        echo -e "${GREEN}✅ Client environment file created${NC}"
    fi
}

# Function to stop existing containers
stop_existing() {
    echo -e "${BLUE}🛑 Stopping existing containers...${NC}"
    docker-compose -f docker-compose.prod.yml down --remove-orphans
    echo -e "${GREEN}✅ Existing containers stopped${NC}"
}

# Function to build and start production
deploy() {
    echo -e "${BLUE}🏗️  Building and starting production containers...${NC}"
    
    # Build and start all services
    docker-compose -f docker-compose.prod.yml up -d --build
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Production deployment successful!${NC}"
    else
        echo -e "${RED}❌ Deployment failed${NC}"
        exit 1
    fi
}

# Function to check deployment status
check_status() {
    echo -e "${BLUE}📊 Checking deployment status...${NC}"
    
    # Wait for services to be ready
    sleep 10
    
    # Check if containers are running
    if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
        echo -e "${GREEN}✅ All services are running${NC}"
        
        # Show container status
        echo -e "${BLUE}📋 Container Status:${NC}"
        docker-compose -f docker-compose.prod.yml ps
        
        # Show URLs
        echo -e "${BLUE}🌐 Access URLs:${NC}"
        echo -e "${GREEN}  • Application: http://localhost${NC}"
        echo -e "${GREEN}  • API Health: http://localhost/api/health${NC}"
        echo -e "${GREEN}  • Mongo Express: http://localhost:8081${NC}"
        echo -e "${GREEN}  • Redis Commander: http://localhost:8082${NC}"
        echo -e "${GREEN}  • MinIO Console: http://localhost:9001${NC}"
        
    else
        echo -e "${RED}❌ Some services failed to start${NC}"
        docker-compose -f docker-compose.prod.yml logs --tail=20
        exit 1
    fi
}

# Function to show logs
show_logs() {
    echo -e "${BLUE}📋 Recent logs:${NC}"
    docker-compose -f docker-compose.prod.yml logs --tail=10
}

# Main execution
main() {
    check_docker
    setup_env
    stop_existing
    deploy
    check_status
    show_logs
    
    echo -e "${GREEN}🎉 Production deployment completed!${NC}"
    echo -e "${BLUE}💡 Use 'docker-compose -f docker-compose.prod.yml logs -f' to monitor logs${NC}"
}

# Run main function
main 