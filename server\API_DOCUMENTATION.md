# API Documentation

## 🚀 Hướng dẫn sử dụng REST Client Extension

### Cài đặt REST Client Extension

1. **Mở VS Code**
2. **Cài đặt extension**: Tìm và cài đặt "REST Client" extension của <PERSON>ao <PERSON>
3. **Restart VS Code** nếu cần

### Cách sử dụng REST Client

#### 1. **Tạo file .http**
- Tạo file có đuôi `.http` (ví dụ: `test-api.http`)
- File này sẽ chứa tất cả các request để test API

#### 2. **Cú pháp cơ bản**
```http
### Tên request (tùy chọn)
METHOD URL
Headers: value
Content-Type: application/json

{
  "key": "value"
}
```

#### 3. **Variables và Environment**
```http
@baseUrl = http://localhost:5001/api
@token = your_jwt_token_here

### Login và lưu token
# @name login
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### Sử dụng token từ response trước
GET {{baseUrl}}/auth/me
Authorization: Bearer {{login.response.body.data.token}}
```

#### 4. **Các tính năng hữu ích**

**a) Chạy request:**
- Click "Send Request" phía trên mỗi request
- Hoặc dùng `Ctrl+Alt+R` (Windows/Linux) hoặc `Cmd+Alt+R` (Mac)

**b) Xem response:**
- Response sẽ hiển thị trong tab mới
- Có thể xem Headers, Body, Status code

**c) Variables:**
```http
@email = <EMAIL>
@password = password123

POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "{{email}}",
  "password": "{{password}}"
}
```

**d) Environment switching:**
```http
@dev = http://localhost:5001/api
@prod = https://api.example.com/api

### Chọn environment bằng cách comment/uncomment
# @baseUrl = {{dev}}
@baseUrl = {{prod}}
```

#### 5. **File test-api.http có sẵn**
Trong thư mục `server/` đã có file `test-api.http` với các request mẫu:
- Authentication (register, login, profile)
- Payment (create deposit, get history)
- Chat (send message, get history)
- Health check

### Cách sử dụng file test-api.http

1. **Mở file**: `server/test-api.http`
2. **Chạy từng request**: Click "Send Request" cho từng request
3. **Thay đổi dữ liệu**: Sửa email, password, amount theo nhu cầu
4. **Lưu token**: Sau khi login, copy token và paste vào biến `@token`

#### 6. **Tips và Tricks**

**a) Debugging:**
```http
### Test với dữ liệu không hợp lệ
POST {{baseUrl}}/auth/register
Content-Type: application/json

{
  "username": "ab",
  "email": "invalid-email",
  "password": "123",
  "fullName": "Test User"
}
```

**b) Headers tùy chỉnh:**
```http
### Test với ngôn ngữ khác
GET {{baseUrl}}/chat/history
Accept-Language: en
Authorization: Bearer {{token}}
```

**c) Query parameters:**
```http
### Pagination
GET {{baseUrl}}/chat/history?page=1&limit=10
Authorization: Bearer {{token}}
```

#### 7. **Troubleshooting**

**Lỗi thường gặp:**
- **Connection refused**: Kiểm tra server có đang chạy không
- **401 Unauthorized**: Kiểm tra token có hợp lệ không
- **400 Bad Request**: Kiểm tra dữ liệu gửi lên có đúng format không

**Debug steps:**
1. Chạy health check trước: `GET {{baseUrl}}/health`
2. Test login để lấy token mới
3. Kiểm tra response headers và body
4. Xem console logs của server

---

## Base URL
```
http://localhost:5001/api
```

## Internationalization (i18n)

API hỗ trợ đa ngôn ngữ với tiếng Việt làm ngôn ngữ mặc định. Có thể chuyển đổi ngôn ngữ bằng các cách sau:

### 1. Header Accept-Language
```
Accept-Language: vi
Accept-Language: en
```

### 2. Custom Header
```
X-Language: vi
X-Language: en
```

### 3. Query Parameter
```
GET /api/chat/history?lang=vi
GET /api/chat/history?language=en
```

### 4. User Preferences
Nếu user đã đăng nhập, API sẽ tự động sử dụng ngôn ngữ từ user preferences.

### Ngôn ngữ được hỗ trợ:
- `vi` - Tiếng Việt (mặc định)
- `en` - English

## Authentication
Most endpoints require authentication using JWT tokens. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

---

## Authentication Endpoints

### 1. Đăng ký tài khoản
**POST** `/auth/register`

**Request Body:**
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "John Doe",
  "phone": "0123456789"
}
```

**Response (Vietnamese):**
```json
{
  "success": true,
  "message": "Đăng ký tài khoản thành công",
  "data": {
    "user": {
      "id": "user_id",
      "username": "johndoe",
      "email": "<EMAIL>",
      "fullName": "John Doe",
      "avatar": null,
      "role": "student",
      "status": "active",
      "balance": 0
    },
    "token": "jwt_token_here"
  }
}
```

**Response (English):**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "user_id",
      "username": "johndoe",
      "email": "<EMAIL>",
      "fullName": "John Doe",
      "avatar": null,
      "role": "student",
      "status": "active",
      "balance": 0
    },
    "token": "jwt_token_here"
  }
}
```

### 2. Đăng nhập
**POST** `/auth/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (Vietnamese):**
```json
{
  "success": true,
  "message": "Đăng nhập thành công",
  "data": {
    "user": {
      "id": "user_id",
      "username": "johndoe",
      "email": "<EMAIL>",
      "fullName": "John Doe",
      "avatar": null,
      "role": "student",
      "status": "active",
      "balance": 0
    },
    "token": "jwt_token_here"
  }
}
```

### 3. Lấy thông tin tài khoản
**GET** `/auth/me`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "username": "johndoe",
      "email": "<EMAIL>",
      "fullName": "John Doe",
      "avatar": null,
      "role": "student",
      "status": "active",
      "balance": 0
    }
  }
}
```

### 4. Cập nhật thông tin tài khoản
**PUT** `/auth/me`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "fullName": "John Updated Doe",
  "phone": "0987654321",
  "avatar": "https://example.com/avatar.jpg",
  "preferences": {
    "language": "en",
    "theme": "dark"
  }
}
```

### 5. Đổi mật khẩu
**POST** `/auth/change-password`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword123"
}
```

---

## Payment Endpoints

### 1. Tạo lệnh nạp tiền
**POST** `/payment/create-deposit`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "amount": 100000,
  "paymentMethod": "bank_transfer",
  "description": "Nạp tiền vào tài khoản"
}
```

**Response (Vietnamese):**
```json
{
  "success": true,
  "message": "Tạo lệnh thanh toán thành công",
  "data": {
    "payment": {
      "id": "payment_id",
      "transactionId": "TXN_1234567890_ABC123",
      "amount": 100000,
      "status": "pending",
      "paymentUrl": "https://sepay.vn/payment/...",
      "qrCode": "data:image/png;base64,..."
    }
  }
}
```

### 2. Lấy lịch sử thanh toán
**GET** `/payment/history?page=1&limit=10&status=completed`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "payments": [
      {
        "id": "payment_id",
        "transactionId": "TXN_1234567890_ABC123",
        "amount": 100000,
        "status": "completed",
        "paymentMethod": "bank_transfer",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "current": 1,
      "total": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 3. Lấy chi tiết thanh toán
**GET** `/payment/:id`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "payment": {
      "id": "payment_id",
      "transactionId": "TXN_1234567890_ABC123",
      "amount": 100000,
      "status": "completed",
      "paymentMethod": "bank_transfer",
      "description": "Nạp tiền vào tài khoản",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "completedAt": "2024-01-01T00:05:00.000Z"
    }
  }
}
```

---

## Chat Endpoints

### 1. Gửi tin nhắn (tạo chat mới hoặc gửi vào chat hiện có)
**POST** `/chat`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "message": "Xin chào, tôi cần hỗ trợ",
  "chatId": "optional_chat_id"
}
```

**Response (Vietnamese):**
```json
{
  "success": true,
  "data": {
    "chatId": "chat_id",
    "response": "Xin chào! Tôi có thể giúp gì cho bạn?",
    "messageId": "message_id"
  }
}
```

### 2. Lấy lịch sử chat (danh sách các cuộc hội thoại)
**GET** `/chat/history?page=1&limit=10&status=active`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "chats": [
      {
        "id": "chat_id",
        "title": "Xin chào, tôi cần hỗ trợ...",
        "type": "general",
        "status": "active",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "lastMessageAt": "2024-01-01T00:05:00.000Z",
        "analytics": {
          "messageCount": 4,
          "creditUsed": 2
        }
      }
    ],
    "pagination": {
      "current": 1,
      "total": 3,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### 3. Lấy chi tiết chat (các tin nhắn trong cuộc hội thoại)
**GET** `/chat/:chatId?page=1&limit=50`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "chat": {
      "id": "chat_id",
      "title": "Xin chào, tôi cần hỗ trợ...",
      "type": "general",
      "status": "active"
    },
    "messages": [
      {
        "id": "message_id_1",
        "type": "user",
        "content": "Xin chào, tôi cần hỗ trợ",
        "timestamp": "2024-01-01T00:00:00.000Z",
        "userId": {
          "username": "johndoe",
          "fullName": "John Doe"
        }
      },
      {
        "id": "message_id_2",
        "type": "bot",
        "content": "Xin chào! Tôi có thể giúp gì cho bạn?",
        "timestamp": "2024-01-01T00:00:05.000Z"
      }
    ],
    "pagination": {
      "current": 1,
      "total": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### 4. Tạo chat mới
**POST** `/chat/create`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "title": "Hỗ trợ về khóa học",
  "type": "course_inquiry",
  "metadata": {
    "courseId": "course_id",
    "priority": "high"
  }
}
```

### 5. Gửi tin nhắn vào chat cụ thể
**POST** `/chat/:chatId/messages`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "message": "Tôi muốn hỏi về khóa học này"
}
```

### 6. Cập nhật thông tin chat
**PUT** `/chat/:chatId`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "title": "Hỗ trợ về khóa học - Đã cập nhật",
  "status": "completed"
}
```

### 7. Xóa chat
**DELETE** `/chat/:chatId`

**Headers:** `Authorization: Bearer <token>`

---

## User Endpoints

### 1. Lấy danh sách user (Admin only)
**GET** `/users`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user_id",
        "username": "johndoe",
        "email": "<EMAIL>",
        "fullName": "John Doe",
        "role": "student",
        "status": "active"
      }
    ]
  }
}
```

---

## Error Responses

All endpoints return consistent error responses with localized messages:

**Vietnamese Error:**
```json
{
  "success": false,
  "message": "Email hoặc mật khẩu không đúng"
}
```

**English Error:**
```json
{
  "success": false,
  "message": "Invalid email or password"
}
```

Common HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

---

## Health Check

**GET** `/api/health`

**Response:**
```json
{
  "status": "OK",
  "message": "Máy chủ đang hoạt động",
  "language": "vi"
}
```

---

## Environment Variables

Make sure to set up the following environment variables in your `.env` file:

```env
# Server Configuration
PORT=5001
NODE_ENV=development
BASE_URL=http://localhost:5001

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/imta-ai

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here

# Langflow API Configuration
LANGFLOW_API_URL=https://langflow.mecode.pro/api/v1/run/your-flow-id
LANGFLOW_API_KEY=your-api-key-here

# Sepay.vn Payment Gateway Configuration
SEPAY_API_URL=https://api.sepay.vn/v1/payment/create
SEPAY_MERCHANT_ID=your-merchant-id
SEPAY_SECRET_KEY=your-secret-key
```

---

## Language Examples

### Request với tiếng Việt:
```bash
curl -H "Accept-Language: vi" \
     -H "Authorization: Bearer <token>" \
     -X POST http://localhost:5001/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password"}'
```

### Request với tiếng Anh:
```bash
curl -H "Accept-Language: en" \
     -H "Authorization: Bearer <token>" \
     -X POST http://localhost:5001/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password"}'
```

### Request với query parameter:
```bash
curl -H "Authorization: Bearer <token>" \
     -X GET "http://localhost:5001/api/chat/history?lang=en" \
     -H "Content-Type: application/json"
``` 