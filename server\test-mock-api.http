### Test Mock Message API với Markdown Formatting

### 1. <PERSON><PERSON><PERSON> nhập để lấy token
POST http://localhost:3000/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### 2. Test greeting message
POST http://localhost:3000/api/chat/message
Authorization: Bearer YOUR_TOKEN_HERE
Content-Type: application/json

{
  "message": "Xin chào"
}

### 3. Test AI topic
POST http://localhost:3000/api/chat/message
Authorization: Bearer YOUR_TOKEN_HERE
Content-Type: application/json

{
  "message": "G<PERSON><PERSON><PERSON> thích về AI"
}

### 4. Test Machine Learning
POST http://localhost:3000/api/chat/message
Authorization: Bearer YOUR_TOKEN_HERE
Content-Type: application/json

{
  "message": "Machine Learning là gì?"
}

### 5. Test Chatbot
POST http://localhost:3000/api/chat/message
Authorization: Bearer YOUR_TOKEN_HERE
Content-Type: application/json

{
  "message": "Chatbot hoạt động như thế nào?"
}

### 6. Test general question
POST http://localhost:3000/api/chat/message
Authorization: Bearer YOUR_TOKEN_HERE
Content-Type: application/json

{
  "message": "React.js là gì?"
}

### 7. Test với chatId cụ thể
POST http://localhost:3000/api/chat/message
Authorization: Bearer YOUR_TOKEN_HERE
Content-Type: application/json

{
  "message": "Cảm ơn bạn",
  "chatId": "CHAT_ID_HERE"
}

### 8. Test goodbye
POST http://localhost:3000/api/chat/message
Authorization: Bearer YOUR_TOKEN_HERE
Content-Type: application/json

{
  "message": "Tạm biệt"
}

### 9. Lấy lịch sử chat
GET http://localhost:3000/api/chat/history
Authorization: Bearer YOUR_TOKEN_HERE

### 10. Lấy chi tiết chat
GET http://localhost:3000/api/chat/CHAT_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

### 11. Đăng xuất
POST http://localhost:3000/api/auth/logout
Authorization: Bearer YOUR_TOKEN_HERE

### Expected Response Format:
# Ví dụ response từ API /message:

```json
{
  "success": true,
  "message": "Message sent successfully",
  "data": {
    "chatId": "64f8a1b2c3d4e5f6a7b8c9d0",
    "response": "# 👋 Xin chào!\n\nRất vui được gặp bạn! Tôi là **AI Assistant** của hệ thống IMTA.\n\n## 🚀 Tôi có thể giúp bạn:\n\n- 💬 **Trò chuyện thông minh** - Hỏi đáp mọi chủ đề\n- 📚 **Học tập** - Giải thích kiến thức chi tiết  \n- 💡 **Tư vấn** - Đưa ra gợi ý và lời khuyên\n- 🛠️ **Hỗ trợ kỹ thuật** - Giải quyết vấn đề\n\nBạn muốn tôi giúp gì hôm nay? 😊",
    "messageId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "isMock": true,
    "format": "markdown"
  }
}
```

### Frontend Rendering Example:
```javascript
// Sử dụng thư viện markdown renderer như react-markdown
import ReactMarkdown from 'react-markdown';

function ChatMessage({ response, format }) {
  if (format === 'markdown') {
    return (
      <div className="chat-message">
        <ReactMarkdown>{response}</ReactMarkdown>
      </div>
    );
  }
  
  return (
    <div className="chat-message">
      <p>{response}</p>
    </div>
  );
}
```

### Streaming Example:
```javascript
// Giả lập streaming với markdown
async function streamMarkdownResponse(response) {
  const lines = response.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const partialResponse = lines.slice(0, i + 1).join('\n');
    
    // Update UI với partial response
    updateChatUI(partialResponse);
    
    // Simulate typing delay
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}
``` 