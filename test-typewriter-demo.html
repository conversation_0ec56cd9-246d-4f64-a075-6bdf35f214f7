<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typewriter Streaming Demo</title>
    <style>
        body {
            font-family: system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .message {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            align-items: flex-start;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .message-bubble {
            background: #f1f5f9;
            border-radius: 12px;
            border-bottom-left-radius: 4px;
            padding: 16px;
            max-width: 500px;
            color: #1f2937;
        }
        
        .typewriter-text {
            line-height: 1.6;
            white-space: pre-wrap;
            word-break: break-word;
        }
        
        .typewriter-cursor {
            display: inline-block;
            background-color: currentColor;
            width: 2px;
            margin-left: 1px;
            animation: typewriter-blink 1s infinite;
            opacity: 1;
        }
        
        @keyframes typewriter-blink {
            0%, 50% { 
                opacity: 1; 
            }
            51%, 100% { 
                opacity: 0; 
            }
        }
        
        .demo-controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 8px 16px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        .metadata {
            font-size: 12px;
            color: #6b7280;
            margin-top: 8px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Demo Hiệu Ứng Typewriter Streaming</h1>
        
        <div class="demo-controls">
            <button onclick="startDemo1()">Demo 1: Tin nhắn ngắn</button>
            <button onclick="startDemo2()">Demo 2: Tin nhắn dài</button>
            <button onclick="startDemo3()">Demo 3: Có xuống dòng</button>
            <button onclick="clearDemo()">Xóa</button>
        </div>
        
        <div id="messages-container"></div>
    </div>

    <script>
        class TypewriterEffect {
            constructor(element, text, speed = 50) {
                this.element = element;
                this.text = text;
                this.speed = speed;
                this.currentIndex = 0;
                this.isComplete = false;
                this.cursor = null;
            }
            
            start() {
                // Clear existing content
                this.element.innerHTML = '';
                this.currentIndex = 0;
                this.isComplete = false;
                
                // Create cursor
                this.cursor = document.createElement('span');
                this.cursor.className = 'typewriter-cursor';
                this.cursor.textContent = '|';
                this.element.appendChild(this.cursor);
                
                this.type();
            }
            
            type() {
                if (this.currentIndex < this.text.length) {
                    const char = this.text[this.currentIndex];
                    
                    // Insert character before cursor
                    const textNode = document.createTextNode(char);
                    this.element.insertBefore(textNode, this.cursor);
                    
                    this.currentIndex++;
                    setTimeout(() => this.type(), this.speed);
                } else {
                    this.isComplete = true;
                    // Hide cursor immediately when done
                    if (this.cursor && this.cursor.parentNode) {
                        this.cursor.remove();
                    }
                }
            }
        }
        
        function createMessage(text, speed = 30) {
            const container = document.getElementById('messages-container');
            
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            
            const avatar = document.createElement('div');
            avatar.className = 'avatar';
            avatar.textContent = 'AI';
            
            const bubble = document.createElement('div');
            bubble.className = 'message-bubble';
            
            const textDiv = document.createElement('div');
            textDiv.className = 'typewriter-text';
            
            const metadata = document.createElement('div');
            metadata.className = 'metadata';
            metadata.textContent = new Date().toLocaleTimeString('vi-VN', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            bubble.appendChild(textDiv);
            bubble.appendChild(metadata);
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(bubble);
            container.appendChild(messageDiv);
            
            // Start typewriter effect
            const typewriter = new TypewriterEffect(textDiv, text, speed);
            typewriter.start();
        }
        
        function startDemo1() {
            createMessage("Xin chào! Tôi có thể giúp gì cho bạn hôm nay?", 40);
        }
        
        function startDemo2() {
            createMessage("Đây là một tin nhắn dài hơn để demo hiệu ứng typewriter streaming. Bạn có thể thấy từng ký tự được hiển thị một cách từ từ, tạo ra hiệu ứng như đang gõ phím thật. Hiệu ứng này giúp tăng tính tương tác và hấp dẫn cho người dùng.", 25);
        }
        
        function startDemo3() {
            createMessage(`Tin nhắn có nhiều dòng:

1. Dòng đầu tiên
2. Dòng thứ hai
3. Dòng cuối cùng

Cảm ơn bạn đã xem demo!`, 35);
        }
        
        function clearDemo() {
            document.getElementById('messages-container').innerHTML = '';
        }
    </script>
</body>
</html>
