# 🔐 GitHub Actions Secrets Setup Guide

## 📋 Required Secrets

Để GitHub Actions workflow hoạt động, bạn cần thiết lập các secrets sau trong repository:

### 1. Vào GitHub Repository Settings
- Mở repository trên GitHub
- Vào **Settings** → **Secrets and variables** → **Actions**
- Click **New repository secret**

### 2. Thi<PERSON>t lập các Secrets

#### 🔑 SSH_PRIVATE_KEY
```
Name: SSH_PRIVATE_KEY
Value: [Nội dung private key SSH của server]
```
**Cách lấy:**
- Trên server: `cat ~/.ssh/id_rsa` (hoặc tên file key khác)
- Hoặc tạo key mới: `ssh-keygen -t rsa -b 4096 -C "<EMAIL>"`

#### 🌐 SERVER_HOST
```
Name: SERVER_HOST
Value: [IP hoặc domain của server]
```
**Ví dụ:** `************` hoặc `imta.ai`

#### 👤 SERVER_USER
```
Name: SERVER_USER
Value: [Username để SSH vào server]
```
**Ví dụ:** `root` hoặc `ubuntu`

#### 📁 PROJECT_PATH
```
Name: PROJECT_PATH
Value: [Đường dẫn thư mục project trên server]
```
**Ví dụ:** `/var/www/imta-ai` hoặc `/home/<USER>/imta-ai`

## 🔧 Thiết lập SSH Key trên Server

### 1. Tạo SSH Key (nếu chưa có)
```bash
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

### 2. Thêm Public Key vào authorized_keys
```bash
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
```

### 3. Copy Private Key cho GitHub
```bash
cat ~/.ssh/id_rsa
```
Copy toàn bộ output (bao gồm `-----BEGIN OPENSSH PRIVATE KEY-----` và `-----END OPENSSH PRIVATE KEY-----`)

## 🧪 Test SSH Connection

Trước khi chạy workflow, test kết nối SSH:

```bash
# Test từ máy local
ssh -i ~/.ssh/id_rsa username@server-ip

# Test từ GitHub Actions (sẽ được thực hiện tự động)
```

## 📝 Ví dụ Secrets Setup

| Secret Name | Example Value |
|-------------|---------------|
| `SSH_PRIVATE_KEY` | `-----BEGIN OPENSSH PRIVATE KEY-----\nb3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAlwAAAAdzc2gtcn\nNhAAAAAwEAAQAAAIEA...` |
| `SERVER_HOST` | `123.456.789.123` |
| `SERVER_USER` | `ubuntu` |
| `PROJECT_PATH` | `/var/www/imta-ai` |

## ⚠️ Lưu ý Bảo mật

1. **Không bao giờ commit secrets vào code**
2. **Sử dụng SSH key riêng cho GitHub Actions**
3. **Giới hạn quyền của SSH key trên server**
4. **Thường xuyên rotate SSH keys**

## 🔍 Troubleshooting

### Lỗi SSH Connection Failed
- Kiểm tra SERVER_HOST có đúng không
- Kiểm tra SERVER_USER có quyền SSH không
- Kiểm tra SSH_PRIVATE_KEY có đúng format không
- Kiểm tra firewall trên server

### Lỗi Permission Denied
- Kiểm tra quyền thư mục PROJECT_PATH
- Kiểm tra user có quyền sudo không
- Kiểm tra quyền thực thi scripts

### Lỗi Docker/Docker Compose
- Kiểm tra Docker đã được cài đặt trên server
- Kiểm tra docker-compose.yml có đúng syntax không
- Kiểm tra ports có bị conflict không

## 📞 Hỗ trợ

Nếu gặp lỗi, hãy:
1. Kiểm tra logs trong GitHub Actions
2. Kiểm tra logs trên server
3. Test từng bước thủ công trên server 