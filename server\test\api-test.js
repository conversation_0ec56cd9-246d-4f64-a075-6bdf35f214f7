const axios = require('axios');
const fs = require('fs');

// Configuration
const BASE_URL = 'http://localhost:5001/api';
const TEST_CONFIG = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// Test results storage
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

// Helper function to make requests
async function makeRequest(method, endpoint, data = null, headers = {}, language = 'vi') {
  try {
    const config = {
      ...TEST_CONFIG,
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        ...TEST_CONFIG.headers,
        'Accept-Language': language,
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status || 500 
    };
  }
}

// Test functions
async function testHealthCheck() {
  console.log('🔍 Testing Health Check...');
  
  // Test Vietnamese
  const viResult = await makeRequest('GET', '/health', null, {}, 'vi');
  if (viResult.success && viResult.data.message === 'Máy chủ đang hoạt động') {
    console.log('✅ Health Check (Vietnamese): PASSED');
    testResults.passed++;
  } else {
    console.log('❌ Health Check (Vietnamese): FAILED');
    testResults.failed++;
    testResults.errors.push('Health Check Vietnamese failed');
  }

  // Test English
  const enResult = await makeRequest('GET', '/health', null, {}, 'en');
  if (enResult.success && viResult.data.message === 'Server is running') {
    console.log('✅ Health Check (English): PASSED');
    testResults.passed++;
  } else {
    console.log('❌ Health Check (English): FAILED');
    testResults.failed++;
    testResults.errors.push('Health Check English failed');
  }
}

async function testAuthentication() {
  console.log('\n🔐 Testing Authentication...');
  
  // Test Registration
  const registerData = {
    username: `testuser_${Date.now()}`,
    email: `test_${Date.now()}@example.com`,
    password: 'password123',
    fullName: 'Test User',
    phone: '**********'
  };

  const registerResult = await makeRequest('POST', '/auth/register', registerData);
  if (registerResult.success && registerResult.data.success) {
    console.log('✅ User Registration: PASSED');
    testResults.passed++;
    
    const token = registerResult.data.data.token;
    
    // Test Login
    const loginData = {
      email: registerData.email,
      password: registerData.password
    };

    const loginResult = await makeRequest('POST', '/auth/login', loginData);
    if (loginResult.success && loginResult.data.success) {
      console.log('✅ User Login: PASSED');
      testResults.passed++;
      
      const loginToken = loginResult.data.data.token;
      
      // Test Get Profile
      const profileResult = await makeRequest('GET', '/auth/me', null, {
        'Authorization': `Bearer ${loginToken}`
      });
      
      if (profileResult.success && profileResult.data.success) {
        console.log('✅ Get Profile: PASSED');
        testResults.passed++;
      } else {
        console.log('❌ Get Profile: FAILED');
        testResults.failed++;
        testResults.errors.push('Get Profile failed');
      }
      
      return loginToken;
    } else {
      console.log('❌ User Login: FAILED');
      testResults.failed++;
      testResults.errors.push('User Login failed');
    }
  } else {
    console.log('❌ User Registration: FAILED');
    testResults.failed++;
    testResults.errors.push('User Registration failed');
  }
  
  return null;
}

async function testChat(token) {
  if (!token) {
    console.log('⚠️ Skipping Chat tests - no token available');
    return;
  }

  console.log('\n💬 Testing Chat...');
  
  const headers = {
    'Authorization': `Bearer ${token}`
  };

  // Test Send Message (Create New Chat)
  const messageData = {
    message: 'Xin chào, tôi cần hỗ trợ về khóa học'
  };

  const sendResult = await makeRequest('POST', '/chat', messageData, headers);
  if (sendResult.success && sendResult.data.success) {
    console.log('✅ Send Message (New Chat): PASSED');
    testResults.passed++;
    
    const chatId = sendResult.data.data.chatId;
    
    // Test Get Chat History
    const historyResult = await makeRequest('GET', '/chat/history?page=1&limit=10', null, headers);
    if (historyResult.success && historyResult.data.success) {
      console.log('✅ Get Chat History: PASSED');
      testResults.passed++;
    } else {
      console.log('❌ Get Chat History: FAILED');
      testResults.failed++;
      testResults.errors.push('Get Chat History failed');
    }
    
    // Test Get Chat Details
    const detailsResult = await makeRequest('GET', `/chat/${chatId}?page=1&limit=50`, null, headers);
    if (detailsResult.success && detailsResult.data.success) {
      console.log('✅ Get Chat Details: PASSED');
      testResults.passed++;
    } else {
      console.log('❌ Get Chat Details: FAILED');
      testResults.failed++;
      testResults.errors.push('Get Chat Details failed');
    }
    
    // Test Send Message to Existing Chat
    const existingMessageData = {
      message: 'Tôi muốn hỏi thêm về khóa học này',
      chatId: chatId
    };

    const existingResult = await makeRequest('POST', '/chat', existingMessageData, headers);
    if (existingResult.success && existingResult.data.success) {
      console.log('✅ Send Message (Existing Chat): PASSED');
      testResults.passed++;
    } else {
      console.log('❌ Send Message (Existing Chat): FAILED');
      testResults.failed++;
      testResults.errors.push('Send Message Existing Chat failed');
    }
  } else {
    console.log('❌ Send Message (New Chat): FAILED');
    testResults.failed++;
    testResults.errors.push('Send Message New Chat failed');
  }
}

async function testPayment(token) {
  if (!token) {
    console.log('⚠️ Skipping Payment tests - no token available');
    return;
  }

  console.log('\n💰 Testing Payment...');
  
  const headers = {
    'Authorization': `Bearer ${token}`
  };

  // Test Create Deposit
  const depositData = {
    amount: 100000,
    paymentMethod: 'bank_transfer',
    description: 'Nạp tiền test'
  };

  const depositResult = await makeRequest('POST', '/payment/create-deposit', depositData, headers);
  if (depositResult.success && depositResult.data.success) {
    console.log('✅ Create Deposit: PASSED');
    testResults.passed++;
    
    const paymentId = depositResult.data.data.payment.id;
    
    // Test Get Payment History
    const historyResult = await makeRequest('GET', '/payment/history?page=1&limit=10', null, headers);
    if (historyResult.success && historyResult.data.success) {
      console.log('✅ Get Payment History: PASSED');
      testResults.passed++;
    } else {
      console.log('❌ Get Payment History: FAILED');
      testResults.failed++;
      testResults.errors.push('Get Payment History failed');
    }
    
    // Test Get Payment Details
    const detailsResult = await makeRequest('GET', `/payment/${paymentId}`, null, headers);
    if (detailsResult.success && detailsResult.data.success) {
      console.log('✅ Get Payment Details: PASSED');
      testResults.passed++;
    } else {
      console.log('❌ Get Payment Details: FAILED');
      testResults.failed++;
      testResults.errors.push('Get Payment Details failed');
    }
  } else {
    console.log('❌ Create Deposit: FAILED');
    testResults.failed++;
    testResults.errors.push('Create Deposit failed');
  }
}

async function testErrorCases() {
  console.log('\n🚨 Testing Error Cases...');
  
  // Test Invalid Login
  const invalidLoginData = {
    email: '<EMAIL>',
    password: 'wrongpassword'
  };

  const invalidLoginResult = await makeRequest('POST', '/auth/login', invalidLoginData);
  if (!invalidLoginResult.success && invalidLoginResult.status === 401) {
    console.log('✅ Invalid Login Error: PASSED');
    testResults.passed++;
  } else {
    console.log('❌ Invalid Login Error: FAILED');
    testResults.failed++;
    testResults.errors.push('Invalid Login Error failed');
  }
  
  // Test Missing Required Fields
  const invalidRegisterData = {
    username: 'test',
    email: '<EMAIL>'
    // Missing password and fullName
  };

  const invalidRegisterResult = await makeRequest('POST', '/auth/register', invalidRegisterData);
  if (!invalidRegisterResult.success && invalidRegisterResult.status === 400) {
    console.log('✅ Missing Fields Error: PASSED');
    testResults.passed++;
  } else {
    console.log('❌ Missing Fields Error: FAILED');
    testResults.failed++;
    testResults.errors.push('Missing Fields Error failed');
  }
}

async function testLanguageSupport() {
  console.log('\n🌍 Testing Language Support...');
  
  // Test Vietnamese
  const viResult = await makeRequest('POST', '/auth/login', {
    email: '<EMAIL>',
    password: 'wrongpassword'
  }, {}, 'vi');
  
  if (!viResult.success && viResult.error?.message?.includes('Email hoặc mật khẩu')) {
    console.log('✅ Vietnamese Language: PASSED');
    testResults.passed++;
  } else {
    console.log('❌ Vietnamese Language: FAILED');
    testResults.failed++;
    testResults.errors.push('Vietnamese Language failed');
  }
  
  // Test English
  const enResult = await makeRequest('POST', '/auth/login', {
    email: '<EMAIL>',
    password: 'wrongpassword'
  }, {}, 'en');
  
  if (!enResult.success && enResult.error?.message?.includes('Invalid email or password')) {
    console.log('✅ English Language: PASSED');
    testResults.passed++;
  } else {
    console.log('❌ English Language: FAILED');
    testResults.failed++;
    testResults.errors.push('English Language failed');
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting API Tests...\n');
  
  try {
    await testHealthCheck();
    const token = await testAuthentication();
    await testChat(token);
    await testPayment(token);
    await testErrorCases();
    await testLanguageSupport();
    
    // Print results
    console.log('\n📊 Test Results:');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(2)}%`);
    
    if (testResults.errors.length > 0) {
      console.log('\n🚨 Errors:');
      testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    // Save results to file
    const resultsFile = `test-results-${new Date().toISOString().split('T')[0]}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(testResults, null, 2));
    console.log(`\n📄 Results saved to: ${resultsFile}`);
    
  } catch (error) {
    console.error('💥 Test runner error:', error.message);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  makeRequest,
  testResults
}; 