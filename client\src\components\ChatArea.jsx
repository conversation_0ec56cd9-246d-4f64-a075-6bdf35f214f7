import React, { useState, useRef, useEffect } from 'react';
import { Send, Loader2, Paperclip, X, AlertTriangle } from 'lucide-react';
import { useChat } from '../context/ChatContext';
import { useSidebar } from '../context/SidebarContext';
import { useCredit } from '../context/CreditContext';
import { useFileUpload } from '../context/FileUploadContext';
import Message from './Message';
import FileUpload from './FileUpload/FileUpload';
import EnhancedFileUpload from './EnhancedFileUpload';
import TypingIndicator from './TypingIndicator';
import { getChatTypeDisplayName, getChatTypeColor } from '../utils/chatAnalyzer';

const ChatArea = () => {  const {
    getCurrentConversation,
    sendMessage,
    isLoading,
    currentConversationId
  } = useChat();

  const { isMobile } = useSidebar();
  const { hasEnoughCredits, consumeCredits, creditBalance } = useCredit();
  const { validateFile, formatFileSize } = useFileUpload();

  const [inputValue, setInputValue] = useState('');
  const [attachedFiles, setAttachedFiles] = useState([]);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [showEnhancedUpload, setShowEnhancedUpload] = useState(false);
  const [creditError, setCreditError] = useState(null);
  const [chatTypeNotification, setChatTypeNotification] = useState(null);
  const messagesEndRef = useRef(null);
  const textareaRef = useRef(null);

  const currentConversation = getCurrentConversation();

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      // Use requestAnimationFrame to ensure DOM is updated
      requestAnimationFrame(() => {
        messagesEndRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
          inline: 'nearest'
        });
      });
    }
  }, [currentConversation?.messages, isLoading]);

  // Auto resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  }, [inputValue]);

  // Show notification when a new typed chat is created
  useEffect(() => {
    if (currentConversation?.type && currentConversation.type !== 'general' && currentConversation.messages.length === 0) {
      setChatTypeNotification({
        type: currentConversation.type,
        displayName: getChatTypeDisplayName(currentConversation.type)
      });
      
      // Auto-hide after 3 seconds
      const timer = setTimeout(() => {
        setChatTypeNotification(null);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [currentConversation?.type, currentConversation?.messages.length]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if ((!inputValue.trim() && attachedFiles.length === 0) || isLoading) return;

    // Check credit requirements
    const messageCredits = 1; // 1 credit per message
    const fileCredits = attachedFiles.length * 2; // 2 credits per file
    const totalCredits = messageCredits + fileCredits;

    if (!hasEnoughCredits('message')) {
      setCreditError('Không đủ credit để gửi tin nhắn. Vui lòng nạp thêm credit.');
      return;
    }

    if (attachedFiles.length > 0 && creditBalance < totalCredits) {
      setCreditError(`Cần ${totalCredits} credits để gửi tin nhắn với ${attachedFiles.length} file. Số dư hiện tại: ${creditBalance} credits.`);
      return;
    }

    const message = inputValue.trim();
    const files = [...attachedFiles];

    // Clear input and files
    setInputValue('');
    setAttachedFiles([]);
    setShowFileUpload(false);
    setCreditError(null);

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }

    try {
      // Consume credits before sending message
      await consumeCredits('message', 'Gửi tin nhắn');

      // Consume credits for file uploads
      for (let i = 0; i < files.length; i++) {
        await consumeCredits('fileUpload', `Upload file: ${files[i].name}`);
      }

      // Send message with files
      await sendMessage(message, files);
    } catch (error) {
      console.error('Error sending message:', error);
      if (error.message.includes('credit')) {
        setCreditError(error.message);
      }
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // File upload handlers
  const handleFileSelect = (fileData) => {
    setAttachedFiles(prev => [...prev, fileData]);
  };

  const handleFileRemove = (fileData) => {
    setAttachedFiles(prev => prev.filter(f => f.id !== fileData.id));
  };

  const toggleFileUpload = () => {
    setShowFileUpload(!showFileUpload);
  };

  const renderWelcomeScreen = () => (
    <div className="h-full overflow-y-auto">
      <div className="min-h-full flex items-center justify-center p-4">
        <div className="text-center max-w-lg mx-auto animate-fade-in-up">
          <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-6">
            <img
              src="/src/assets/imta.png"
              alt="IMTA AI"
              className="w-full h-full object-contain"
            />
          </div>
          <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">
            Chào mừng đến với IMTA AI
          </h2>
          <p className="text-sm sm:text-base text-gray-600 mb-6">
            Hãy bắt đầu cuộc trò chuyện bằng cách gửi tin nhắn đầu tiên của bạn.
            <br />
            <span className="text-blue-600 font-medium">✨ AI sẽ tự động nhận diện loại cuộc trò chuyện phù hợp!</span>
          </p>
          
          {/* Extra padding at bottom */}
          <div className="h-20 sm:h-24" />
        </div>
      </div>
    </div>
  );

  const renderMessages = () => (
    <div className="h-full overflow-y-auto scroll-smooth">
      <div className="max-w-4xl mx-auto px-3 sm:px-6 py-4">
        {currentConversation?.messages.map((message, index) => (
          <Message
            key={message.id}
            message={message}
            enableTyping={false}
            enableStreaming={index === currentConversation.messages.length - 1 && message.role === 'assistant'}
          />
        ))}
        {isLoading && (
          <TypingIndicator show={true} message="đang tạo phản hồi..." />
        )}
        {/* Extra padding at bottom to ensure last message is visible above input */}
        <div className="h-20 sm:h-24" />
        <div ref={messagesEndRef} />
      </div>
    </div>
  );  return (
    <div className="flex-1 flex flex-col bg-white relative">
      {/* Chat Type Notification */}
      {chatTypeNotification && (
        <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 animate-fade-in-up">
          <div className={`px-4 py-2 rounded-lg shadow-lg border ${getChatTypeColor(chatTypeNotification.type)}`}>
            <p className="text-sm font-medium">
              🎯 Đã tạo cuộc trò chuyện: {chatTypeNotification.displayName}
            </p>
          </div>
        </div>
      )}

      {/* Messages area - with proper scrolling */}
      <div className="flex-1 relative">
        <div className={`absolute inset-0 ${isMobile ? 'pb-20' : 'pb-16 sm:pb-20'}`}>
          {!currentConversationId || !currentConversation?.messages.length ?
            renderWelcomeScreen() :
            renderMessages()
          }
        </div>
      </div>

      {/* Input area - Fixed at bottom with higher z-index for mobile */}
      <div className={`
        fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200
        ${isMobile ? 'p-3 z-40' : 'p-2 sm:p-4 z-10'}
        ${isMobile ? 'sm:left-0' : 'sm:left-64'}
      `}>
        <div className="max-w-4xl mx-auto px-3 sm:px-6">
          {/* File Upload Area */}
          {showFileUpload && (
            <div className="mb-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-gray-700">Upload Files</h3>
                <button
                  onClick={toggleFileUpload}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              <FileUpload
                onFileSelect={handleFileSelect}
                onFileRemove={handleFileRemove}
                disabled={isLoading}
                maxFiles={5}
              />
            </div>
          )}

          {/* Credit Error */}
          {creditError && (
            <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-700">
                <AlertTriangle className="w-4 h-4" />
                <span className="text-sm">{creditError}</span>
              </div>
            </div>
          )}

          {/* Attached Files Preview */}
          {attachedFiles.length > 0 && !showFileUpload && (
            <div className="mb-3 flex flex-wrap gap-2">
              {attachedFiles.map((fileData) => (
                <div
                  key={fileData.id}
                  className="flex items-center gap-2 bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm"
                >
                  <span className="truncate max-w-[100px]">{fileData.name}</span>
                  <button
                    onClick={() => handleFileRemove(fileData)}
                    className="text-blue-500 hover:text-blue-700"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          )}

          <form onSubmit={handleSubmit} className="flex gap-2 sm:gap-3">
            {/* File Upload Button */}
            <button
              type="button"
              onClick={() => setShowEnhancedUpload(true)}
              className={`
                flex-shrink-0 p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50
                rounded-lg transition-colors
                ${isMobile ? 'min-w-[48px] min-h-[48px]' : ''}
              `}
              title="Upload files"
            >
              <Paperclip className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4 sm:w-5 sm:h-5'}`} />
            </button>

            <div className="flex-1 relative">
              <textarea
                ref={textareaRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Nhập tin nhắn của bạn..."
                className={`
                  w-full border border-gray-300 rounded-lg resize-none
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                  disabled:opacity-50 disabled:cursor-not-allowed
                  ${isMobile
                    ? 'px-3 py-3 text-base min-h-[48px] max-h-32'
                    : 'px-3 py-2 sm:px-4 sm:py-3 text-sm sm:text-base min-h-[40px] sm:min-h-[50px] max-h-24 sm:max-h-32'
                  }
                `}
                rows={1}
                disabled={isLoading}
              />
            </div>            <button
              type="submit"
              disabled={(!inputValue.trim() && attachedFiles.length === 0) || isLoading}
              className={`
                bg-blue-500 text-white rounded-lg hover:bg-blue-600
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex-shrink-0
                ${isMobile
                  ? 'px-4 py-3 min-w-[48px] min-h-[48px]'
                  : 'px-3 py-2 sm:px-4 sm:py-3'
                }
              `}
            >
              {isLoading ? (
                <Loader2 className={`animate-spin ${isMobile ? 'w-5 h-5' : 'w-4 h-4 sm:w-5 sm:h-5'}`} />
              ) : (
                <Send className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4 sm:w-5 sm:h-5'}`} />
              )}
            </button>
          </form>
          {!isMobile && (
            <div className="mt-1 sm:mt-2 text-xs text-gray-500 text-center hidden sm:block">
              Nhấn Enter để gửi, Shift + Enter để xuống dòng
            </div>
          )}
        </div>
      </div>

      {/* Enhanced File Upload Modal */}
      <EnhancedFileUpload
        isOpen={showEnhancedUpload}
        onClose={() => setShowEnhancedUpload(false)}
        onFilesUploaded={(files) => {
          // Convert uploaded files to attached files format
          const convertedFiles = files.map(file => ({
            id: file.id,
            name: file.name,
            size: file.size,
            type: file.type,
            url: file.url
          }));
          setAttachedFiles(prev => [...prev, ...convertedFiles]);
          setShowEnhancedUpload(false);
        }}
        maxFiles={5}
        allowMultiple={true}
      />
      
    </div>
  );
};

export default ChatArea;
