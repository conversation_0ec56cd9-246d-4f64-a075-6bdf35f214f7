# Tài liệu Thay đổi Client cho Chat Streaming

## <PERSON><PERSON><PERSON>
- [1. Thay đổi trong API Utils](#1-thay-đổi-trong-api-utils)
- [2. Thay đổi trong Chat Context](#2-thay-đổi-trong-chat-context)
- [3. Thay đổi trong Message Component](#3-thay-đổi-trong-message-component)
- [4. Tóm tắt các cải tiến](#4-tóm-tắt-các-cải-tiến)
- [5. Hướng dẫn Test](#5-hướng-dẫn-test)

## 1. Thay đổi trong API Utils

### 1.1. Hàm `streamMessage` (`client/src/utils/api.js`)

#### Code cũ
```javascript
async streamMessage(messageData, onChunk, onComplete, onError) {
  try {
    const response = await fetch(`${API_BASE_URL}/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      },
      body: JSON.stringify(messageData)
    });

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = JSON.parse(line.slice(6));
          if (data.error) {
            onError(data.error);
            return;
          }
          if (data.done) {
            onComplete(data);
            return;
          }
          onChunk(data.chunk);
        }
      }
    }
  } catch (error) {
    onError(error.message);
  }
}
```

#### Code mới
```javascript
async streamMessage(messageData, onChunk, onComplete, onError) {
  try {
    console.log('Starting stream with data:', messageData);
    
    const response = await fetch(`${API_BASE_URL}/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      },
      body: JSON.stringify(messageData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    console.log('Stream started, reading response...');
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let chunkCount = 0;
    let lastUpdateTime = Date.now();
    const MIN_UPDATE_DELAY = 16; // ~60fps

    const processChunk = (text, metadata) => {
      const now = Date.now();
      const timeSinceLastUpdate = now - lastUpdateTime;
      
      if (timeSinceLastUpdate < MIN_UPDATE_DELAY) {
        setTimeout(() => {
          onChunk(text, metadata);
          lastUpdateTime = Date.now();
        }, MIN_UPDATE_DELAY - timeSinceLastUpdate);
      } else {
        onChunk(text, metadata);
        lastUpdateTime = now;
      }
    };

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('Stream reading done');
        if (buffer.trim()) {
          try {
            const data = JSON.parse(buffer);
            console.log('Final buffer data:', data);
            if (data.done) {
              onComplete(data);
            }
          } catch (e) {
            console.error('Error parsing final buffer:', e);
          }
        }
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      console.log('Received raw chunk:', chunk);
      buffer += chunk;

      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim().startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            console.log('Parsed chunk data:', data);
            
            if (data.error) {
              console.error('Stream error:', data.error);
              onError(data.error);
              return;
            }
            
            if (data.done) {
              console.log('Stream complete');
              onComplete(data);
              return;
            }

            if (data.chunk) {
              chunkCount++;
              console.log(`Processing chunk ${chunkCount}:`, data.chunk);
              processChunk(data.chunk, {
                chunkNumber: data.chunkNumber,
                timestamp: data.timestamp
              });
            }
          } catch (e) {
            console.error('Error parsing SSE data:', e, line);
          }
        }
      }
    }
  } catch (error) {
    console.error('Stream error:', error);
    onError(error.message);
  }
}
```

#### Thay đổi chính trong API Utils
1. Thêm logging chi tiết
2. Thêm buffer để xử lý chunks không đầy đủ
3. Thêm `processChunk` để kiểm soát tốc độ update UI
4. Thêm metadata cho mỗi chunk
5. Cải thiện xử lý lỗi
6. Thêm validation cho response

## 2. Thay đổi trong Chat Context

### 2.1. Hàm `sendMessage` (`client/src/context/ChatContext.jsx`)

#### Code cũ
```javascript
sendMessage: async (content, files = []) => {
  // ... code cũ ...
  try {
    await chatAPI.streamMessage(
      { message: content, chatId: backendChatId },
      (chunk) => {
        dispatch({
          type: actionTypes.UPDATE_MESSAGE,
          payload: {
            conversationId,
            messageId: tempBotMessage.id,
            updates: {
              content: (prev) => prev + chunk,
              isStreaming: true
            }
          }
        });
      },
      // ... callbacks khác ...
    );
  } catch (error) {
    // ... error handling ...
  }
}
```

#### Code mới
```javascript
sendMessage: async (content, files = []) => {
  console.log(' sendMessage called with:', { content, files: files.length });
  
  // ... code khác giữ nguyên ...

  try {
    console.log('Starting stream with:', { content, chatId: backendChatId });
    
    await chatAPI.streamMessage(
      {
        message: content,
        chatId: backendChatId
      },
      // On chunk received
      (chunk, metadata) => {
        console.log('Received chunk:', chunk, 'Metadata:', metadata);
        dispatch({
          type: actionTypes.UPDATE_MESSAGE,
          payload: {
            conversationId,
            messageId: tempBotMessage.id,
            updates: (prev) => ({
              ...prev,
              content: prev.content + chunk,
              isStreaming: true,
              metadata: {
                ...prev.metadata,
                ...metadata
              }
            })
          }
        });
      },
      // On complete
      (data) => {
        console.log('Stream complete:', data);
        // ... code xử lý complete ...
      },
      // On error
      (error) => {
        console.error('Streaming error:', error);
        // ... code xử lý error ...
      }
    );
  } catch (error) {
    console.error('Error sending message:', error);
    // ... error handling ...
  }
}
```

#### Thay đổi chính trong Chat Context
1. Thêm logging chi tiết
2. Sử dụng hàm updater cho state updates
3. Thêm xử lý metadata
4. Cải thiện error handling

## 3. Thay đổi trong Message Component

### 3.1. Component Message (`client/src/components/Message.jsx`)

#### Code cũ
```javascript
const Message = ({ message }) => {
  const { content, role, isStreaming } = message;
  return (
    <div className={`message ${role} ${isStreaming ? 'streaming' : ''}`}>
      <div className="message-content">
        {content}
        {isStreaming && <span className="typing-indicator">▋</span>}
      </div>
    </div>
  );
};
```

#### Code mới
```javascript
const Message = ({ message }) => {
  const { content, role, isStreaming, type = 'text', metadata } = message;
  
  return (
    <div className={`message ${role} ${isStreaming ? 'streaming' : ''}`}>
      <div className="message-content">
        {type === 'markdown' ? (
          <ReactMarkdown>{content}</ReactMarkdown>
        ) : (
          <div className="text-content">{content}</div>
        )}
        {isStreaming && (
          <span className="typing-indicator" title={`Chunk ${metadata?.chunkNumber || '?'}`}>
            ▋
          </span>
        )}
      </div>
      {metadata && (
        <div className="message-metadata">
          <small>{new Date(metadata.timestamp).toLocaleTimeString()}</small>
        </div>
      )}
    </div>
  );
};
```

### 3.2. CSS mới (`client/src/styles/Message.css`)
```css
.message {
  transition: all 0.2s ease-in-out;
}

.message.streaming {
  opacity: 0.8;
}

.message-content {
  transition: all 0.2s ease-in-out;
}

.typing-indicator {
  display: inline-block;
  animation: blink 1s step-end infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.message-metadata {
  font-size: 0.8em;
  color: #666;
  margin-top: 4px;
}
```

#### Thay đổi chính trong Message Component
1. Thêm support cho markdown
2. Thêm hiệu ứng animation cho typing indicator
3. Thêm hiển thị metadata
4. Thêm transitions cho UI mượt mà hơn

## 4. Tóm tắt các cải tiến

### 4.1. Hiệu năng
- Thêm buffer để xử lý chunks không đầy đủ
- Kiểm soát tốc độ update UI
- Tối ưu re-renders

### 4.2. UX
- Animation mượt mà hơn
- Typing indicator trực quan
- Hiển thị metadata
- Transitions mượt mà

### 4.3. Debug
- Logging chi tiết
- Error handling tốt hơn
- Metadata tracking

### 4.4. Tính năng
- Support markdown
- Streaming mượt mà
- Metadata tracking
- Error recovery

## 5. Hướng dẫn Test

### 5.1. Test UI
```javascript
// Gửi tin nhắn test
const testMessage = "Đây là tin nhắn test dài để kiểm tra streaming. " + 
                   "Tin nhắn này sẽ được chia thành nhiều chunks nhỏ. " +
                   "Chúng ta sẽ thấy text xuất hiện từng chút một.";
```

### 5.2. Test Console
1. Mở Developer Tools (F12)
2. Chuyển đến tab Console
3. Quan sát logs:
   - Chunks nhận được
   - Metadata
   - Timing
   - Errors

### 5.3. Test Error Cases
- Mất kết nối
- Token hết hạn
- Server error
- Invalid data

### 5.4. Test Performance
- Tin nhắn dài
- Nhiều tin nhắn liên tiếp
- Nhiều tab/window
