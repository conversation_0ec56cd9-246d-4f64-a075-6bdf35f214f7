import React, { useState } from 'react';
import { AlertTriangle, ExternalLink } from 'lucide-react';

const ChartDisplay = ({ chartData }) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  if (!chartData) {
    return null;
  }

  const { type, content, url, alt, title } = chartData;

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const renderChart = () => {
    switch (type) {
      case 'html':
        return (
          <div
            className="w-full overflow-hidden rounded-lg"
            dangerouslySetInnerHTML={{ __html: content }}
          />
        );

      case 'image':
        return (
          <div className="relative">
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            )}

            {imageError ? (
              <div className="flex flex-col items-center justify-center p-8 bg-gray-100 rounded-lg text-gray-500">
                <AlertTriangle className="w-12 h-12 mb-2" />
                <p className="text-sm text-center">
                  Không thể tải biểu đồ từ nguồn bên ngoài
                </p>
                <p className="text-xs text-center mt-1">
                  {url}
                </p>
              </div>
            ) : (
              <img
                src={url}
                alt={alt || 'Chart from external API'}
                className="w-full h-auto rounded-lg"
                onLoad={handleImageLoad}
                onError={handleImageError}
                style={{ display: isLoading ? 'none' : 'block' }}
              />
            )}
          </div>
        );

      default:
        return (
          <div className="flex items-center justify-center p-8 bg-gray-100 rounded-lg text-gray-500">
            <p>Định dạng biểu đồ không được hỗ trợ</p>
          </div>
        );
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-3 sm:p-4 my-4 w-full">
      {/* Chart title */}
      <div className="mb-3 flex items-center justify-between">
        <h3 className="text-sm sm:text-lg font-semibold text-gray-800 truncate">
          {title || 'Biểu đồ từ API'}
        </h3>
        {type === 'image' && url && (
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:text-blue-700 ml-2 flex-shrink-0"
            title="Mở trong tab mới"
          >
            <ExternalLink className="w-4 h-4" />
          </a>
        )}
      </div>

      {/* Chart content */}
      <div className="w-full overflow-hidden">
        {renderChart()}
      </div>

      {/* Chart info */}
      <div className="mt-2 text-xs text-gray-500 flex items-center justify-between">
        <span>
          Nguồn: {type === 'html' ? 'HTML từ API' : 'Hình ảnh từ API'}
        </span>
        {type === 'image' && !imageError && (
          <span className="text-green-600">✓ Đã tải</span>
        )}
      </div>
    </div>
  );
};

export default ChartDisplay;
