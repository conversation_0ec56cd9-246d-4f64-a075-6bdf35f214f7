{"name": "chatbot-monorepo", "version": "1.0.0", "description": "Chatbot project with React frontend and Express backend integrating with Langflow", "main": "index.js", "scripts": {"dev": "echo '🚀 Starting IMTA AI Project...' && echo '📋 Application URLs:' && echo '   Frontend: http://localhost:5173' && echo '   Backend API: http://localhost:5001/api' && echo '   Health Check: http://localhost:5001/api/health' && echo '   MongoDB: localhost:27017' && echo '   Redis: localhost:6380' && echo '   MinIO: localhost:9000' && echo '   MongoDB Admin: http://localhost:8081' && echo '   Redis Admin: http://localhost:8082' && echo '' && echo '🎉 Ready to use! Open http://localhost:5173 in your browser' && echo '' && concurrently \"npm run server\" \"npm run client\"", "start": "chmod +x start.sh && ./start.sh", "start-dev": "chmod +x start.sh && ./start.sh", "server": "cd server && npm run dev", "client": "cd client && npm start", "install-all": "npm install && cd server && npm install && cd ../client && npm install", "build": "cd client && npm run build", "start-server": "cd server && npm start", "deploy-prod": "chmod +x deploy-prod.sh && ./deploy-prod.sh", "prod-up": "docker-compose -f docker-compose.prod.yml up -d", "prod-down": "docker-compose -f docker-compose.prod.yml down", "prod-logs": "docker-compose -f docker-compose.prod.yml logs -f", "prod-restart": "docker-compose -f docker-compose.prod.yml restart"}, "keywords": ["chatbot", "react", "express", "langflow", "monorepo"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}