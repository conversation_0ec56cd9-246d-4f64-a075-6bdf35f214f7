// File validator utility - Pure JavaScript (no JSX)

// File size limits (in bytes)
export const FILE_SIZE_LIMITS = {
  image: 10 * 1024 * 1024, // 10MB for images
  document: 10 * 1024 * 1024, // 10MB for documents
  default: 10 * 1024 * 1024 // 10MB default
};

// Allowed file types
export const ALLOWED_FILE_TYPES = {
  images: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
  documents: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ],
  archives: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'],
  audio: ['audio/mpeg', 'audio/wav', 'audio/ogg'],
  video: ['video/mp4', 'video/webm', 'video/ogg']
};

// Get all allowed types
export const getAllowedTypes = () => {
  return [
    ...ALLOWED_FILE_TYPES.images,
    ...ALLOWED_FILE_TYPES.documents,
    ...ALLOWED_FILE_TYPES.archives,
    ...ALLOWED_FILE_TYPES.audio,
    ...ALLOWED_FILE_TYPES.video
  ];
};

// Validate file
export const validateFile = (file) => {
  const result = {
    isValid: true,
    error: null
  };

  // Check if file exists
  if (!file) {
    result.isValid = false;
    result.error = 'File không tồn tại';
    return result;
  }

  // Check file type
  const allowedTypes = getAllowedTypes();
  if (!allowedTypes.includes(file.type)) {
    result.isValid = false;
    result.error = `Loại file không được hỗ trợ: ${file.type}`;
    return result;
  }

  // Check file size
  const maxSize = getMaxFileSize(file.type);
  if (file.size > maxSize) {
    result.isValid = false;
    result.error = `File quá lớn. Tối đa: ${formatFileSize(maxSize)}`;
    return result;
  }

  // Check file name
  if (file.name.length > 255) {
    result.isValid = false;
    result.error = 'Tên file quá dài (tối đa 255 ký tự)';
    return result;
  }

  return result;
};

// Get maximum file size based on type
export const getMaxFileSize = (fileType) => {
  if (ALLOWED_FILE_TYPES.images.includes(fileType)) {
    return FILE_SIZE_LIMITS.image;
  }
  if (ALLOWED_FILE_TYPES.documents.includes(fileType)) {
    return FILE_SIZE_LIMITS.document;
  }
  return FILE_SIZE_LIMITS.default;
};

// Format file size for display
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Get file icon name based on type (returns string for icon name)
export const getFileIconName = (fileType) => {
  if (ALLOWED_FILE_TYPES.images.includes(fileType)) {
    return 'Image';
  }
  if (ALLOWED_FILE_TYPES.documents.includes(fileType)) {
    return 'FileText';
  }
  if (ALLOWED_FILE_TYPES.archives.includes(fileType)) {
    return 'Archive';
  }
  if (ALLOWED_FILE_TYPES.audio.includes(fileType)) {
    return 'Music';
  }
  if (ALLOWED_FILE_TYPES.video.includes(fileType)) {
    return 'Video';
  }
  return 'File';
};

// Get file icon emoji based on type (fallback for simple display)
export const getFileIconEmoji = (fileType) => {
  if (ALLOWED_FILE_TYPES.images.includes(fileType)) {
    return '🖼️';
  }
  if (ALLOWED_FILE_TYPES.documents.includes(fileType)) {
    return '📄';
  }
  if (ALLOWED_FILE_TYPES.archives.includes(fileType)) {
    return '📦';
  }
  if (ALLOWED_FILE_TYPES.audio.includes(fileType)) {
    return '🎵';
  }
  if (ALLOWED_FILE_TYPES.video.includes(fileType)) {
    return '🎬';
  }
  return '📎';
};

// Get file category
export const getFileCategory = (fileType) => {
  if (ALLOWED_FILE_TYPES.images.includes(fileType)) return 'image';
  if (ALLOWED_FILE_TYPES.documents.includes(fileType)) return 'document';
  if (ALLOWED_FILE_TYPES.archives.includes(fileType)) return 'archive';
  if (ALLOWED_FILE_TYPES.audio.includes(fileType)) return 'audio';
  if (ALLOWED_FILE_TYPES.video.includes(fileType)) return 'video';
  return 'unknown';
};

// Check if file is image
export const isImageFile = (fileType) => {
  return ALLOWED_FILE_TYPES.images.includes(fileType);
};

// Generate unique file ID
export const generateFileId = () => {
  return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Create file object for upload
export const createFileObject = (file) => {
  return {
    id: generateFileId(),
    file: file,
    name: file.name,
    size: file.size,
    type: file.type,
    category: getFileCategory(file.type),
    isImage: isImageFile(file.type),
    preview: null,
    uploadProgress: 0,
    uploadStatus: 'pending', // pending, uploading, completed, error
    error: null,
    createdAt: new Date().toISOString()
  };
};
