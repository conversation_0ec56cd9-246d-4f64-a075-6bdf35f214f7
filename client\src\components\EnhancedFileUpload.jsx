import React, { useState, useRef, useCallback } from 'react';
import { 
  Upload, 
  X, 
  File, 
  Image, 
  FileText, 
  Music, 
  Video, 
  Archive,
  Code,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { useFileUpload } from '../context/FileUploadContext';

const EnhancedFileUpload = ({ 
  isOpen, 
  onClose, 
  onFilesUploaded,
  maxFiles = 10,
  allowMultiple = true 
}) => {
  const {
    uploadFile,
    uploadMultipleFiles,
    validateFile,
    formatFileSize,
    getUserTier,
    FILE_TYPES,
    USER_TIERS,
    uploadError,
    clearUploadError
  } = useFileUpload();

  const [dragActive, setDragActive] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResults, setUploadResults] = useState([]);
  const fileInputRef = useRef(null);

  // Get file icon
  const getFileIcon = (fileName) => {
    const fileType = Object.keys(FILE_TYPES).find(type => 
      FILE_TYPES[type].extensions.some(ext => 
        fileName.toLowerCase().endsWith(ext)
      )
    );

    const iconMap = {
      image: Image,
      document: FileText,
      audio: Music,
      video: Video,
      archive: Archive,
      code: Code
    };

    return iconMap[fileType] || File;
  };

  // Handle drag events
  const handleDrag = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  // Handle drop
  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, []);

  // Handle file selection
  const handleFiles = (fileList) => {
    const files = Array.from(fileList);
    const userTier = getUserTier();
    const maxFilesAllowed = allowMultiple ? Math.min(maxFiles, USER_TIERS[userTier].maxFilesPerDay) : 1;
    
    if (files.length > maxFilesAllowed) {
      alert(`Chỉ có thể upload tối đa ${maxFilesAllowed} files`);
      return;
    }

    // Validate each file
    const validatedFiles = files.map(file => {
      const validation = validateFile(file);
      return {
        file,
        id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        validation,
        preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null
      };
    });

    setSelectedFiles(validatedFiles);
    clearUploadError();
  };

  // Handle file input change
  const handleFileInputChange = (e) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  // Remove selected file
  const removeSelectedFile = (fileId) => {
    setSelectedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // Upload files
  const handleUpload = async () => {
    const validFiles = selectedFiles.filter(f => f.validation.valid);
    
    if (validFiles.length === 0) {
      alert('Không có file hợp lệ để upload');
      return;
    }

    setIsUploading(true);
    setUploadResults([]);

    try {
      const results = await uploadMultipleFiles(
        validFiles.map(f => f.file),
        (progress) => {
          setUploadProgress({ overall: progress });
        }
      );

      setUploadResults(results);
      
      // Call success callback
      const successfulUploads = results.filter(r => !r.error);
      if (successfulUploads.length > 0) {
        onFilesUploaded?.(successfulUploads);
      }

      // Auto close after successful upload
      if (results.every(r => !r.error)) {
        setTimeout(() => {
          onClose();
        }, 2000);
      }

    } catch (error) {
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
      setUploadProgress({});
    }
  };

  // Reset modal
  const handleClose = () => {
    setSelectedFiles([]);
    setUploadResults([]);
    setUploadProgress({});
    setIsUploading(false);
    clearUploadError();
    onClose();
  };

  if (!isOpen) return null;

  const userTier = getUserTier();
  const tierConfig = USER_TIERS[userTier];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-2xl">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Upload Files</h2>
              <p className="text-blue-100 text-sm mt-1">
                Gói {userTier} - Tối đa {formatFileSize(tierConfig.maxFileSize)} mỗi file
              </p>
            </div>
            <button
              onClick={handleClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="p-8">
          {/* Upload Results */}
          {uploadResults.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Kết quả Upload</h3>
              <div className="space-y-2">
                {uploadResults.map((result, index) => (
                  <div
                    key={index}
                    className={`flex items-center gap-3 p-3 rounded-lg ${
                      result.error ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'
                    }`}
                  >
                    {result.error ? (
                      <AlertCircle className="w-5 h-5" />
                    ) : (
                      <CheckCircle className="w-5 h-5" />
                    )}
                    <span className="flex-1">
                      {result.error ? result.error : `${result.name} uploaded successfully`}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Drag and Drop Area */}
          {selectedFiles.length === 0 && (
            <div
              className={`
                border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300
                ${dragActive 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'
                }
              `}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="flex flex-col items-center gap-4">
                <div className={`p-4 rounded-full ${dragActive ? 'bg-blue-100' : 'bg-gray-100'}`}>
                  <Upload className={`w-12 h-12 ${dragActive ? 'text-blue-600' : 'text-gray-600'}`} />
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Kéo thả files vào đây
                  </h3>
                  <p className="text-gray-600 mb-4">
                    hoặc click để chọn files từ máy tính
                  </p>
                  
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
                  >
                    Chọn Files
                  </button>
                  
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple={allowMultiple}
                    onChange={handleFileInputChange}
                    className="hidden"
                    accept={Object.values(FILE_TYPES).flatMap(type => type.extensions).join(',')}
                  />
                </div>
              </div>
              
              {/* Supported file types */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <p className="text-sm text-gray-600 mb-3">Loại files được hỗ trợ:</p>
                <div className="flex flex-wrap gap-2 justify-center">
                  {tierConfig.allowedTypes.map(type => (
                    <span
                      key={type}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs"
                    >
                      <span>{FILE_TYPES[type].icon}</span>
                      <span>{FILE_TYPES[type].category}</span>
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Files đã chọn ({selectedFiles.length})
                </h3>
                <button
                  onClick={() => setSelectedFiles([])}
                  className="text-sm text-gray-600 hover:text-red-600 transition-colors"
                >
                  Xóa tất cả
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {selectedFiles.map((fileData) => {
                  const IconComponent = getFileIcon(fileData.file.name);
                  
                  return (
                    <div
                      key={fileData.id}
                      className={`
                        border rounded-lg p-4 transition-all
                        ${fileData.validation.valid 
                          ? 'border-green-200 bg-green-50' 
                          : 'border-red-200 bg-red-50'
                        }
                      `}
                    >
                      <div className="flex items-start gap-3">
                        {/* File preview or icon */}
                        <div className="flex-shrink-0">
                          {fileData.preview ? (
                            <img
                              src={fileData.preview}
                              alt={fileData.file.name}
                              className="w-12 h-12 object-cover rounded"
                            />
                          ) : (
                            <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                              <IconComponent className="w-6 h-6 text-gray-600" />
                            </div>
                          )}
                        </div>

                        {/* File info */}
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900 truncate">
                            {fileData.file.name}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {formatFileSize(fileData.file.size)}
                          </p>
                          
                          {!fileData.validation.valid && (
                            <p className="text-sm text-red-600 mt-1">
                              {fileData.validation.error}
                            </p>
                          )}
                        </div>

                        {/* Remove button */}
                        <button
                          onClick={() => removeSelectedFile(fileData.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Upload Progress */}
              {isUploading && (
                <div className="mb-6">
                  <div className="flex items-center gap-2 mb-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm font-medium">Đang upload...</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress.overall || 0}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Upload Button */}
              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => setSelectedFiles([])}
                  disabled={isUploading}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                >
                  Hủy
                </button>
                <button
                  onClick={handleUpload}
                  disabled={isUploading || selectedFiles.every(f => !f.validation.valid)}
                  className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 font-medium"
                >
                  {isUploading ? 'Đang upload...' : `Upload ${selectedFiles.filter(f => f.validation.valid).length} files`}
                </button>
              </div>
            </div>
          )}

          {/* Error message */}
          {uploadError && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="w-5 h-5" />
                <span>{uploadError}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedFileUpload;
