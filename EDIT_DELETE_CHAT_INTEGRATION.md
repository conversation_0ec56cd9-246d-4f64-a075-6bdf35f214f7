# Tích Hợp API Backend: Edit & Delete Chat

## Phân Tích Backend APIs

### 🔍 APIs Có Sẵn Trong Backend

#### 1. PUT /api/chat/:chatId - Cập nhật chat
```javascript
// Location: server/routes/chat.js lines 182-208
// Request Body: { title, status, metadata }
// Response: { success: true, data: { chat: updatedChat } }
// Security: Chỉ user sở hữu chat mới có thể update
```

#### 2. DELETE /api/chat/:chatId - Xóa chat
```javascript
// Location: server/routes/chat.js lines 210-234  
// Features:
// - <PERSON><PERSON><PERSON> tất cả messages trong chat trước
// - Sau đó xóa chat
// - Security: Chỉ user sở hữu chat mới có thể delete
```

### 🔗 Frontend APIs Có Sẵn
```javascript
// client/src/utils/api.js
chatAPI.updateChat(chatId, updateData) // PUT /chat/:chatId
chatAPI.deleteChat(chatId)            // DELETE /chat/:chatId
```

## Implementation Chi Tiết

### 1. ChatContext Updates

#### A. Thêm Action Type
```javascript
const actionTypes = {
  // ...existing actions...
  UPDATE_CONVERSATION: 'UPDATE_CONVERSATION', // NEW
};
```

#### B. Thêm Reducer Case
```javascript
case actionTypes.UPDATE_CONVERSATION:
  return {
    ...state,
    conversations: state.conversations.map(conv =>
      conv.id === action.payload.id
        ? { ...conv, ...action.payload.updates }
        : conv
    ),
  };
```

#### C. Thêm updateConversation Action
```javascript
updateConversation: async (id, updates) => {
  const conversation = state.conversations.find(conv => conv.id === id);
  
  if (conversation.backendChatId) {
    // Update trên backend
    const response = await chatAPI.updateChat(conversation.backendChatId, updates);
    
    // Update local state với backend response
    dispatch({
      type: actionTypes.UPDATE_CONVERSATION,
      payload: {
        id: id,
        updates: {
          title: response.data.chat.title,
          status: response.data.chat.status,
          metadata: response.data.chat.metadata,
          updatedAt: response.data.chat.updatedAt
        }
      }
    });
  } else {
    // Local-only update
    dispatch({
      type: actionTypes.UPDATE_CONVERSATION,
      payload: { id: id, updates }
    });
  }
}
```

#### D. Cập nhật deleteConversation Action
```javascript
deleteConversation: async (id) => {
  const conversation = state.conversations.find(conv => conv.id === id);
  
  if (conversation.backendChatId) {
    // Delete trên backend (sẽ xóa chat + messages)
    await chatAPI.deleteChat(conversation.backendChatId);
  }
  
  // Delete from local state
  dispatch({ type: actionTypes.DELETE_CONVERSATION, payload: id });
}
```

### 2. Sidebar Component Updates

#### A. Thêm State Management
```javascript
const [isUpdatingChat, setIsUpdatingChat] = useState(false);
const [isDeletingChat, setIsDeletingChat] = useState(null);
```

#### B. Enhanced handleEditSave
```javascript
const handleEditSave = async (e) => {
  e.stopPropagation();
  
  if (!editTitle.trim()) {
    alert('Tiêu đề không thể để trống');
    return;
  }
  
  setIsUpdatingChat(true);
  try {
    await updateConversation(editingId, { title: editTitle.trim() });
    setEditingId(null);
    setEditTitle('');
  } catch (error) {
    alert('Không thể cập nhật tiêu đề cuộc trò chuyện');
  } finally {
    setIsUpdatingChat(false);
  }
};
```

#### C. Enhanced handleDeleteConversation
```javascript
const handleDeleteConversation = async (e, id) => {
  e.stopPropagation();
  
  const confirmed = window.confirm('Bạn có chắc chắn muốn xóa cuộc trò chuyện này? Hành động này không thể hoàn tác.');
  if (!confirmed) return;
  
  setIsDeletingChat(id);
  try {
    await deleteConversation(id);
  } catch (error) {
    alert('Không thể xóa cuộc trò chuyện');
  } finally {
    setIsDeletingChat(null);
  }
};
```

#### D. UI Loading States
```jsx
{/* Edit Button with Loading */}
<button
  onClick={handleEditSave}
  disabled={isUpdatingChat}
  className={isUpdatingChat ? 'text-gray-500 cursor-not-allowed' : 'text-green-400 hover:text-green-300'}
>
  {isUpdatingChat ? (
    <div className="w-4 h-4 border border-gray-500 border-t-transparent rounded-full animate-spin"></div>
  ) : (
    <Check className="w-4 h-4" />
  )}
</button>

{/* Delete Button with Loading */}
<button
  onClick={(e) => handleDeleteConversation(e, conversation.id)}
  disabled={isDeletingChat === conversation.id}
>
  {isDeletingChat === conversation.id ? (
    <div className="w-3 h-3 border border-gray-500 border-t-transparent rounded-full animate-spin"></div>
  ) : (
    <Trash2 className="w-3 h-3" />
  )}
</button>
```

## Workflow Hoạt Động

### 📝 Edit Chat Title Flow
1. User click nút edit → Edit mode active
2. User nhập title mới → Click save button
3. Frontend validate → Gọi `updateConversation(id, { title })`
4. `updateConversation` gọi `chatAPI.updateChat(backendChatId, { title })`
5. Backend cập nhật chat trong database
6. Frontend nhận response → Update local state
7. UI refresh với title mới

### 🗑️ Delete Chat Flow
1. User click nút delete → Confirmation dialog
2. User confirm → Frontend gọi `deleteConversation(id)`
3. `deleteConversation` gọi `chatAPI.deleteChat(backendChatId)`
4. Backend xóa tất cả messages + chat trong database
5. Frontend xóa conversation khỏi local state
6. UI refresh → Chat biến mất khỏi sidebar

## Features & Benefits

### ✅ Real-time Backend Sync
- Edit/Delete operations được sync với database
- Data consistency giữa frontend và backend
- Persistent changes across sessions

### ✅ Enhanced UX
- Loading spinners khi đang process
- Disabled buttons để prevent multiple operations
- Clear confirmation dialog cho delete
- Validation cho empty titles

### ✅ Error Handling
- Try-catch wrapping cho tất cả API calls
- User-friendly error messages
- Graceful fallback cho local-only chats
- Console logging để debug

### ✅ Defensive Programming
- Check conversation existence trước khi process
- Prevent multiple simultaneous operations
- Input validation
- Stop event propagation

## Debug Console Logs

### Edit Operation
```
💾 Saving conversation title: {id} {newTitle}
🔄 Updating conversation: {id} {updates}
🌐 Updating chat on backend: {backendChatId}
✅ Backend chat updated successfully: {response}
✅ Conversation updated successfully
```

### Delete Operation  
```
🗑️ Deleting conversation: {id}
🌐 Deleting chat on backend: {backendChatId}
✅ Backend chat deleted successfully: {response}
✅ Conversation deleted successfully
```

## Testing Checklist

### ✅ Edit Functionality
- [ ] Click edit button → Edit mode active
- [ ] Type new title → Save button shows loading
- [ ] API call successful → Title updated in UI
- [ ] Empty title → Validation error
- [ ] API fails → Error message + title unchanged

### ✅ Delete Functionality  
- [ ] Click delete button → Confirmation dialog
- [ ] Cancel → No deletion
- [ ] Confirm → Delete button shows loading
- [ ] API call successful → Chat removed from sidebar
- [ ] API fails → Error message + chat unchanged

### ✅ Edge Cases
- [ ] Local-only chats (no backendChatId) → Local operations only
- [ ] Multiple rapid clicks → Only one operation proceeds
- [ ] Network failure → Proper error handling

## Files Modified

1. **`client/src/context/ChatContext.jsx`**
   - Added UPDATE_CONVERSATION action type
   - Added updateConversation action with API integration
   - Enhanced deleteConversation with API integration
   - Error handling và logging

2. **`client/src/components/Sidebar.jsx`**
   - Enhanced handleEditSave với async API call
   - Enhanced handleDeleteConversation với async API call
   - Added loading states for edit/delete buttons
   - UI improvements với spinners và disabled states

## Status

✅ **Backend APIs**: Đã có sẵn và hoạt động tốt  
✅ **Frontend Integration**: Hoàn thành đầy đủ  
✅ **UI/UX**: Enhanced với loading states và error handling  
✅ **Testing**: Ready để test toàn diện  

---

**Ngày hoàn thành**: 15/06/2025  
**Tích hợp**: Edit & Delete Chat với Backend APIs  
**Status**: ✅ HOÀN THÀNH VÀ READY TO TEST
