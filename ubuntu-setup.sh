#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Setting up IMTA AI on Ubuntu with existing Nginx...${NC}"

# Configuration
PROJECT_PATH="/home/<USER>"
NGINX_CONF="/etc/nginx/sites-available/imta-ai"
NGINX_ENABLED="/etc/nginx/sites-enabled/imta-ai"
SSL_CERT="/etc/ssl/certs/imta.ai.crt"
SSL_KEY="/etc/ssl/private/imta.ai.key"

# Function to log messages
log_message() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Function to log errors
log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Step 1: Update system and install dependencies
log_message "📦 Step 1: Installing dependencies..."

# Update system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Install Docker (Ubuntu)
if ! command -v docker &> /dev/null; then
    log_message "🐳 Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    log_message "✅ Docker installed successfully"
else
    log_message "✅ Docker already installed"
fi

# Install Docker Compose
if ! command -v docker-compose &> /dev/null; then
    log_message "🐳 Installing Docker Compose..."
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    log_message "✅ Docker Compose installed successfully"
else
    log_message "✅ Docker Compose already installed"
fi

# Install Node.js (for build process)
if ! command -v node &> /dev/null; then
    log_message "📦 Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    log_message "✅ Node.js installed successfully"
else
    log_message "✅ Node.js already installed"
fi

# Step 2: Create project directory
log_message "📁 Step 2: Setting up project directory..."
sudo mkdir -p $PROJECT_PATH
sudo chown $USER:$USER $PROJECT_PATH

# Step 3: Clone repository
log_message "📥 Step 3: Cloning repository..."
if [ ! -d "$PROJECT_PATH/.git" ]; then
    git clone https://github.com/dangngocbinh/imta-ai.git $PROJECT_PATH
else
    cd $PROJECT_PATH
    git pull origin main
fi

# Step 4: Setup environment files
log_message "⚙️  Step 4: Setting up environment files..."
cd $PROJECT_PATH

# Create environment files
cp server/env.example server/.env
cp client/env.example client/.env

# Configure production environment
sed -i 's/NODE_ENV=development/NODE_ENV=production/' server/.env
sed -i 's/MONGODB_URI=.*/MONGODB_URI=mongodb:\/\/imta_user:imta123456@mongodb:27017\/imta-ai/' server/.env
sed -i 's/REDIS_HOST=.*/REDIS_HOST=redis/' server/.env
sed -i 's/MINIO_ENDPOINT=.*/MINIO_ENDPOINT=minio/' server/.env
sed -i 's/CORS_ORIGIN=.*/CORS_ORIGIN=*/' server/.env

# Update client environment
sed -i 's/REACT_APP_API_URL=.*/REACT_APP_API_URL=https:\/\/imta.ai\/api/' client/.env
sed -i 's/REACT_APP_ENV=.*/REACT_APP_ENV=production/' client/.env

# Step 5: Setup Nginx configuration
log_message "🌐 Step 5: Setting up Nginx configuration..."

# Copy Nginx configuration
sudo cp nginx-ubuntu.conf $NGINX_CONF

# Create SSL directory if it doesn't exist
sudo mkdir -p /etc/ssl/certs /etc/ssl/private

# Check if SSL certificates exist
if [ ! -f "$SSL_CERT" ] || [ ! -f "$SSL_KEY" ]; then
    log_message "🔐 SSL certificates not found. Please place your SSL certificates:"
    log_message "   Certificate: $SSL_CERT"
    log_message "   Private Key: $SSL_KEY"
    log_message "   Or update the paths in $NGINX_CONF"
fi

# Enable the site
sudo ln -sf $NGINX_CONF $NGINX_ENABLED

# Test Nginx configuration
if sudo nginx -t; then
    log_message "✅ Nginx configuration is valid"
else
    log_error "❌ Nginx configuration is invalid"
    exit 1
fi

# Step 6: Setup permissions and dependencies
log_message "🔧 Step 6: Setting up permissions and dependencies..."
chmod +x start.sh deploy-prod.sh check-docker.sh backup.sh monitor.sh
npm run install-all

# Step 7: Create systemd service for auto-start
log_message "🔧 Step 7: Creating systemd service..."
sudo tee /etc/systemd/system/imta-ai.service > /dev/null << EOF
[Unit]
Description=IMTA AI Application
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$PROJECT_PATH
ExecStart=/usr/local/bin/docker-compose -f docker-compose.prod.yml up -d
ExecStop=/usr/local/bin/docker-compose -f docker-compose.prod.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable service
sudo systemctl daemon-reload
sudo systemctl enable imta-ai.service

# Step 8: Setup firewall
log_message "🔥 Step 8: Configuring firewall..."
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 5001/tcp
sudo ufw allow 27017/tcp
sudo ufw allow 6379/tcp
sudo ufw allow 9000/tcp
sudo ufw allow 9001/tcp
sudo ufw allow 8081/tcp
sudo ufw allow 8082/tcp
sudo ufw --force enable

# Step 9: Setup log rotation
log_message "📋 Step 9: Setting up log rotation..."
sudo tee /etc/logrotate.d/imta-ai > /dev/null << EOF
$PROJECT_PATH/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        systemctl reload nginx
    endscript
}
EOF

# Step 10: Setup cron jobs
log_message "⏰ Step 10: Setting up cron jobs..."

# Add backup cron job
(crontab -l 2>/dev/null; echo "0 2 * * * $PROJECT_PATH/backup.sh") | crontab -

# Add monitoring cron job
(crontab -l 2>/dev/null; echo "*/5 * * * * $PROJECT_PATH/monitor.sh") | crontab -

# Step 11: Deploy application
log_message "🚀 Step 11: Deploying application..."

# Stop any existing containers
docker-compose -f docker-compose.prod.yml down --remove-orphans 2>/dev/null || true

# Build and start production
docker-compose -f docker-compose.prod.yml up -d --build

# Wait for services to start
sleep 30

# Step 12: Verify deployment
log_message "✅ Step 12: Verifying deployment..."

# Check container status
docker-compose -f docker-compose.prod.yml ps

# Test health endpoint
if curl -f http://localhost:5001/api/health > /dev/null 2>&1; then
    log_message "✅ Application health check passed"
else
    log_error "❌ Application health check failed"
fi

# Reload Nginx
sudo systemctl reload nginx

# Step 13: Show access information
log_message "🎉 Setup completed successfully!"
echo -e "${BLUE}🌐 Access URLs:${NC}"
echo -e "${GREEN}  • Main Application: https://imta.ai${NC}"
echo -e "${GREEN}  • API Health Check: https://imta.ai/api/health${NC}"
echo -e "${GREEN}  • Mongo Express: https://imta.ai:8081${NC}"
echo -e "${GREEN}  • Redis Commander: https://imta.ai:8082${NC}"
echo -e "${GREEN}  • MinIO Console: https://imta.ai:9001${NC}"

echo -e "${BLUE}💡 Management commands:${NC}"
echo -e "${YELLOW}  • View logs: docker-compose -f docker-compose.prod.yml logs -f${NC}"
echo -e "${YELLOW}  • Restart: sudo systemctl restart imta-ai${NC}"
echo -e "${YELLOW}  • Stop: sudo systemctl stop imta-ai${NC}"
echo -e "${YELLOW}  • Status: sudo systemctl status imta-ai${NC}"
echo -e "${YELLOW}  • Backup: $PROJECT_PATH/backup.sh${NC}"
echo -e "${YELLOW}  • Monitor: $PROJECT_PATH/monitor.sh${NC}"

echo -e "${BLUE}🔧 Nginx commands:${NC}"
echo -e "${YELLOW}  • Test config: sudo nginx -t${NC}"
echo -e "${YELLOW}  • Reload: sudo systemctl reload nginx${NC}"
echo -e "${YELLOW}  • Restart: sudo systemctl restart nginx${NC}"
echo -e "${YELLOW}  • Status: sudo systemctl status nginx${NC}"

log_message "🎉 IMTA AI is now running on Ubuntu with your existing Nginx!" 