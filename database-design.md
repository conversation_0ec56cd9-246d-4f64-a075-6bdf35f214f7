# 🗄️ THIẾT KẾ DATABASE - IMTA AI

## 📊 COLLECTIONS OVERVIEW

### 1. **users** - Qu<PERSON><PERSON> lý học viên
### 2. **credits** - Quản lý credit/token
### 3. **chats** - Phiên chat
### 4. **messages** - Tin nhắn trong chat
### 5. **ads_analysis** - Phân tích file ads
### 6. **content_generation** - Bài quảng cáo được tạo
### 7. **avatar_analysis** - Phân tích avatar khách hàng
### 8. **courses** - Thông tin khóa học IMTA
### 9. **policies** - Chính sách IMTA

---

## 📋 CHI TIẾT COLLECTIONS

### 1. **users** - Học viên
```javascript
{
  _id: ObjectId,
  username: String,           // Tên đăng nhập
  email: String,              // Email
  fullName: String,           // Họ tên đầy đủ
  phone: String,              // Số điện thoại
  avatar: String,             // URL avatar
  role: String,               // "student", "admin", "teacher"
  status: String,             // "active", "inactive", "suspended"
  registrationDate: Date,     // Ngày đăng ký
  lastLogin: Date,            // Lần đăng nhập cuối
  preferences: {
    language: String,         // "vi", "en"
    theme: String,            // "light", "dark"
    notifications: Boolean
  },
  metadata: {
    source: String,           // Nguồn đăng ký
    referrer: String,         // Người giới thiệu
    tags: [String]            // Tags phân loại
  }
}
```

### 2. **credits** - Quản lý credit
```javascript
{
  _id: ObjectId,
  userId: ObjectId,           // Reference to users
  balance: Number,            // Số credit hiện tại
  totalEarned: Number,        // Tổng credit đã nhận
  totalSpent: Number,         // Tổng credit đã sử dụng
  transactions: [{
    type: String,             // "earn", "spend", "refund"
    amount: Number,           // Số lượng
    description: String,      // Mô tả
    service: String,          // "chat", "ads_analysis", "content_gen", "avatar_analysis"
    timestamp: Date,
    metadata: Object          // Thông tin bổ sung
  }],
  lastUpdated: Date
}
```

### 3. **chats** - Phiên chat
```javascript
{
  _id: ObjectId,
  userId: ObjectId,           // Reference to users
  title: String,              // Tiêu đề chat
  type: String,               // "general", "course_inquiry", "ads_analysis", "content_gen", "avatar_analysis"
  status: String,             // "active", "completed", "archived"
  createdAt: Date,
  updatedAt: Date,
  lastMessageAt: Date,
  metadata: {
    courseId: ObjectId,       // Nếu liên quan đến khóa học
    policyId: ObjectId,       // Nếu liên quan đến chính sách
    tags: [String],           // Tags phân loại
    priority: String          // "low", "medium", "high"
  },
  analytics: {
    messageCount: Number,     // Số tin nhắn
    duration: Number,         // Thời gian chat (phút)
    creditUsed: Number        // Credit đã sử dụng
  }
}
```

### 4. **messages** - Tin nhắn
```javascript
{
  _id: ObjectId,
  chatId: ObjectId,           // Reference to chats
  sender: String,             // "user", "bot"
  content: String,            // Nội dung tin nhắn
  contentType: String,        // "text", "image", "file", "chart"
  timestamp: Date,
  metadata: {
    attachments: [{
      type: String,           // "image", "pdf", "excel", "chart"
      url: String,            // URL file
      filename: String,       // Tên file
      size: Number            // Kích thước (bytes)
    }],
    aiResponse: {
      model: String,          // Model AI sử dụng
      confidence: Number,     // Độ tin cậy
      processingTime: Number  // Thời gian xử lý (ms)
    },
    creditCost: Number        // Credit tiêu tốn
  }
}
```

### 5. **ads_analysis** - Phân tích file ads
```javascript
{
  _id: ObjectId,
  userId: ObjectId,           // Reference to users
  chatId: ObjectId,           // Reference to chats
  originalFile: {
    filename: String,
    url: String,
    size: Number,
    type: String              // "excel", "csv", "pdf"
  },
  analysis: {
    summary: String,          // Tóm tắt phân tích
    insights: [String],       // Các insight chính
    recommendations: [String], // Khuyến nghị
    charts: [{
      type: String,           // "line", "bar", "pie", "scatter"
      title: String,
      data: Object,           // Dữ liệu chart
      imageUrl: String        // URL hình ảnh chart
    }],
    metrics: {
      totalSpend: Number,
      impressions: Number,
      clicks: Number,
      conversions: Number,
      roas: Number
    }
  },
  status: String,             // "processing", "completed", "failed"
  createdAt: Date,
  completedAt: Date,
  creditUsed: Number
}
```

### 6. **content_generation** - Bài quảng cáo
```javascript
{
  _id: ObjectId,
  userId: ObjectId,           // Reference to users
  chatId: ObjectId,           // Reference to chats
  prompt: String,             // Prompt gốc
  generatedContent: {
    title: String,            // Tiêu đề
    body: String,             // Nội dung chính
    hashtags: [String],       // Hashtags
    callToAction: String,     // CTA
    platform: String,         // "facebook", "instagram", "tiktok", "google"
    tone: String,             // "professional", "casual", "friendly"
    length: String            // "short", "medium", "long"
  },
  metadata: {
    industry: String,         // Ngành nghề
    targetAudience: String,   // Đối tượng mục tiêu
    keywords: [String],       // Từ khóa
    language: String          // "vi", "en"
  },
  status: String,             // "generated", "edited", "approved"
  createdAt: Date,
  creditUsed: Number
}
```

### 7. **avatar_analysis** - Phân tích avatar
```javascript
{
  _id: ObjectId,
  userId: ObjectId,           // Reference to users
  chatId: ObjectId,           // Reference to chats
  originalImage: {
    filename: String,
    url: String,
    size: Number
  },
  analysis: {
    demographics: {
      age: String,            // "18-25", "26-35", etc.
      gender: String,         // "male", "female", "other"
      ethnicity: String
    },
    personality: {
      traits: [String],       // ["extroverted", "creative", "professional"]
      style: String,          // "casual", "business", "creative"
      mood: String            // "friendly", "serious", "energetic"
    },
    recommendations: [String], // Khuyến nghị về avatar
    score: Number             // Điểm đánh giá (1-10)
  },
  status: String,             // "processing", "completed", "failed"
  createdAt: Date,
  completedAt: Date,
  creditUsed: Number
}
```

### 8. **courses** - Khóa học IMTA
```javascript
{
  _id: ObjectId,
  name: String,               // Tên khóa học
  code: String,               // Mã khóa học
  description: String,        // Mô tả
  price: Number,              // Giá
  duration: String,           // Thời lượng
  level: String,              // "beginner", "intermediate", "advanced"
  category: String,           // Danh mục
  instructor: String,         // Giảng viên
  status: String,             // "active", "inactive", "draft"
  metadata: {
    syllabus: [String],       // Nội dung khóa học
    requirements: [String],   // Yêu cầu đầu vào
    outcomes: [String],       // Kết quả đạt được
    tags: [String]            // Tags
  },
  createdAt: Date,
  updatedAt: Date
}
```

### 9. **policies** - Chính sách IMTA
```javascript
{
  _id: ObjectId,
  title: String,              // Tiêu đề chính sách
  category: String,           // "enrollment", "refund", "attendance", "graduation"
  content: String,            // Nội dung chính sách
  version: String,            // Phiên bản
  status: String,             // "active", "inactive", "draft"
  effectiveDate: Date,        // Ngày có hiệu lực
  expiryDate: Date,           // Ngày hết hạn (nếu có)
  metadata: {
    tags: [String],           // Tags
    priority: String,         // "low", "medium", "high"
    department: String        // Phòng ban phụ trách
  },
  createdAt: Date,
  updatedAt: Date
}
```

---

## 🔗 RELATIONSHIPS & INDEXES

### **Indexes cần thiết:**
```javascript
// users
db.users.createIndex({ "email": 1 }, { unique: true })
db.users.createIndex({ "username": 1 }, { unique: true })
db.users.createIndex({ "status": 1 })

// credits
db.credits.createIndex({ "userId": 1 }, { unique: true })
db.credits.createIndex({ "balance": 1 })

// chats
db.chats.createIndex({ "userId": 1 })
db.chats.createIndex({ "type": 1 })
db.chats.createIndex({ "status": 1 })
db.chats.createIndex({ "createdAt": -1 })

// messages
db.messages.createIndex({ "chatId": 1 })
db.messages.createIndex({ "timestamp": -1 })
db.messages.createIndex({ "sender": 1 })

// ads_analysis
db.ads_analysis.createIndex({ "userId": 1 })
db.ads_analysis.createIndex({ "status": 1 })
db.ads_analysis.createIndex({ "createdAt": -1 })

// content_generation
db.content_generation.createIndex({ "userId": 1 })
db.content_generation.createIndex({ "platform": 1 })
db.content_generation.createIndex({ "createdAt": -1 })

// avatar_analysis
db.avatar_analysis.createIndex({ "userId": 1 })
db.avatar_analysis.createIndex({ "status": 1 })

// courses
db.courses.createIndex({ "code": 1 }, { unique: true })
db.courses.createIndex({ "category": 1 })
db.courses.createIndex({ "status": 1 })

// policies
db.policies.createIndex({ "category": 1 })
db.policies.createIndex({ "status": 1 })
db.policies.createIndex({ "effectiveDate": -1 })
```

---

## 💡 IMPLEMENTATION NOTES

### **Credit System:**
- Mỗi loại dịch vụ có mức phí khác nhau
- Chat cơ bản: 1 credit/message
- Phân tích ads: 5 credits/analysis
- Tạo content: 3 credits/content
- Phân tích avatar: 2 credits/analysis

### **File Storage:**
- Sử dụng cloud storage (AWS S3, Google Cloud Storage)
- Lưu metadata trong MongoDB, file thực tế trong cloud

### **Real-time Features:**
- WebSocket cho chat real-time
- Server-sent events cho progress updates

### **Analytics:**
- Tracking usage patterns
- Performance metrics
- User behavior analysis 