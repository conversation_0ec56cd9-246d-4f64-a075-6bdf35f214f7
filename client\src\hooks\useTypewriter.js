import { useState, useEffect, useRef, useCallback } from 'react';

const useTypewriter = ({
  text = '',
  speed = 50,
  delay = 0,
  loop = false,
  deleteSpeed = 30,
  deleteDelay = 1000,
  onComplete = null,
  onStart = null,
  skipAnimation = false
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  const timeoutRef = useRef(null);
  const startTimeRef = useRef(null);

  // Skip animation if requested
  useEffect(() => {
    if (skipAnimation && text) {
      setDisplayText(text);
      setIsComplete(true);
      setIsTyping(false);
      onComplete?.();
      return;
    }
  }, [skipAnimation, text, onComplete]);

  // Reset when text changes
  useEffect(() => {
    if (!skipAnimation) {
      setDisplayText('');
      setCurrentIndex(0);
      setIsComplete(false);
      setIsTyping(false);
      setIsDeleting(false);
      setIsPaused(false);
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    }
  }, [text, skipAnimation]);

  // Main typing effect
  useEffect(() => {
    if (!text || skipAnimation || isPaused) return;

    const typeCharacter = () => {
      if (currentIndex < text.length) {
        setDisplayText(text.slice(0, currentIndex + 1));
        setCurrentIndex(prev => prev + 1);
        setIsTyping(true);
      } else {
        setIsTyping(false);
        setIsComplete(true);
        onComplete?.();
        
        if (loop) {
          timeoutRef.current = setTimeout(() => {
            setIsDeleting(true);
          }, deleteDelay);
        }
      }
    };

    const deleteCharacter = () => {
      if (currentIndex > 0) {
        setDisplayText(text.slice(0, currentIndex - 1));
        setCurrentIndex(prev => prev - 1);
        setIsDeleting(true);
      } else {
        setIsDeleting(false);
        timeoutRef.current = setTimeout(() => {
          setCurrentIndex(0);
        }, delay);
      }
    };

    if (!startTimeRef.current) {
      startTimeRef.current = Date.now();
      onStart?.();
      
      if (delay > 0) {
        timeoutRef.current = setTimeout(() => {
          typeCharacter();
        }, delay);
        return;
      }
    }

    if (isDeleting) {
      timeoutRef.current = setTimeout(deleteCharacter, deleteSpeed);
    } else {
      timeoutRef.current = setTimeout(typeCharacter, speed);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [
    text,
    currentIndex,
    speed,
    delay,
    loop,
    deleteSpeed,
    deleteDelay,
    isDeleting,
    onComplete,
    onStart,
    skipAnimation,
    isPaused
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Control functions
  const pause = useCallback(() => {
    setIsPaused(true);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const resume = useCallback(() => {
    setIsPaused(false);
  }, []);

  const skip = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setDisplayText(text);
    setCurrentIndex(text.length);
    setIsTyping(false);
    setIsComplete(true);
    onComplete?.();
  }, [text, onComplete]);

  const reset = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setDisplayText('');
    setCurrentIndex(0);
    setIsComplete(false);
    setIsTyping(false);
    setIsDeleting(false);
    setIsPaused(false);
    startTimeRef.current = null;
  }, []);

  return {
    displayText,
    isTyping,
    isDeleting,
    isComplete,
    isPaused,
    pause,
    resume,
    skip,
    reset,
    progress: text.length > 0 ? (currentIndex / text.length) * 100 : 0
  };
};

export default useTypewriter;
