import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { useCredit } from './CreditContext';

const FileUploadContext = createContext();

export const useFileUpload = () => {
  const context = useContext(FileUploadContext);
  if (!context) {
    throw new Error('useFileUpload must be used within a FileUploadProvider');
  }
  return context;
};

// File type configurations
export const FILE_TYPES = {
  image: {
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
    maxSize: 10 * 1024 * 1024, // 10MB
    creditCost: 2,
    icon: '🖼️',
    category: 'Hình ảnh'
  },
  document: {
    extensions: ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'],
    maxSize: 50 * 1024 * 1024, // 50MB
    creditCost: 3,
    icon: '📄',
    category: 'Tài liệu'
  },
  spreadsheet: {
    extensions: ['.xls', '.xlsx', '.csv', '.ods'],
    maxSize: 25 * 1024 * 1024, // 25MB
    creditCost: 3,
    icon: '📊',
    category: 'Bảng tính'
  },
  presentation: {
    extensions: ['.ppt', '.pptx', '.odp'],
    maxSize: 100 * 1024 * 1024, // 100MB
    creditCost: 4,
    icon: '📽️',
    category: 'Thuyết trình'
  },
  audio: {
    extensions: ['.mp3', '.wav', '.ogg', '.m4a', '.aac'],
    maxSize: 100 * 1024 * 1024, // 100MB
    creditCost: 5,
    icon: '🎵',
    category: 'Âm thanh'
  },
  video: {
    extensions: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'],
    maxSize: 500 * 1024 * 1024, // 500MB
    creditCost: 10,
    icon: '🎬',
    category: 'Video'
  },
  archive: {
    extensions: ['.zip', '.rar', '.7z', '.tar', '.gz'],
    maxSize: 200 * 1024 * 1024, // 200MB
    creditCost: 4,
    icon: '📦',
    category: 'Nén'
  },
  code: {
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.html', '.css', '.json', '.xml'],
    maxSize: 10 * 1024 * 1024, // 10MB
    creditCost: 2,
    icon: '💻',
    category: 'Code'
  }
};

// User tier configurations
export const USER_TIERS = {
  free: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFilesPerDay: 10,
    maxStorageTotal: 100 * 1024 * 1024, // 100MB
    allowedTypes: ['image', 'document', 'code']
  },
  premium: {
    maxFileSize: 100 * 1024 * 1024, // 100MB
    maxFilesPerDay: 50,
    maxStorageTotal: 1024 * 1024 * 1024, // 1GB
    allowedTypes: Object.keys(FILE_TYPES)
  },
  enterprise: {
    maxFileSize: 500 * 1024 * 1024, // 500MB
    maxFilesPerDay: 200,
    maxStorageTotal: 10 * 1024 * 1024 * 1024, // 10GB
    allowedTypes: Object.keys(FILE_TYPES)
  }
};

export const FileUploadProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const { hasEnoughCredits, consumeCredits } = useCredit();
  
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState(null);
  const [fileHistory, setFileHistory] = useState([]);

  // Initialize file history when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadFileHistory();
    } else {
      setUploadedFiles([]);
      setFileHistory([]);
    }
  }, [isAuthenticated, user?.id]); // Only depend on user.id instead of entire user object

  // Get user tier
  const getUserTier = () => {
    if (!user) return 'free';
    if (user.role === 'admin') return 'enterprise';
    if (user.credits > 500) return 'premium';
    return 'free';
  };

  // Get file type from extension
  const getFileType = (fileName) => {
    const extension = '.' + fileName.split('.').pop().toLowerCase();
    
    for (const [type, config] of Object.entries(FILE_TYPES)) {
      if (config.extensions.includes(extension)) {
        return type;
      }
    }
    return 'document'; // Default fallback
  };

  // Validate file
  const validateFile = (file) => {
    const userTier = getUserTier();
    const tierConfig = USER_TIERS[userTier];
    const fileType = getFileType(file.name);
    const fileConfig = FILE_TYPES[fileType];

    // Check if file type is allowed for user tier
    if (!tierConfig.allowedTypes.includes(fileType)) {
      return {
        valid: false,
        error: `Loại file ${fileConfig.category} không được hỗ trợ cho gói ${userTier}`
      };
    }

    // Check file size against user tier limit
    if (file.size > tierConfig.maxFileSize) {
      return {
        valid: false,
        error: `File quá lớn. Giới hạn cho gói ${userTier}: ${formatFileSize(tierConfig.maxFileSize)}`
      };
    }

    // Check file size against file type limit
    if (file.size > fileConfig.maxSize) {
      return {
        valid: false,
        error: `File ${fileConfig.category} quá lớn. Giới hạn: ${formatFileSize(fileConfig.maxSize)}`
      };
    }

    // Check credit requirements
    if (!hasEnoughCredits('fileUpload')) {
      return {
        valid: false,
        error: `Không đủ credit để upload file. Cần ${fileConfig.creditCost} credits`
      };
    }

    return { valid: true };
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Simulate file upload
  const uploadFile = async (file, onProgress) => {
    const validation = validateFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    const fileType = getFileType(file.name);
    const fileConfig = FILE_TYPES[fileType];
    const fileId = `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      setIsUploading(true);
      setUploadError(null);

      // Simulate upload progress
      for (let progress = 0; progress <= 100; progress += 10) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setUploadProgress(prev => ({ ...prev, [fileId]: progress }));
        onProgress?.(progress);
      }

      // Consume credits
      await consumeCredits('fileUpload', `Upload file: ${file.name}`);

      // Create file record
      const fileRecord = {
        id: fileId,
        name: file.name,
        size: file.size,
        type: fileType,
        category: fileConfig.category,
        icon: fileConfig.icon,
        creditCost: fileConfig.creditCost,
        uploadDate: new Date(),
        url: URL.createObjectURL(file), // Mock URL for preview
        status: 'completed'
      };

      // Add to uploaded files
      setUploadedFiles(prev => [...prev, fileRecord]);
      setFileHistory(prev => [fileRecord, ...prev]);

      // Clear progress
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[fileId];
        return newProgress;
      });

      return fileRecord;

    } catch (error) {
      setUploadError(error.message);
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  // Upload multiple files
  const uploadMultipleFiles = async (files, onProgress) => {
    const results = [];
    const totalFiles = files.length;

    for (let i = 0; i < files.length; i++) {
      try {
        const result = await uploadFile(files[i], (fileProgress) => {
          const overallProgress = ((i * 100) + fileProgress) / totalFiles;
          onProgress?.(overallProgress);
        });
        results.push(result);
      } catch (error) {
        results.push({ error: error.message, file: files[i] });
      }
    }

    return results;
  };

  // Load file history (mock)
  const loadFileHistory = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock file history
      const mockHistory = [
        {
          id: 'file_001',
          name: 'document.pdf',
          size: 2048576,
          type: 'document',
          category: 'Tài liệu',
          icon: '📄',
          creditCost: 3,
          uploadDate: new Date(Date.now() - 86400000),
          status: 'completed'
        },
        {
          id: 'file_002',
          name: 'image.jpg',
          size: 1024768,
          type: 'image',
          category: 'Hình ảnh',
          icon: '🖼️',
          creditCost: 2,
          uploadDate: new Date(Date.now() - 3600000),
          status: 'completed'
        }
      ];
      
      setFileHistory(mockHistory);
    } catch (error) {
      console.error('Error loading file history:', error);
    }
  };

  // Delete file
  const deleteFile = async (fileId) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
      setFileHistory(prev => prev.filter(f => f.id !== fileId));
      
      return { success: true };
    } catch (error) {
      throw new Error('Không thể xóa file');
    }
  };

  // Get storage usage
  const getStorageUsage = () => {
    const totalSize = fileHistory.reduce((sum, file) => sum + file.size, 0);
    const userTier = getUserTier();
    const maxStorage = USER_TIERS[userTier].maxStorageTotal;
    
    return {
      used: totalSize,
      total: maxStorage,
      percentage: (totalSize / maxStorage) * 100
    };
  };

  // Clear upload error
  const clearUploadError = () => setUploadError(null);

  const value = {
    // State
    uploadedFiles,
    uploadProgress,
    isUploading,
    uploadError,
    fileHistory,
    
    // Actions
    uploadFile,
    uploadMultipleFiles,
    validateFile,
    deleteFile,
    loadFileHistory,
    clearUploadError,
    
    // Utilities
    getFileType,
    formatFileSize,
    getUserTier,
    getStorageUsage,
    
    // Constants
    FILE_TYPES,
    USER_TIERS
  };

  return (
    <FileUploadContext.Provider value={value}>
      {children}
    </FileUploadContext.Provider>
  );
};

export default FileUploadProvider;
