name: 🚀 Deploy to IMTA AI VPS (Ubuntu)

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

env:
  SERVER_HOST: ${{ secrets.SERVER_HOST }}
  SERVER_USER: ${{ secrets.SERVER_USER }}
  PROJECT_PATH: ${{ secrets.PROJECT_PATH }}

jobs:
  test:
    name: 🧪 Test Application
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 📦 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: 📦 Install dependencies
      run: |
        npm install
        cd server && npm install
        cd ../client && npm install
        
    - name: 🧪 Run tests
      run: |
        cd server && npm test || echo "No tests configured yet"
        
    - name: 🏗️ Build client
      run: |
        cd client && npm run build
        
  deploy:
    name: 🚀 Deploy to Production (Ubuntu)
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🔐 Setup SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
        
    - name: 🌐 Test SSH connection
      env:
        SERVER_HOST: ${{ secrets.SERVER_HOST }}
        SERVER_USER: ${{ secrets.SERVER_USER }}
      run: |
        echo "Testing SSH connection to $SERVER_USER@$SERVER_HOST..."
        
        # Add server to known hosts with strict host key checking disabled
        ssh-keyscan -H $SERVER_HOST >> ~/.ssh/known_hosts 2>/dev/null || echo "Failed to add host to known_hosts"
        
        # Test SSH connection
        ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 $SERVER_USER@$SERVER_HOST "echo 'SSH connection successful'" || {
          echo "❌ SSH connection failed!"
          echo "Please check:"
          echo "1. SERVER_HOST is correct: $SERVER_HOST"
          echo "2. SERVER_USER is correct: $SERVER_USER"
          echo "3. SSH_PRIVATE_KEY is valid"
          echo "4. Server is accessible from GitHub Actions"
          exit 1
        }
        
    - name: 📋 Deploy to Ubuntu server
      env:
        SERVER_HOST: ${{ secrets.SERVER_HOST }}
        SERVER_USER: ${{ secrets.SERVER_USER }}
        PROJECT_PATH: ${{ secrets.PROJECT_PATH }}
      run: |
        # Create deployment script for Ubuntu
        cat > deploy-ubuntu.sh << 'EOF'
        #!/bin/bash
        
        # Colors for output
        RED='\033[0;31m'
        GREEN='\033[0;32m'
        YELLOW='\033[1;33m'
        BLUE='\033[0;34m'
        NC='\033[0m'
        
        echo -e "${BLUE}🚀 Deploying IMTA AI to Ubuntu server...${NC}"
        
        # Function to execute command on remote server
        execute_remote() {
            local command="$1"
            echo -e "${YELLOW}Executing: $command${NC}"
            ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_HOST "$command"
        }
        
        # Step 1: Connect and prepare server
        echo -e "${BLUE}📋 Step 1: Preparing server...${NC}"
        
        # Create project directory and pull latest code
        execute_remote "mkdir -p $PROJECT_PATH && cd $PROJECT_PATH && git fetch origin && git reset --hard origin/main"
        
        # Step 2: Setup environment files
        echo -e "${BLUE}📋 Step 2: Setting up environment...${NC}"
        
        # Create environment files if they don't exist
        execute_remote "cd $PROJECT_PATH && [ ! -f server/.env ] && cp server/env.example server/.env 2>/dev/null || echo 'No env.example found'"
        execute_remote "cd $PROJECT_PATH && [ ! -f client/.env ] && cp client/env.example client/.env 2>/dev/null || echo 'No env.example found'"
        
        # Step 3: Configure production environment
        echo -e "${BLUE}📋 Step 3: Configuring production environment...${NC}"
        
        # Update server environment if .env exists
        execute_remote "cd $PROJECT_PATH && [ -f server/.env ] && sed -i 's/NODE_ENV=development/NODE_ENV=production/' server/.env || echo 'No server/.env to update'"
        execute_remote "cd $PROJECT_PATH && [ -f server/.env ] && sed -i 's/MONGODB_URI=.*/MONGODB_URI=mongodb:\/\/imta_user:imta123456@mongodb:27017\/imta-ai/' server/.env || echo 'No server/.env to update'"
        execute_remote "cd $PROJECT_PATH && [ -f server/.env ] && sed -i 's/REDIS_HOST=.*/REDIS_HOST=redis/' server/.env || echo 'No server/.env to update'"
        execute_remote "cd $PROJECT_PATH && [ -f server/.env ] && sed -i 's/MINIO_ENDPOINT=.*/MINIO_ENDPOINT=minio/' server/.env || echo 'No server/.env to update'"
        execute_remote "cd $PROJECT_PATH && [ -f server/.env ] && sed -i 's/CORS_ORIGIN=.*/CORS_ORIGIN=*/' server/.env || echo 'No server/.env to update'"
        
        # Update client environment if .env exists
        execute_remote "cd $PROJECT_PATH && [ -f client/.env ] && sed -i 's/REACT_APP_API_URL=.*/REACT_APP_API_URL=https:\/\/imta.ai\/api/' client/.env || echo 'No client/.env to update'"
        execute_remote "cd $PROJECT_PATH && [ -f client/.env ] && sed -i 's/REACT_APP_ENV=.*/REACT_APP_ENV=production/' client/.env || echo 'No client/.env to update'"
        
        # Step 4: Setup permissions and dependencies
        echo -e "${BLUE}📋 Step 4: Setting up permissions and dependencies...${NC}"
        
        execute_remote "cd $PROJECT_PATH && chmod +x start.sh deploy-prod.sh check-docker.sh backup.sh monitor.sh 2>/dev/null || echo 'Some scripts not found'"
        execute_remote "cd $PROJECT_PATH && npm install"
        execute_remote "cd $PROJECT_PATH/server && npm install"
        execute_remote "cd $PROJECT_PATH/client && npm install"
        
        # Step 5: Update Nginx configuration
        echo -e "${BLUE}📋 Step 5: Updating Nginx configuration...${NC}"
        
        # Copy Nginx configuration to server if it exists
        execute_remote "cd $PROJECT_PATH && [ -f nginx-ubuntu.conf ] && sudo cp nginx-ubuntu.conf /etc/nginx/sites-available/imta-ai || echo 'No nginx-ubuntu.conf found'"
        execute_remote "cd $PROJECT_PATH && [ -f nginx-ubuntu.conf ] && sudo ln -sf /etc/nginx/sites-available/imta-ai /etc/nginx/sites-enabled/imta-ai || echo 'No nginx config to link'"
        
        # Test Nginx configuration
        execute_remote "sudo nginx -t || echo 'Nginx config test failed'"
        
        # Step 6: Deploy with Docker
        echo -e "${BLUE}📋 Step 6: Deploying with Docker...${NC}"
        
        # Stop any existing containers
        execute_remote "cd $PROJECT_PATH && docker-compose -f docker-compose.prod.yml down --remove-orphans 2>/dev/null || echo 'No containers to stop'"
        
        # Build and start production
        execute_remote "cd $PROJECT_PATH && docker-compose -f docker-compose.prod.yml up -d --build || echo 'Docker deployment failed'"
        
        # Step 7: Verify deployment
        echo -e "${BLUE}📋 Step 7: Verifying deployment...${NC}"
        
        # Wait for services to start
        sleep 30
        
        # Check container status
        execute_remote "cd $PROJECT_PATH && docker-compose -f docker-compose.prod.yml ps"
        
        # Test health endpoint
        echo -e "${BLUE}📋 Testing health endpoint...${NC}"
        execute_remote "curl -f http://localhost:5001/api/health || echo 'Health check failed'"
        
        # Reload Nginx
        execute_remote "sudo systemctl reload nginx || echo 'Nginx reload failed'"
        
        # Step 8: Show access information
        echo -e "${GREEN}🎉 Deployment completed!${NC}"
        echo -e "${BLUE}🌐 Access URLs:${NC}"
        echo -e "${GREEN}  • Main Application: https://imta.ai${NC}"
        echo -e "${GREEN}  • API Health Check: https://imta.ai/api/health${NC}"
        echo -e "${GREEN}  • Mongo Express: https://imta.ai:8081${NC}"
        echo -e "${GREEN}  • Redis Commander: https://imta.ai:8082${NC}"
        echo -e "${GREEN}  • MinIO Console: https://imta.ai:9001${NC}"
        
        EOF
        
        # Make script executable and run
        chmod +x deploy-ubuntu.sh
        ./deploy-ubuntu.sh
        
    - name: 📧 Notify deployment status
      if: always()
      run: |
        if [ ${{ job.status }} == 'success' ]; then
          echo "✅ Deployment successful!"
          echo "🌐 Application: https://imta.ai"
          echo "🔗 Health Check: https://imta.ai/api/health"
        else
          echo "❌ Deployment failed!"
        fi 