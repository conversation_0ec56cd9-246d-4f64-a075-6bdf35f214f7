import React, { useState, useEffect, useRef } from 'react';

const SlowStreamingText = ({
  text,
  speed = 0.5, // EXTREMELY SLOW: 0.5 character per second (2 seconds per character)
  onComplete,
  onStart,
  className = '',
  isStreaming = true
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  const intervalRef = useRef(null);
  const indexRef = useRef(0);

  // Reset when text changes
  useEffect(() => {
    if (text) {
      setDisplayText('');
      setIsComplete(false);
      indexRef.current = 0;
      
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      
      onStart?.();
      startTyping();
    }
  }, [text]);

  const startTyping = () => {
    const targetText = text || '';
    if (!targetText) return;

    const interval = 1000 / speed; // milliseconds per character
    
    intervalRef.current = setInterval(() => {
      const currentIndex = indexRef.current;
      
      if (currentIndex < targetText.length) {
        const newDisplayText = targetText.substring(0, currentIndex + 1);
        setDisplayText(newDisplayText);
        indexRef.current++;
        
        console.log('🔤 Typing character:', targetText[currentIndex], 'Index:', currentIndex + 1, '/', targetText.length);
      } else {
        // Animation complete
        clearInterval(intervalRef.current);
        setIsComplete(true);
        onComplete?.();
        console.log('✅ Typing animation completed');
      }
    }, interval);
  };

  // Cleanup
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const streamingClass = isComplete ? 'streaming-text completed' : 'streaming-text';
  const combinedClassName = `${streamingClass} ${className}`.trim();

  return (
    <span className={combinedClassName}>
      {displayText}
    </span>
  );
};

export default SlowStreamingText;
