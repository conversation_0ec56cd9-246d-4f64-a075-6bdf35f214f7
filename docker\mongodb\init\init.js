// MongoDB initialization script for Imta AI
db = db.getSiblingDB('imta-ai');

// Create application user
db.createUser({
  user: 'imta_user',
  pwd: 'imta123456',
  roles: [
    {
      role: 'readWrite',
      db: 'imta-ai'
    }
  ]
});

// Create collections with validation
db.createCollection('users');
db.createCollection('credits');
db.createCollection('chats');
db.createCollection('messages');

// Create indexes for better performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "status": 1 });

db.credits.createIndex({ "userId": 1 }, { unique: true });
db.credits.createIndex({ "balance": 1 });

db.chats.createIndex({ "userId": 1 });
db.chats.createIndex({ "type": 1 });
db.chats.createIndex({ "status": 1 });
db.chats.createIndex({ "createdAt": -1 });

db.messages.createIndex({ "chatId": 1 });
db.messages.createIndex({ "timestamp": -1 });
db.messages.createIndex({ "sender": 1 });
db.messages.createIndex({ "contentType": 1 });

// Insert sample data (optional)
db.users.insertOne({
  username: 'admin',
  email: '<EMAIL>',
  fullName: 'Admin User',
  role: 'admin',
  status: 'active',
  registrationDate: new Date(),
  lastLogin: new Date(),
  preferences: {
    language: 'vi',
    theme: 'light',
    notifications: true
  },
  metadata: {
    source: 'system',
    tags: ['admin', 'system']
  }
});

// Create credit record for admin
db.credits.insertOne({
  userId: db.users.findOne({ username: 'admin' })._id,
  balance: 1000,
  totalEarned: 1000,
  totalSpent: 0,
  transactions: [],
  lastUpdated: new Date()
});

print('MongoDB initialization completed successfully!'); 