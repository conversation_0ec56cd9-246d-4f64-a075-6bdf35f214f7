import React, { useState } from 'react';
import { useChat } from '../context/ChatContext';
import { Bug, Eye, EyeOff } from 'lucide-react';

const DebugPanel = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { conversations, currentConversationId, isLoading } = useChat();

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-2 sm:bottom-4 right-2 sm:right-4 bg-red-500 text-white p-2 rounded-full shadow-lg hover:bg-red-600 z-30"
        title="Show Debug Panel"
      >
        <Bug className="w-4 h-4 sm:w-5 sm:h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-2 sm:bottom-4 right-2 sm:right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-3 sm:p-4 max-w-xs sm:max-w-md max-h-80 sm:max-h-96 overflow-auto z-30">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-bold text-gray-800 flex items-center gap-2">
          <Bug className="w-4 h-4" />
          Debug Panel
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          <EyeOff className="w-4 h-4" />
        </button>
      </div>
      
      <div className="space-y-3 text-sm">
        <div>
          <strong>Total Conversations:</strong> {conversations.length}
        </div>
        
        <div>
          <strong>Current Conversation ID:</strong> 
          <br />
          <code className="text-xs bg-gray-100 p-1 rounded">
            {currentConversationId || 'null'}
          </code>
        </div>
        
        <div>
          <strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}
        </div>
        
        <div>
          <strong>Conversations List:</strong>
          <div className="mt-1 space-y-1">
            {conversations.map((conv, index) => (
              <div key={conv.id} className="bg-gray-50 p-2 rounded text-xs">
                <div><strong>#{index + 1}</strong></div>
                <div><strong>ID:</strong> {conv.id.substring(0, 8)}...</div>
                <div><strong>Title:</strong> {conv.title}</div>
                <div><strong>Messages:</strong> {conv.messages.length}</div>
                <div><strong>Current:</strong> {conv.id === currentConversationId ? 'Yes' : 'No'}</div>
              </div>
            ))}
          </div>
        </div>
        
        <button
          onClick={() => {
            localStorage.clear();
            window.location.reload();
          }}
          className="w-full bg-red-500 text-white py-1 px-2 rounded text-xs hover:bg-red-600"
        >
          Clear localStorage & Reload
        </button>
      </div>
    </div>
  );
};

export default DebugPanel;
