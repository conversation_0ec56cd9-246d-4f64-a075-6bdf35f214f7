// Image URL patterns
const IMAGE_URL_PATTERNS = [
  // Direct image URLs
  /https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp|svg|bmp|ico)(?:\?[^\s]*)?/gi,
  // Common image hosting services
  /https?:\/\/(?:i\.)?imgur\.com\/[^\s]+/gi,
  /https?:\/\/[^\s]*\.(?:imgur|flickr|photobucket|tinypic)\.com\/[^\s]+/gi,
  // Social media image URLs
  /https?:\/\/[^\s]*\.(?:facebook|twitter|instagram)\.com\/[^\s]+/gi,
  // CDN and cloud storage
  /https?:\/\/[^\s]*\.(?:cloudfront|amazonaws|googleusercontent|dropbox)\.com\/[^\s]+\.(?:jpg|jpeg|png|gif|webp|svg)/gi,
];

// Image file extensions
const IMAGE_EXTENSIONS = [
  'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'ico', 'tiff', 'tif'
];

/**
 * Detect image URLs in text
 * @param {string} text - Text to search for image URLs
 * @returns {Array} Array of detected image URLs
 */
export const detectImageUrls = (text) => {
  if (!text || typeof text !== 'string') return [];

  const urls = new Set();

  // Apply all patterns
  IMAGE_URL_PATTERNS.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(url => urls.add(url));
    }
  });

  // Additional check for any URL ending with image extension
  const urlPattern = /https?:\/\/[^\s]+/gi;
  const allUrls = text.match(urlPattern) || [];
  
  allUrls.forEach(url => {
    const cleanUrl = url.replace(/[.,;!?]$/, ''); // Remove trailing punctuation
    const extension = cleanUrl.split('.').pop()?.toLowerCase();
    if (extension && IMAGE_EXTENSIONS.includes(extension)) {
      urls.add(cleanUrl);
    }
  });

  return Array.from(urls);
};

/**
 * Check if a URL is likely an image
 * @param {string} url - URL to check
 * @returns {boolean} True if URL is likely an image
 */
export const isImageUrl = (url) => {
  if (!url || typeof url !== 'string') return false;

  // Check extension
  const extension = url.split('.').pop()?.toLowerCase().split('?')[0];
  if (extension && IMAGE_EXTENSIONS.includes(extension)) {
    return true;
  }

  // Check against patterns
  return IMAGE_URL_PATTERNS.some(pattern => pattern.test(url));
};

/**
 * Extract images and text separately from content
 * @param {string} content - Content to parse
 * @returns {Object} Object with text and images arrays
 */
export const parseContentWithImages = (content) => {
  if (!content || typeof content !== 'string') {
    return { text: '', images: [] };
  }

  const images = detectImageUrls(content);
  let text = content;

  // Remove image URLs from text
  images.forEach(imageUrl => {
    text = text.replace(new RegExp(escapeRegExp(imageUrl), 'gi'), '').trim();
  });

  // Clean up extra whitespace
  text = text.replace(/\s+/g, ' ').trim();

  return {
    text,
    images: images.map((url, index) => ({
      id: `img_${Date.now()}_${index}`,
      url,
      alt: `Image ${index + 1}`,
      type: 'url'
    }))
  };
};

/**
 * Escape special regex characters
 * @param {string} string - String to escape
 * @returns {string} Escaped string
 */
const escapeRegExp = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

/**
 * Validate if image URL is accessible
 * @param {string} url - Image URL to validate
 * @returns {Promise<boolean>} Promise that resolves to true if image is accessible
 */
export const validateImageUrl = async (url) => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    const contentType = response.headers.get('content-type');
    return response.ok && contentType && contentType.startsWith('image/');
  } catch (error) {
    return false;
  }
};

/**
 * Get image metadata from URL
 * @param {string} url - Image URL
 * @returns {Promise<Object>} Promise that resolves to image metadata
 */
export const getImageMetadata = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
        aspectRatio: img.naturalWidth / img.naturalHeight,
        size: null // Cannot get file size from URL without fetching
      });
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    
    img.src = url;
  });
};

/**
 * Create image object for display
 * @param {string} url - Image URL
 * @param {string} alt - Alt text
 * @param {string} type - Image type ('url' or 'file')
 * @returns {Object} Image object
 */
export const createImageObject = (url, alt = '', type = 'url') => {
  return {
    id: `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    url,
    alt: alt || `Image from ${type}`,
    type,
    createdAt: new Date().toISOString()
  };
};

/**
 * Format image for message display
 * @param {Object} imageData - Image data object
 * @returns {Object} Formatted image object
 */
export const formatImageForMessage = (imageData) => {
  return {
    id: imageData.id,
    url: imageData.url || imageData.preview,
    alt: imageData.alt || imageData.name || 'Image',
    type: imageData.type || 'file',
    size: imageData.size || null,
    width: imageData.width || null,
    height: imageData.height || null
  };
};
