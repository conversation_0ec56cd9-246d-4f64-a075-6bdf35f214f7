// Test the chat analyzer functionality
import { analyzeChatContext, generateChatTitle, getChatTypeDisplayName } from './chatAnalyzer.js';

// Test cases
const testMessages = [
  'Tôi muốn tìm hiểu về khóa học AI của bạn',
  '<PERSON><PERSON><PERSON> thế nào để tạo quảng cáo Facebook hiệu quả?',
  '<PERSON><PERSON><PERSON><PERSON> giúp tôi một bài blog về marketing',
  '<PERSON>ân tích hình ảnh avatar này cho tôi',
  'Xin chào, bạn có khỏe không?'
];

console.log('=== Testing Chat Analyzer ===');

testMessages.forEach((message, index) => {
  console.log(`\nTest ${index + 1}: "${message}"`);
  const result = analyzeChatContext(message);
  console.log('Result:', {
    type: result.type,
    title: result.title,
    metadata: result.metadata,
    displayName: getChatTypeDisplayName(result.type)
  });
});

// Expected results:
// 1. course_inquiry with courseId
// 2. ads_analysis
// 3. content_gen
// 4. avatar_analysis
// 5. general
