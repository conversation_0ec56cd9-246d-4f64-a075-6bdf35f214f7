const mongoose = require('mongoose');

// Sub-schemas for different types of metadata
const attachmentSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['image', 'pdf', 'excel', 'csv', 'chart'],
    required: true
  },
  url: {
    type: String,
    required: true
  },
  filename: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    min: [0, 'Size cannot be negative']
  }
});

const chartSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['line', 'bar', 'pie', 'scatter'],
    required: true
  },
  title: {
    type: String,
    required: true
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  imageUrl: {
    type: String
  }
});

const adsAnalysisSchema = new mongoose.Schema({
  summary: {
    type: String,
    trim: true
  },
  insights: [{
    type: String,
    trim: true
  }],
  recommendations: [{
    type: String,
    trim: true
  }],
  charts: [chartSchema],
  metrics: {
    totalSpend: Number,
    impressions: Number,
    clicks: Number,
    conversions: Number,
    roas: Number
  }
});

const contentGenerationSchema = new mongoose.Schema({
  title: {
    type: String,
    trim: true
  },
  body: {
    type: String,
    trim: true
  },
  hashtags: [{
    type: String,
    trim: true
  }],
  callToAction: {
    type: String,
    trim: true
  },
  platform: {
    type: String,
    enum: ['facebook', 'instagram', 'tiktok', 'google', 'linkedin']
  },
  tone: {
    type: String,
    enum: ['professional', 'casual', 'friendly', 'formal', 'creative']
  },
  length: {
    type: String,
    enum: ['short', 'medium', 'long']
  }
});

const demographicsSchema = new mongoose.Schema({
  age: {
    type: String,
    enum: ['18-25', '26-35', '36-45', '46-55', '55+']
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other']
  },
  ethnicity: {
    type: String
  }
});

const personalitySchema = new mongoose.Schema({
  traits: [{
    type: String,
    trim: true
  }],
  style: {
    type: String,
    enum: ['casual', 'business', 'creative', 'formal']
  },
  mood: {
    type: String,
    enum: ['friendly', 'serious', 'energetic', 'calm', 'professional']
  }
});

const avatarAnalysisSchema = new mongoose.Schema({
  demographics: demographicsSchema,
  personality: personalitySchema,
  recommendations: [{
    type: String,
    trim: true
  }],
  score: {
    type: Number,
    min: [1, 'Score must be at least 1'],
    max: [10, 'Score cannot exceed 10']
  }
});

const aiResponseSchema = new mongoose.Schema({
  model: {
    type: String,
    trim: true
  },
  confidence: {
    type: Number,
    min: [0, 'Confidence must be at least 0'],
    max: [1, 'Confidence cannot exceed 1']
  },
  processingTime: {
    type: Number,
    min: [0, 'Processing time cannot be negative']
  }
});

const messageSchema = new mongoose.Schema({
  chatId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Chat',
    required: true
  },
  sender: {
    type: String,
    enum: ['user', 'bot'],
    required: true
  },
  content: {
    type: String,
    required: true,
    trim: true
  },
  contentType: {
    type: String,
    enum: ['text', 'image', 'file', 'chart', 'analysis'],
    default: 'text'
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  metadata: {
    attachments: [attachmentSchema],
    adsAnalysis: adsAnalysisSchema,
    contentGeneration: contentGenerationSchema,
    avatarAnalysis: avatarAnalysisSchema,
    aiResponse: aiResponseSchema,
    creditCost: {
      type: Number,
      default: 0,
      min: [0, 'Credit cost cannot be negative']
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for formatted timestamp
messageSchema.virtual('formattedTimestamp').get(function() {
  return this.timestamp.toLocaleString();
});

// Virtual for message type
messageSchema.virtual('messageType').get(function() {
  if (this.metadata.adsAnalysis) return 'ads_analysis';
  if (this.metadata.contentGeneration) return 'content_generation';
  if (this.metadata.avatarAnalysis) return 'avatar_analysis';
  return this.contentType;
});

// Indexes
messageSchema.index({ chatId: 1 });
messageSchema.index({ timestamp: -1 });
messageSchema.index({ sender: 1 });
messageSchema.index({ contentType: 1 });

// Static methods
messageSchema.statics.findByChatId = function(chatId, options = {}) {
  const query = this.find({ chatId })
    .populate('chatId', 'title type')
    .sort({ timestamp: 1 });

  if (options.limit) {
    query.limit(options.limit);
  }
  if (options.skip) {
    query.skip(options.skip);
  }

  return query;
};

messageSchema.statics.findByType = function(contentType, options = {}) {
  const query = this.find({ contentType })
    .populate('chatId', 'title userId')
    .populate('chatId.userId', 'username email')
    .sort({ timestamp: -1 });

  if (options.limit) {
    query.limit(options.limit);
  }

  return query;
};

messageSchema.statics.findAnalysisMessages = function(chatId) {
  return this.find({
    chatId,
    $or: [
      { 'metadata.adsAnalysis': { $exists: true } },
      { 'metadata.contentGeneration': { $exists: true } },
      { 'metadata.avatarAnalysis': { $exists: true } }
    ]
  }).sort({ timestamp: -1 });
};

messageSchema.statics.getMessageStats = function(chatId) {
  return this.aggregate([
    { $match: { chatId: mongoose.Types.ObjectId(chatId) } },
    {
      $group: {
        _id: null,
        totalMessages: { $sum: 1 },
        userMessages: {
          $sum: { $cond: [{ $eq: ['$sender', 'user'] }, 1, 0] }
        },
        botMessages: {
          $sum: { $cond: [{ $eq: ['$sender', 'bot'] }, 1, 0] }
        },
        totalCreditCost: { $sum: '$metadata.creditCost' },
        avgProcessingTime: {
          $avg: '$metadata.aiResponse.processingTime'
        }
      }
    }
  ]);
};

// Instance methods
messageSchema.methods.addAttachment = function(attachment) {
  this.metadata.attachments.push(attachment);
  return this.save();
};

messageSchema.methods.setAdsAnalysis = function(analysis) {
  this.metadata.adsAnalysis = analysis;
  this.contentType = 'analysis';
  return this.save();
};

messageSchema.methods.setContentGeneration = function(content) {
  this.metadata.contentGeneration = content;
  this.contentType = 'analysis';
  return this.save();
};

messageSchema.methods.setAvatarAnalysis = function(analysis) {
  this.metadata.avatarAnalysis = analysis;
  this.contentType = 'analysis';
  return this.save();
};

messageSchema.methods.setAiResponse = function(response) {
  this.metadata.aiResponse = response;
  return this.save();
};

messageSchema.methods.setCreditCost = function(cost) {
  this.metadata.creditCost = cost;
  return this.save();
};

messageSchema.methods.hasAnalysis = function() {
  return !!(this.metadata.adsAnalysis || 
           this.metadata.contentGeneration || 
           this.metadata.avatarAnalysis);
};

messageSchema.methods.getAnalysisType = function() {
  if (this.metadata.adsAnalysis) return 'ads_analysis';
  if (this.metadata.contentGeneration) return 'content_generation';
  if (this.metadata.avatarAnalysis) return 'avatar_analysis';
  return null;
};

module.exports = mongoose.model('Message', messageSchema); 