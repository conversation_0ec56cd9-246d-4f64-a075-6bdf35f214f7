#!/bin/bash

echo "🔧 Complete IMTA-AI Deployment Fix..."

# 1. Update NGINX with correct Docker IP
echo "📍 Step 1: Update NGINX with Docker container IP"
./update-nginx-ip.sh

# 2. Sync latest assets 
echo "📁 Step 2: Sync assets from container"
./sync-assets.sh

# 3. Test everything
echo "🧪 Step 3: Testing deployment"

echo "Testing Container API directly:"
CONTAINER_IP=$(docker inspect imta-app-prod | grep -Po '"IPAddress": "\K[^"]*' | grep -v "^$" | head -1)
curl -s http://$CONTAINER_IP:5001/api/health

echo -e "\nTesting public API through NGINX:"
curl -s https://imta.ai/api/health

echo -e "\nTesting Frontend access:"
curl -I https://imta.ai/ 2>/dev/null | head -1

echo -e "\nTesting assets:"
curl -I https://imta.ai/assets/index-BQixJU2K.js 2>/dev/null | head -1

# 4. Verification
echo -e "\n📊 Current status:"
echo "Container IP: $CONTAINER_IP"
echo "NGINX proxy target: $(grep 'proxy_pass' /etc/nginx/sites-enabled/imta.ai.conf)"
echo "Assets in CloudPanel:"
ls -la /home/<USER>/htdocs/imta.ai/assets/ | wc -l
echo "Container status:"
docker ps --format "table {{.Names}}\t{{.Status}}" | grep imta-app

echo -e "\n✅ Fix completed!"

