import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import ChatArea from '../components/ChatArea';
import Sidebar from '../components/Sidebar';
import TopBar from '../components/TopBar';
import PaymentModal from '../components/PaymentModal';
import { useSidebar } from '../context/SidebarContext';

const Chat = () => {
  const { user } = useAuth();
  const { isMobile, isSidebarCollapsed } = useSidebar();
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(null);

  // Handle payment success
  const handlePaymentSuccess = (result) => {
    setPaymentSuccess(result);
    setTimeout(() => setPaymentSuccess(null), 5000);
  };

  return (
    <div className="h-screen flex bg-gray-100">
      {/* Payment Success Notification */}
      {paymentSuccess && (
        <div className="fixed top-4 right-4 z-50 bg-green-50 border border-green-200 rounded-lg p-4 shadow-lg">
          <div className="flex items-center gap-2 text-green-700">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="font-medium">
              Nạp credit thành công! +{paymentSuccess.transaction?.amount || 0} credits
            </span>
          </div>
        </div>
      )}

      {/* Sidebar */}
      <Sidebar />

      {/* Main Chat Area */}
      <div className={`
        flex-1 flex flex-col transition-all duration-300 ease-in-out
        ${!isMobile && !isSidebarCollapsed ? 'ml-64' : 'ml-0'}
      `}>
        {/* Top Bar - Always show, responsive design */}
        <TopBar onTopUpClick={() => setShowPaymentModal(true)} />

        {/* Chat Area */}
        <ChatArea />
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        onSuccess={handlePaymentSuccess}
      />
    </div>
  );
};

export default Chat;
