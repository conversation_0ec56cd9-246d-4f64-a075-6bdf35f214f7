import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, User, Mail, Calendar, Shield, Edit3, Save, X, Loader2, CheckCircle } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import TopBar from '../components/TopBar';
import PaymentModal from '../components/PaymentModal';

const Profile = () => {
  const { user, updateProfile, logout, refreshUserProfile, profileLoading } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [validationErrors, setValidationErrors] = useState({});
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(null);
  // Dynamic class for role color
  const roleColorClass = user?.role === 'student' ? 'text-green-600' : 'text-gray-500';

  // Handle payment success
  const handlePaymentSuccess = (result) => {
    setPaymentSuccess(result);
    setTimeout(() => setPaymentSuccess(null), 5000);
  };

  // Initialize form data when user data is available
  useEffect(() => {
    if (user) {
      setFormData({
        name: user.fullName || user.name || '',
        email: user.email || ''
      });
    }
  }, [user]);

  // Fetch fresh user profile on component mount
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        console.log('🔄 Profile page: Fetching fresh user data...');
        await refreshUserProfile();
        console.log('✅ Profile page: User data refreshed');
      } catch (error) {
        console.error('❌ Profile page: Failed to fetch user data:', error);
        // Don't show error to user since we have fallback data
      }
    };

    // Only fetch if user is already authenticated
    if (user) {
      fetchUserProfile();
    }
  }, []); // Run once on mount

  // Clear success message after 3 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear validation errors when user types
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Họ tên là bắt buộc';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Họ tên phải có ít nhất 2 ký tự';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email là bắt buộc';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email không hợp lệ';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle save profile
  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await updateProfile({
        name: formData.name.trim(),
        email: formData.email.trim()
      });
      
      setIsEditing(false);
      setSuccessMessage('Thông tin đã được cập nhật thành công!');
    } catch (error) {
      setValidationErrors({
        general: error.message || 'Có lỗi xảy ra khi cập nhật thông tin'
      });
    } finally {
      setIsLoading(false);
    }
  };
  // Handle cancel edit
  const handleCancel = () => {
    setFormData({
      name: user?.fullName || user?.name || '',
      email: user?.email || ''
    });
    setIsEditing(false);
    setValidationErrors({});
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">
            {profileLoading ? 'Đang tải thông tin người dùng...' : 'Đang kiểm tra thông tin...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#F9FAFB' }}>
      {/* Payment Success Notification */}
      {paymentSuccess && (
        <div className="fixed top-4 right-4 z-50 bg-green-50 border border-green-200 rounded-lg p-4 shadow-lg">
          <div className="flex items-center gap-2 text-green-700">
            <CheckCircle className="w-5 h-5" />
            <span className="font-medium">
              Nạp credit thành công! +{paymentSuccess.transaction?.amount || 0} credits
            </span>
          </div>
        </div>
      )}

      {/* TopBar */}
      <TopBar onTopUpClick={() => setShowPaymentModal(true)} />

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white/70 backdrop-blur-lg rounded-xl shadow-xl ring-1 ring-black/10">
          {/* Profile Header */}
          <div className="px-6 py-8 border-b border-gray-200">
            <div className="flex items-center gap-6">
              {/* Avatar */}
              <div className="w-24 h-24 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full p-1 shadow-lg">
                <div className="w-full h-full bg-white rounded-full flex items-center justify-center">
                  <User className="w-10 h-10 text-indigo-600" />
                </div>
              </div>
                {/* User Info */}
              <div className="flex-1">
                <h2 className="text-3xl font-bold text-gray-900">
                  {user.fullName || user.name || 'Tên người dùng'}
                </h2>
                <p className="text-gray-600">{user.email || '<EMAIL>'}</p>
                <div className="flex items-center gap-4 mt-2">
                  <div className="flex items-center gap-1 text-sm text-gray-500">
                    <Shield className="w-4 h-4" />
                    <span className={`capitalize ${roleColorClass}`}>
                      {user.role || 'student'}
                    </span>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-gray-500">
                    <Calendar className="w-4 h-4" />
                    <span>
                      Tham gia {user.registrationDate 
                        ? formatDate(user.registrationDate) 
                        : formatDate(user.createdAt || new Date())
                      }
                    </span>
                  </div>
                </div>
              </div>

              {/* Edit Button */}
              {!isEditing && (
                <button
                  onClick={() => setIsEditing(true)}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Edit3 className="w-4 h-4" />
                  Chỉnh sửa
                </button>
              )}
            </div>
          </div>

          {/* Success Message */}
          {successMessage && (
            <div className="mx-6 mt-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
              <p className="text-sm text-green-700">{successMessage}</p>
            </div>
          )}

          {/* General Error */}
          {validationErrors.general && (
            <div className="mx-6 mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-700">{validationErrors.general}</p>
            </div>          )}

          {isEditing && (
            <div className="px-6 py-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Name Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Họ và tên
                  </label>
                  <div>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`
                        w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500
                        ${validationErrors.name ? 'border-red-300' : 'border-gray-300'}
                      `}
                      placeholder="Nhập họ và tên"
                    />
                    {validationErrors.name && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.name}</p>
                    )}
                  </div>
                </div>

                {/* Email Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <div>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`
                        w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500
                        ${validationErrors.email ? 'border-red-300' : 'border-gray-300'}
                      `}
                      placeholder="Nhập email"
                    />
                    {validationErrors.email && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.email}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-3 mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={handleSave}
                  disabled={isLoading}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Save className="w-4 h-4" />
                  )}
                  {isLoading ? 'Đang lưu...' : 'Lưu thay đổi'}
                </button>
                <button
                  onClick={handleCancel}
                  disabled={isLoading}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 transition-colors"
                >
                  <X className="w-4 h-4" />
                  Hủy
                </button>
              </div>
            </div>
          )}

          {/* Additional Actions */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div className="flex flex-col sm:flex-row gap-3">
              <Link
                to="/change-password"
                className="flex items-center justify-center gap-2 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
              >
                <Shield className="w-4 h-4" />
                Đổi mật khẩu
              </Link>
              <button
                onClick={logout}
                className="flex items-center justify-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Đăng xuất
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        onSuccess={handlePaymentSuccess}
      />
    </div>
  );
};

export default Profile;
