<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Very Slow Character-by-Character Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f8ff;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .demo-text {
            font-size: 20px;
            line-height: 1.8;
            margin: 20px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 5px solid #007bff;
            min-height: 60px;
        }
        
        .streaming-text {
            position: relative;
            display: inline;
        }
        
        .streaming-text::after {
            content: '|';
            animation: blink 1s infinite;
            color: #007bff;
            font-weight: bold;
            margin-left: 2px;
        }
        
        .streaming-text.completed::after {
            display: none;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        
        button {
            padding: 15px 25px;
            margin: 0 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.2s;
        }
        
        button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .speed-display {
            margin: 15px 0;
            padding: 10px;
            background: #e7f3ff;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
        }
        
        .char-counter {
            position: absolute;
            top: 10px;
            right: 15px;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .demo-title {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 15px;
            margin-bottom: 25px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="demo-title">🐌 Rất Chậm - Từng Chữ Một</h1>
        
        <div class="speed-display">
            Tốc độ: <span id="speedDisplay">2 ký tự/giây</span> (Cực chậm để dễ nhìn)
        </div>
        
        <div class="controls">
            <button onclick="startDemo()">Bắt Đầu Demo</button>
            <button onclick="resetDemo()">Reset</button>
            <button onclick="makeSlower()">Chậm Hơn</button>
            <button onclick="makeFaster()">Nhanh Hơn</button>
        </div>
        
        <div class="demo-text" style="position: relative;">
            <div class="char-counter" id="charCounter">0/0</div>
            <span id="streamingText" class="streaming-text"></span>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
            <strong>🎯 Mục đích:</strong><br>
            Demo hiệu ứng typing rất chậm để dễ quan sát từng ký tự xuất hiện.<br>
            Mỗi ký tự xuất hiện với khoảng cách thời gian rõ ràng, cursor nhấp nháy liên tục.
        </div>
    </div>
    
    <script>
        let currentSpeed = 2; // characters per second
        let currentInterval = null;
        let isAnimating = false;
        let currentIndex = 0;
        let targetText = '';
        
        const sampleTexts = [
            "Xin chào! Đây là demo typing cực chậm.",
            "Mỗi ký tự xuất hiện từ từ để bạn có thể thấy rõ.",
            "Hiệu ứng này giúp người dùng dễ đọc và theo dõi nội dung.",
            "Cursor sẽ nhấp nháy và biến mất khi hoàn thành.",
            "Bạn có thể điều chỉnh tốc độ bằng các nút trên."
        ];
        
        function updateSpeedDisplay() {
            document.getElementById('speedDisplay').textContent = `${currentSpeed} ký tự/giây`;
        }
        
        function updateCharCounter() {
            document.getElementById('charCounter').textContent = `${currentIndex}/${targetText.length}`;
        }
        
        function startDemo() {
            if (isAnimating) return;
            
            const textElement = document.getElementById('streamingText');
            targetText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
            
            textElement.textContent = '';
            textElement.className = 'streaming-text';
            currentIndex = 0;
            isAnimating = true;
            updateCharCounter();
            
            console.log('🎬 Starting very slow animation:', targetText);
            console.log('⚡ Speed:', currentSpeed, 'chars/sec');
            
            const intervalMs = 1000 / currentSpeed;
            console.log('⏱️ Interval:', intervalMs, 'ms per character');
            
            currentInterval = setInterval(() => {
                if (currentIndex < targetText.length) {
                    const char = targetText[currentIndex];
                    textElement.textContent += char;
                    currentIndex++;
                    updateCharCounter();
                    
                    console.log(`🔤 Added character "${char}" (${currentIndex}/${targetText.length})`);
                } else {
                    // Animation complete
                    clearInterval(currentInterval);
                    textElement.className = 'streaming-text completed';
                    isAnimating = false;
                    console.log('✅ Animation completed!');
                }
            }, intervalMs);
        }
        
        function resetDemo() {
            if (currentInterval) {
                clearInterval(currentInterval);
            }
            
            const textElement = document.getElementById('streamingText');
            textElement.textContent = 'Click "Bắt Đầu Demo" để xem hiệu ứng...';
            textElement.className = 'streaming-text completed';
            isAnimating = false;
            currentIndex = 0;
            targetText = textElement.textContent;
            updateCharCounter();
        }
        
        function makeSlower() {
            if (currentSpeed > 0.5) {
                currentSpeed = Math.max(0.5, currentSpeed - 0.5);
                updateSpeedDisplay();
                console.log('🐌 Speed decreased to:', currentSpeed);
            }
        }
        
        function makeFaster() {
            if (currentSpeed < 10) {
                currentSpeed = Math.min(10, currentSpeed + 0.5);
                updateSpeedDisplay();
                console.log('🚀 Speed increased to:', currentSpeed);
            }
        }
        
        // Initialize
        resetDemo();
        updateSpeedDisplay();
    </script>
</body>
</html>
