import React, { useState, useEffect, useRef } from 'react';

const TypewriterStreamingText = ({ 
  text = '', 
  speed = 50, 
  showCursor = true,
  cursorChar = '|',
  cursorBlinkSpeed = 530,
  onComplete = null,
  className = ''
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [showTypingCursor, setShowTypingCursor] = useState(true);
  const intervalRef = useRef(null);
  const blinkIntervalRef = useRef(null);

  // Handle typewriter effect
  useEffect(() => {
    if (!text) {
      setDisplayedText('');
      setCurrentIndex(0);
      setIsComplete(false);
      return;
    }

    // Clean the text from control characters
    const cleanText = text
      .replace(/▋+/g, '') // Remove typing indicators
      .replace(/C#\d+/g, '') // Remove C#1, C#2, etc.
      .replace(/\[[0-9;]*m/g, '') // Remove ANSI escape codes
      .trim();

    if (currentIndex < cleanText.length) {
      intervalRef.current = setTimeout(() => {
        setDisplayedText(prev => prev + cleanText[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);
    } else if (currentIndex >= cleanText.length && cleanText.length > 0) {
      if (!isComplete) {
        setIsComplete(true);
        setShowTypingCursor(false); // Hide cursor immediately when done
        if (onComplete) {
          onComplete();
        }
      }
    }

    return () => {
      if (intervalRef.current) {
        clearTimeout(intervalRef.current);
      }
    };
  }, [text, currentIndex, speed, isComplete, onComplete]);

  // Handle cursor blinking
  useEffect(() => {
    if (showCursor) {
      blinkIntervalRef.current = setInterval(() => {
        setShowTypingCursor(prev => !prev);
      }, cursorBlinkSpeed);
    }

    return () => {
      if (blinkIntervalRef.current) {
        clearInterval(blinkIntervalRef.current);
      }
    };
  }, [showCursor, cursorBlinkSpeed]);

  // Reset when text changes
  useEffect(() => {
    if (text !== displayedText) {
      setDisplayedText('');
      setCurrentIndex(0);
      setIsComplete(false);
      setShowTypingCursor(true);
    }
  }, [text]);

  return (
    <div className={`typewriter-container ${className}`}>
      <span className="typewriter-text whitespace-pre-wrap break-words">
        {displayedText}
        {showCursor && !isComplete && showTypingCursor && (
          <span className="typewriter-cursor animate-pulse">
            {cursorChar}
          </span>
        )}
      </span>
    </div>
  );
};

export default TypewriterStreamingText;
