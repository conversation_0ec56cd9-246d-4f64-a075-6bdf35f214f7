import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Download,
  Trash2,
  Eye,
  Calendar,
  HardDrive,
  Upload,
  Grid,
  List,
  SortAsc,
  SortDesc,
  File,
  Image,
  FileText,
  Music,
  Video,
  Archive,
  Code
} from 'lucide-react';
import { useFileUpload } from '../context/FileUploadContext';

const FileManager = ({ onUploadClick }) => {
  const {
    fileHistory,
    loadFileHistory,
    deleteFile,
    formatFileSize,
    getStorageUsage,
    FILE_TYPES
  } = useFileUpload();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load files on mount
  useEffect(() => {
    loadFileHistory();
  }, []);

  // Get file icon component
  const getFileIcon = (type) => {
    const iconMap = {
      image: Image,
      document: FileText,
      audio: Music,
      video: Video,
      archive: Archive,
      code: Code
    };
    return iconMap[type] || File;
  };

  // Filter and sort files
  const filteredFiles = fileHistory
    .filter(file => {
      const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || file.type === selectedCategory;
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'size':
          comparison = a.size - b.size;
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'date':
        default:
          comparison = new Date(a.uploadDate) - new Date(b.uploadDate);
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  // Get unique categories
  const categories = ['all', ...new Set(fileHistory.map(file => file.type))];

  // Handle file selection
  const toggleFileSelection = (fileId) => {
    setSelectedFiles(prev => 
      prev.includes(fileId) 
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedFiles.length === 0) return;
    
    if (window.confirm(`Bạn có chắc muốn xóa ${selectedFiles.length} files?`)) {
      setIsLoading(true);
      try {
        await Promise.all(selectedFiles.map(fileId => deleteFile(fileId)));
        setSelectedFiles([]);
      } catch (error) {
        alert('Có lỗi xảy ra khi xóa files');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Handle single file delete
  const handleSingleDelete = async (fileId) => {
    if (window.confirm('Bạn có chắc muốn xóa file này?')) {
      setIsLoading(true);
      try {
        await deleteFile(fileId);
      } catch (error) {
        alert('Có lỗi xảy ra khi xóa file');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Format date
  const formatDate = (date) => {
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  const storageUsage = getStorageUsage();

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Quản lý Files</h2>
          <button
            onClick={onUploadClick}
            className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <Upload className="w-4 h-4" />
            Upload Files
          </button>
        </div>

        {/* Storage Usage */}
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <HardDrive className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-900">Dung lượng đã sử dụng</span>
            </div>
            <span className="text-sm text-gray-600">
              {formatFileSize(storageUsage.used)} / {formatFileSize(storageUsage.total)}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                storageUsage.percentage > 90 ? 'bg-red-500' :
                storageUsage.percentage > 70 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(storageUsage.percentage, 100)}%` }}
            ></div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Tìm kiếm files..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">Tất cả loại</option>
            {categories.slice(1).map(category => (
              <option key={category} value={category}>
                {FILE_TYPES[category]?.category || category}
              </option>
            ))}
          </select>

          {/* Sort */}
          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order);
            }}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="date-desc">Mới nhất</option>
            <option value="date-asc">Cũ nhất</option>
            <option value="name-asc">Tên A-Z</option>
            <option value="name-desc">Tên Z-A</option>
            <option value="size-desc">Lớn nhất</option>
            <option value="size-asc">Nhỏ nhất</option>
          </select>

          {/* View Mode */}
          <div className="flex border border-gray-300 rounded-lg overflow-hidden">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedFiles.length > 0 && (
        <div className="p-4 bg-blue-50 border-b border-blue-200">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              Đã chọn {selectedFiles.length} files
            </span>
            <div className="flex gap-2">
              <button
                onClick={() => setSelectedFiles([])}
                className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800 transition-colors"
              >
                Bỏ chọn
              </button>
              <button
                onClick={handleBulkDelete}
                disabled={isLoading}
                className="flex items-center gap-1 px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors disabled:opacity-50"
              >
                <Trash2 className="w-3 h-3" />
                Xóa
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Files List */}
      <div className="p-6">
        {filteredFiles.length === 0 ? (
          <div className="text-center py-12">
            <File className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || selectedCategory !== 'all' ? 'Không tìm thấy files' : 'Chưa có files nào'}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedCategory !== 'all' 
                ? 'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm'
                : 'Upload files đầu tiên để bắt đầu'
              }
            </p>
            {!searchTerm && selectedCategory === 'all' && (
              <button
                onClick={onUploadClick}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Upload Files
              </button>
            )}
          </div>
        ) : (
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4' : 'space-y-2'}>
            {filteredFiles.map((file) => {
              const IconComponent = getFileIcon(file.type);
              const isSelected = selectedFiles.includes(file.id);

              if (viewMode === 'grid') {
                return (
                  <div
                    key={file.id}
                    className={`border rounded-lg p-4 hover:shadow-md transition-all cursor-pointer ${
                      isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                    }`}
                    onClick={() => toggleFileSelection(file.id)}
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                          <IconComponent className="w-5 h-5 text-gray-600" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 truncate text-sm">
                          {file.name}
                        </h4>
                        <p className="text-xs text-gray-600">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{formatDate(file.uploadDate)}</span>
                      <div className="flex gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle preview
                          }}
                          className="p-1 hover:bg-gray-200 rounded"
                        >
                          <Eye className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSingleDelete(file.id);
                          }}
                          className="p-1 hover:bg-red-100 hover:text-red-600 rounded"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              } else {
                return (
                  <div
                    key={file.id}
                    className={`flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer ${
                      isSelected ? 'bg-blue-50 border border-blue-200' : ''
                    }`}
                    onClick={() => toggleFileSelection(file.id)}
                  >
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                        <IconComponent className="w-4 h-4 text-gray-600" />
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 truncate">
                        {file.name}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {file.category} • {formatFileSize(file.size)}
                      </p>
                    </div>
                    
                    <div className="text-sm text-gray-500">
                      {formatDate(file.uploadDate)}
                    </div>
                    
                    <div className="flex gap-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle preview
                        }}
                        className="p-2 hover:bg-gray-200 rounded"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleSingleDelete(file.id);
                        }}
                        className="p-2 hover:bg-red-100 hover:text-red-600 rounded"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                );
              }
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default FileManager;
