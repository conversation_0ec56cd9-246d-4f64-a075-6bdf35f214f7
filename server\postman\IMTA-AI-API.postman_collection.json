{"info": {"name": "IMTA AI API", "description": "API collection for IMTA AI chatbot with authentication, payment, and chat features", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:5001/api", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "chatId", "value": "", "type": "string"}, {"key": "paymentId", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Authentication", "item": [{"name": "Register User (Vietnamese)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('authToken', response.data.token);", "    pm.collectionVariables.set('userId', response.data.user.id);", "    console.log('User registered and token saved');", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "vi"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"fullName\": \"Test User\",\n  \"phone\": \"**********\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}}, {"name": "Register User (English)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('authToken', response.data.token);", "    pm.collectionVariables.set('userId', response.data.user.id);", "    console.log('User registered and token saved');", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "en"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser_en\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"fullName\": \"Test User EN\",\n  \"phone\": \"0987654321\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('authToken', response.data.token);", "    pm.collectionVariables.set('userId', response.data.user.id);", "    console.log('User logged in and token saved');", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "vi"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "vi"}], "url": {"raw": "{{baseUrl}}/auth/me", "host": ["{{baseUrl}}"], "path": ["auth", "me"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "vi"}], "body": {"mode": "raw", "raw": "{\n  \"fullName\": \"Updated Test User\",\n  \"phone\": \"**********\",\n  \"preferences\": {\n    \"language\": \"en\",\n    \"theme\": \"dark\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/auth/me", "host": ["{{baseUrl}}"], "path": ["auth", "me"]}}}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "vi"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"password123\",\n  \"newPassword\": \"newpassword123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/change-password", "host": ["{{baseUrl}}"], "path": ["auth", "change-password"]}}}]}, {"name": "Payment", "item": [{"name": "<PERSON><PERSON><PERSON><PERSON> (Vietnamese)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('paymentId', response.data.payment.id);", "    console.log('Payment created:', response.data.payment.transactionId);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "vi"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100000,\n  \"paymentMethod\": \"bank_transfer\",\n  \"description\": \"Nạp tiền test\"\n}"}, "url": {"raw": "{{baseUrl}}/payment/create-deposit", "host": ["{{baseUrl}}"], "path": ["payment", "create-deposit"]}}}, {"name": "<PERSON><PERSON><PERSON><PERSON> (English)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "en"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 50000,\n  \"paymentMethod\": \"e_wallet\",\n  \"description\": \"Test deposit\"\n}"}, "url": {"raw": "{{baseUrl}}/payment/create-deposit", "host": ["{{baseUrl}}"], "path": ["payment", "create-deposit"]}}}, {"name": "Get Payment History", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "vi"}], "url": {"raw": "{{baseUrl}}/payment/history?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["payment", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Payment Details", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "vi"}], "url": {"raw": "{{baseUrl}}/payment/{{paymentId}}", "host": ["{{baseUrl}}"], "path": ["payment", "{{paymentId}}"]}}}]}, {"name": "Cha<PERSON>", "item": [{"name": "Send Message (Create New Chat)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('chatId', response.data.chatId);", "    console.log('Chat created:', response.data.chatId);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "vi"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"<PERSON><PERSON> chào, tôi cần hỗ trợ về khóa học\"\n}"}, "url": {"raw": "{{baseUrl}}/chat", "host": ["{{baseUrl}}"], "path": ["chat"]}}}, {"name": "Send Message (Existing Chat)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "vi"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"T<PERSON><PERSON> muốn hỏi thêm về khóa học này\",\n  \"chatId\": \"{{chatId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/chat", "host": ["{{baseUrl}}"], "path": ["chat"]}}}, {"name": "Get Chat History", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "vi"}], "url": {"raw": "{{baseUrl}}/chat/history?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["chat", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Chat Details", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "vi"}], "url": {"raw": "{{baseUrl}}/chat/{{chatId}}?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["chat", "{{chatId}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}]}}}, {"name": "Create New Chat", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "vi"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Hỗ trợ về khóa học AI\",\n  \"type\": \"course_inquiry\",\n  \"metadata\": {\n    \"courseId\": \"ai_course_001\",\n    \"priority\": \"high\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/chat/create", "host": ["{{baseUrl}}"], "path": ["chat", "create"]}}}, {"name": "Send Message to Specific Chat", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "vi"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"Tôi muốn đăng ký khóa học này\"\n}"}, "url": {"raw": "{{baseUrl}}/chat/{{chatId}}/messages", "host": ["{{baseUrl}}"], "path": ["chat", "{{chatId}}", "messages"]}}}, {"name": "Update Chat", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "vi"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Hỗ trợ về khóa học AI - <PERSON><PERSON> cập nhật\",\n  \"status\": \"completed\"\n}"}, "url": {"raw": "{{baseUrl}}/chat/{{chatId}}", "host": ["{{baseUrl}}"], "path": ["chat", "{{chatId}}"]}}}, {"name": "Delete Chat", "request": {"method": "DELETE", "header": [{"key": "Accept-Language", "value": "vi"}], "url": {"raw": "{{baseUrl}}/chat/{{chatId}}", "host": ["{{baseUrl}}"], "path": ["chat", "{{chatId}}"]}}}]}, {"name": "Users", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "vi"}], "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}}]}, {"name": "Language Testing", "item": [{"name": "Test Vietnamese", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "vi"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Test English", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept-Language", "value": "en"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Test Query Parameter", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/chat/history?lang=en", "host": ["{{baseUrl}}"], "path": ["chat", "history"], "query": [{"key": "lang", "value": "en"}]}}}]}]}