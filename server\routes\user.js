const express = require('express');
const router = express.Router();
const { User } = require('../models');
const { sendLocalizedSuccess, sendLocalizedError } = require('../utils/i18n');

// GET /api/users - <PERSON><PERSON><PERSON> s<PERSON>ch user
router.get('/', async (req, res) => {
  try {
    const users = await User.find().select('-__v -password');
    sendLocalizedSuccess(res, 200, null, { users });
  } catch (error) {
    console.error('Error fetching users:', error);
    sendLocalizedError(res, 500, 'user.list.get_failed');
  }
});

module.exports = router; 