const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  transactionId: {
    type: String,
    required: true,
    unique: true
  },
  amount: {
    type: Number,
    required: true,
    min: 1000 // Minimum 1000 VND
  },
  currency: {
    type: String,
    default: 'VND'
  },
  type: {
    type: String,
    enum: ['deposit', 'withdrawal', 'refund'],
    default: 'deposit'
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['bank_transfer', 'credit_card', 'e_wallet', 'qr_code'],
    required: true
  },
  paymentGateway: {
    type: String,
    default: 'sepay.vn'
  },
  gatewayTransactionId: {
    type: String,
    default: null
  },
  gatewayResponse: {
    type: mongoose.Schema.Types.Mixed,
    default: null
  },
  description: {
    type: String,
    default: 'Nạp tiền vào tài khoản'
  },
  metadata: {
    ipAddress: String,
    userAgent: String,
    referrer: String
  },
  completedAt: {
    type: Date,
    default: null
  },
  failedAt: {
    type: Date,
    default: null
  },
  failureReason: {
    type: String,
    default: null
  }
}, {
  timestamps: true
});

// Indexes
paymentSchema.index({ userId: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ createdAt: -1 });
paymentSchema.index({ gatewayTransactionId: 1 });

// Static methods
paymentSchema.statics.generateTransactionId = function() {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substr(2, 9);
  return `TXN_${timestamp}_${random}`.toUpperCase();
};

paymentSchema.statics.findByUserId = function(userId) {
  return this.find({ userId }).sort({ createdAt: -1 });
};

paymentSchema.statics.findPendingPayments = function() {
  return this.find({ status: 'pending' });
};

// Instance methods
paymentSchema.methods.markAsCompleted = function(gatewayResponse = null) {
  this.status = 'completed';
  this.completedAt = new Date();
  if (gatewayResponse) {
    this.gatewayResponse = gatewayResponse;
  }
  return this.save();
};

paymentSchema.methods.markAsFailed = function(reason) {
  this.status = 'failed';
  this.failedAt = new Date();
  this.failureReason = reason;
  return this.save();
};

paymentSchema.methods.markAsCancelled = function() {
  this.status = 'cancelled';
  return this.save();
};

// Pre-save middleware
paymentSchema.pre('save', function(next) {
  if (this.isNew && !this.transactionId) {
    this.transactionId = this.constructor.generateTransactionId();
  }
  next();
});

module.exports = mongoose.model('Payment', paymentSchema); 