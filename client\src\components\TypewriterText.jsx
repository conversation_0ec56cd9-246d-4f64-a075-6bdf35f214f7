import React, { useState, useEffect } from 'react';
import useTypewriter from '../hooks/useTypewriter';

const TypewriterText = ({ 
  text, 
  speed = 50, 
  delay = 0,
  showCursor = true,
  cursorChar = '|',
  onComplete,
  onStart,
  className = '',
  skipAnimation = false,
  enableSkip = true
}) => {
  const [userSkipped, setUserSkipped] = useState(false);
  
  const {
    displayText,
    isTyping,
    isComplete,
    skip,
    progress
  } = useTypewriter({
    text,
    speed,
    delay,
    onComplete: () => {
      onComplete?.();
    },
    onStart,
    skipAnimation: skipAnimation || userSkipped
  });

  // Handle user click to skip animation
  const handleSkip = () => {
    if (enableSkip && !isComplete && !userSkipped) {
      setUserSkipped(true);
      skip();
    }
  };

  // Reset userSkipped when text changes
  useEffect(() => {
    setUserSkipped(false);
  }, [text]);

  return (
    <div 
      className={`relative ${className}`}
      onClick={handleSkip}
      style={{ cursor: enableSkip && !isComplete ? 'pointer' : 'default' }}
      title={enableSkip && !isComplete ? 'Click để bỏ qua hiệu ứng' : ''}
    >
      {/* Main text */}
      <span className="whitespace-pre-wrap break-words">
        {displayText}
      </span>
      
      {/* Blinking cursor */}
      {showCursor && isTyping && (
        <span className="animate-pulse text-gray-600 ml-1">
          {cursorChar}
        </span>
      )}
      
      {/* Skip hint (only show on hover and when typing) */}
      {enableSkip && isTyping && !userSkipped && (
        <div className="absolute -top-8 left-0 bg-black text-white text-xs px-2 py-1 rounded opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
          Click để bỏ qua
        </div>
      )}
    </div>
  );
};

export default TypewriterText;
