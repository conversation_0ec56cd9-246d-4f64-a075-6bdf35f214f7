<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Streaming Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-container {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .streaming-cursor {
            animation: blink 1s infinite;
            color: #3b82f6;
            font-weight: bold;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        .message {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Chat Streaming Animation Test</h1>
    
    <div class="test-container">
        <h3>Test 1: Basic Streaming Animation</h3>
        <p>This test simulates how the streaming text would appear character by character:</p>
        <div id="test1-output" class="message"></div>
        <button onclick="startTest1()">Start Test 1</button>
        <button onclick="resetTest1()">Reset</button>
    </div>
    
    <div class="test-container">
        <h3>Test 2: Incremental Chunks (Like API)</h3>
        <p>This test simulates receiving chunks of text from the API:</p>
        <div id="test2-output" class="message"></div>
        <button onclick="startTest2()">Start Test 2</button>
        <button onclick="resetTest2()">Reset</button>
    </div>
    
    <div class="test-container">
        <h3>Expected Behavior</h3>
        <ul>
            <li>Text should appear character by character at a readable speed</li>
            <li>A blinking cursor should appear during streaming</li>
            <li>The cursor should disappear when streaming is complete</li>
            <li>Previous messages should NOT re-animate when new ones start</li>
            <li>Only the currently streaming message should have the animation</li>
        </ul>
    </div>

    <script>
        // Simulate the StreamingText logic
        class StreamingSimulator {
            constructor(element, options = {}) {
                this.element = element;
                this.speed = options.speed || 80; // chars per second
                this.showCursor = options.showCursor !== false;
                this.cursorChar = options.cursorChar || '▋';
                
                this.targetText = '';
                this.displayedLength = 0;
                this.isStreaming = true;
                this.animationId = null;
                this.isComplete = false;
            }
            
            updateText(newText) {
                this.targetText = newText;
                if (!this.animationId && !this.isComplete) {
                    this.startAnimation();
                }
            }
            
            startAnimation() {
                const animate = () => {
                    if (this.displayedLength < this.targetText.length) {
                        this.displayedLength++;
                        this.render();
                        this.animationId = setTimeout(animate, 1000 / this.speed);
                    } else if (this.isStreaming) {
                        // Wait for more text
                        this.animationId = setTimeout(animate, 100);
                    } else {
                        // Complete
                        this.complete();
                    }
                };
                animate();
            }
            
            complete() {
                this.isComplete = true;
                this.isStreaming = false;
                this.render();
                if (this.animationId) {
                    clearTimeout(this.animationId);
                    this.animationId = null;
                }
            }
            
            render() {
                const displayText = this.targetText.slice(0, this.displayedLength);
                const cursor = this.showCursor && !this.isComplete ? `<span class="streaming-cursor">${this.cursorChar}</span>` : '';
                this.element.innerHTML = displayText + cursor;
            }
            
            reset() {
                this.targetText = '';
                this.displayedLength = 0;
                this.isStreaming = true;
                this.isComplete = false;
                if (this.animationId) {
                    clearTimeout(this.animationId);
                    this.animationId = null;
                }
                this.element.innerHTML = '';
            }
        }
        
        let test1Simulator, test2Simulator;
        
        function startTest1() {
            const element = document.getElementById('test1-output');
            test1Simulator = new StreamingSimulator(element);
            
            const fullText = "Xin chào! Tôi là AI assistant của bạn. Tôi có thể giúp bạn trả lời các câu hỏi, hỗ trợ học tập, và thực hiện nhiều tác vụ khác. Hãy cho tôi biết bạn cần hỗ trợ gì nhé!";
            
            // Simulate receiving the full text at once
            test1Simulator.updateText(fullText);
            
            // Complete after 3 seconds
            setTimeout(() => {
                test1Simulator.isStreaming = false;
            }, 3000);
        }
        
        function resetTest1() {
            if (test1Simulator) {
                test1Simulator.reset();
            }
        }
        
        function startTest2() {
            const element = document.getElementById('test2-output');
            test2Simulator = new StreamingSimulator(element);
            
            const chunks = [
                "Đây là",
                " một ví dụ",
                " về cách",
                " API streaming",
                " hoạt động.",
                " Mỗi chunk",
                " được gửi",
                " từ server",
                " và hiển thị",
                " ngay lập tức",
                " trong UI.",
                " Kết quả là",
                " trải nghiệm",
                " tương tác",
                " mượt mà",
                " như ChatGPT!"
            ];
            
            let currentText = '';
            let chunkIndex = 0;
            
            const sendChunk = () => {
                if (chunkIndex < chunks.length) {
                    currentText += chunks[chunkIndex];
                    test2Simulator.updateText(currentText);
                    chunkIndex++;
                    setTimeout(sendChunk, 300); // Send chunk every 300ms
                } else {
                    // Complete streaming
                    test2Simulator.isStreaming = false;
                }
            };
            
            sendChunk();
        }
        
        function resetTest2() {
            if (test2Simulator) {
                test2Simulator.reset();
            }
        }
    </script>
</body>
</html>
