import React, { useState, useEffect, useRef } from 'react';

// Debug mode - set to false to disable console logs
const DEBUG_MODE = false;
const debugLog = (...args) => {
  if (DEBUG_MODE) {
    console.log(...args);
  }
};

const StreamingText = ({
  text,
  duration, // Optional: override automatic duration calculation
  delay = 0,
  showCursor = true,
  onComplete,
  onStart,
  className = '',
  skipAnimation = false,
  enableSkip = true,
  forceComplete = false,
  isStreaming: parentIsStreaming = true
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  const [userSkipped, setUserSkipped] = useState(false);

  const animationRef = useRef(null);
  const targetTextRef = useRef('');
  const startTimeRef = useRef(null);

  // Calculate dynamic duration based on text length for consistent speed
  const calculateDuration = (textLength) => {
    if (duration) return duration;
    const baseSpeed = 20; // characters per second
    return Math.max(textLength * (1000 / baseSpeed), 500); // minimum 500ms
  };

  // Skip animation handler
  const handleSkip = () => {
    if (enableSkip && !userSkipped) {
      debugLog('⏭️ User skipped animation');
      setUserSkipped(true);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
      setDisplayText(targetTextRef.current);
      setIsComplete(true);
      onComplete?.();
    }
  };

  // Main effect to handle text changes and streaming state
  useEffect(() => {
    debugLog('🔍 StreamingText Effect - Props:', { 
      text: text?.substring(0, 100), 
      textLength: text?.length,
      parentIsStreaming,
      isComplete,
      userSkipped
    });
    
    const currentText = text || '';
    const wasEmpty = !targetTextRef.current;
    
    // Update target text
    targetTextRef.current = currentText;
    
    // Reset if text got shorter (new message)
    if (currentText.length < displayText.length) {
      debugLog('📏 Text shorter, resetting display');
      setDisplayText('');
      setIsComplete(false);
      setUserSkipped(false);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
    }
    
    // Start animation if we have text and conditions are met
    if (currentText && !userSkipped && !isComplete) {
      debugLog('🎯 Starting requestAnimationFrame animation');
      if (wasEmpty) {
        onStart?.();
      }
      startAnimation(currentText);
    }
    
    // If streaming ended and we're caught up, mark as complete
    if (!parentIsStreaming && displayText.length >= currentText.length && currentText && !isComplete) {
      debugLog('✅ Marking as complete - streaming ended and caught up');
      setIsComplete(true);
      onComplete?.();
    }
    
  }, [text, parentIsStreaming, userSkipped, isComplete, displayText, onStart, onComplete]);

  const startAnimation = (targetText) => {
    if (!targetText || userSkipped) return;
    
    // Cancel any existing animation
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    
    const textLength = targetText.length;
    const animationDuration = calculateDuration(textLength);
    startTimeRef.current = performance.now();
    
    debugLog('🎬 Starting animation with requestAnimationFrame:', {
      textLength,
      animationDuration,
      targetText: targetText.substring(0, 50) + '...'
    });
    
    const animate = (currentTime) => {
      if (userSkipped) {
        debugLog('⏭️ Animation stopped - user skipped');
        return;
      }
      
      const elapsed = currentTime - startTimeRef.current;
      const progress = Math.min(elapsed / animationDuration, 1);
      const currentLength = Math.floor(textLength * progress);
      
      const newDisplayText = targetText.substring(0, currentLength);
      
      debugLog('🎞️ Animation frame:', {
        elapsed,
        progress: (progress * 100).toFixed(1) + '%',
        currentLength,
        textLength,
        newChar: newDisplayText.slice(-1)
      });
      
      setDisplayText(newDisplayText);
      
      if (progress < 1 && targetTextRef.current === targetText) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        debugLog('🏁 Animation complete or text changed');
        // Animation complete or text changed
        if (!parentIsStreaming && targetTextRef.current === targetText) {
          setIsComplete(true);
          onComplete?.();
        }
      }
    };
    
    animationRef.current = requestAnimationFrame(animate);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // Handle force complete
  useEffect(() => {
    if (forceComplete && !isComplete) {
      debugLog('🏃 Force completing animation');
      setUserSkipped(true);
      handleSkip();
    }
  }, [forceComplete]);

  // Skip animation if disabled
  if (skipAnimation) {
    return (
      <span className={className}>
        {text}
      </span>
    );
  }

  const streamingClass = isComplete ? 'streaming-text completed' : 'streaming-text';
  const combinedClassName = `${streamingClass} ${className}`.trim();

  return (
    <span 
      className={combinedClassName}
      onClick={handleSkip}
      style={{ cursor: enableSkip ? 'pointer' : 'default' }}
      title={enableSkip ? 'Click to skip animation' : ''}
    >
      {displayText}
    </span>
  );
};

export default StreamingText;
