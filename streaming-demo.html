<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RequestAnimationFrame Streaming Text Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .demo-text {
            font-size: 18px;
            line-height: 1.6;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            position: relative;
        }
        
        .streaming-text {
            position: relative;
            display: inline;
        }
        
        .streaming-text::after {
            content: '|';
            animation: blink 1s infinite;
            color: #007bff;
            font-weight: bold;
            margin-left: 1px;
        }
        
        .streaming-text.completed::after {
            display: none;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        
        button {
            padding: 12px 24px;
            margin: 0 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.2s;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .speed-controls {
            margin: 15px 0;
        }
        
        .speed-controls label {
            margin: 0 10px;
            font-weight: bold;
        }
        
        .speed-controls input {
            margin: 0 5px;
        }
        
        .demo-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="demo-title">🚀 RequestAnimationFrame Streaming Text Demo</h1>
        
        <div class="controls">
            <button onclick="startDemo()">Start Demo</button>
            <button onclick="resetDemo()">Reset</button>
            <button onclick="skipAnimation()" id="skipBtn" disabled>Skip Animation</button>
        </div>
        
        <div class="speed-controls">
            <label>Speed:</label>
            <input type="radio" name="speed" value="1000" id="slow"> <label for="slow">Slow (1s)</label>
            <input type="radio" name="speed" value="2000" id="medium" checked> <label for="medium">Medium (2s)</label>
            <input type="radio" name="speed" value="3000" id="fast"> <label for="fast">Fast (3s)</label>
            <input type="radio" name="speed" value="auto" id="auto"> <label for="auto">Auto Speed</label>
        </div>
        
        <div class="demo-text">
            <span id="streamingText" class="streaming-text"></span>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 6px;">
            <strong>✨ Features:</strong>
            <ul>
                <li>Uses <code>requestAnimationFrame</code> for smooth 60fps animation</li>
                <li>CSS-based cursor blinking with <code>::after</code> pseudo-element</li>
                <li>Dynamic duration calculation based on text length</li>
                <li>Click to skip animation</li>
                <li>Automatically hides cursor when animation completes</li>
            </ul>
        </div>
    </div>
    
    <script>
        let currentAnimation = null;
        let isAnimating = false;
        
        const sampleTexts = [
            "Xin chào! Đây là demo streaming text sử dụng requestAnimationFrame. Hiệu ứng này mượt mà và tự nhiên hơn nhiều so với setTimeout.",
            "RequestAnimationFrame cho phép chúng ta tạo ra hiệu ứng animation mượt mà ở 60fps, đồng bộ với refresh rate của màn hình.",
            "Cursor được tạo bằng CSS ::after pseudo-element với animation blink, và sẽ tự động biến mất khi animation hoàn thành.",
            "Bạn có thể click vào text để skip animation, hoặc thay đổi tốc độ bằng các radio buttons ở trên.",
            "Điều tuyệt vời là hiệu ứng này rất nhẹ và không gây lag cho browser, perfect cho chat streaming!"
        ];
        
        function getSelectedDuration() {
            const selected = document.querySelector('input[name="speed"]:checked').value;
            return selected === 'auto' ? null : parseInt(selected);
        }
          function calculateDuration(textLength) {
            const baseSpeed = 8; // characters per second (much slower)
            return Math.max(textLength * (1000 / baseSpeed), 1000);
        }
        
        function animateText(element, text, duration = null) {
            if (currentAnimation) {
                cancelAnimationFrame(currentAnimation);
            }
            
            const textLength = text.length;
            const animationDuration = duration || calculateDuration(textLength);
            const startTime = performance.now();
            
            console.log('🎬 Starting animation:', { textLength, animationDuration });
            
            element.className = 'streaming-text';
            element.textContent = '';
            isAnimating = true;
            document.getElementById('skipBtn').disabled = false;
            
            const animate = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / animationDuration, 1);
                const currentLength = Math.floor(textLength * progress);
                
                element.textContent = text.substring(0, currentLength);
                
                if (progress < 1 && isAnimating) {
                    currentAnimation = requestAnimationFrame(animate);
                } else {
                    // Animation complete
                    element.className = 'streaming-text completed';
                    isAnimating = false;
                    document.getElementById('skipBtn').disabled = true;
                    console.log('✅ Animation completed');
                }
            };
            
            currentAnimation = requestAnimationFrame(animate);
        }
        
        function startDemo() {
            const textElement = document.getElementById('streamingText');
            const randomText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
            const duration = getSelectedDuration();
            
            animateText(textElement, randomText, duration);
        }
        
        function resetDemo() {
            if (currentAnimation) {
                cancelAnimationFrame(currentAnimation);
            }
            
            const textElement = document.getElementById('streamingText');
            textElement.textContent = 'Click "Start Demo" to begin...';
            textElement.className = 'streaming-text completed';
            isAnimating = false;
            document.getElementById('skipBtn').disabled = true;
        }
        
        function skipAnimation() {
            if (isAnimating && currentAnimation) {
                cancelAnimationFrame(currentAnimation);
                
                const textElement = document.getElementById('streamingText');
                textElement.className = 'streaming-text completed';
                isAnimating = false;
                document.getElementById('skipBtn').disabled = true;
                console.log('⏭️ Animation skipped');
            }
        }
        
        // Click to skip functionality
        document.getElementById('streamingText').addEventListener('click', skipAnimation);
        
        // Initialize
        resetDemo();
    </script>
</body>
</html>
