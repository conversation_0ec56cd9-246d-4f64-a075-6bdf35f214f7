# 🚀 Hướng dẫn sử dụng REST Client Extension

## 📋 Mục lục
- [<PERSON>à<PERSON> đặt](#cài-đặt)
- [Cách sử dụng cơ bản](#cách-sử-dụng-c<PERSON>-bản)
- [Variables và Environment](#variables-và-environment)
- [File test-api.http](#file-test-apihttp)
- [Tips và Tricks](#tips-và-tricks)
- [Troubleshooting](#troubleshooting)

---

## 🛠️ Cài đặt

### 1. Cài đặt REST Client Extension
1. **Mở VS Code**
2. **Extensions**: `Ctrl+Shift+X` (Windows/Linux) hoặc `Cmd+Shift+X` (Mac)
3. **Tìm kiếm**: "REST Client"
4. **Cài đặt**: Extension của Huachao Mao
5. **Restart VS Code** nếu cần

### 2. Kiểm tra cài đặt
- Mở file `.http` bất kỳ
- Bạn sẽ thấy "Send Request" phía trên mỗi request

---

## 🎯 Cách sử dụng cơ bản

### 1. Tạo file .http
```http
### Tên request (tùy chọn)
METHOD URL
Headers: value
Content-Type: application/json

{
  "key": "value"
}
```

### 2. Chạy request
- **Click**: "Send Request" phía trên mỗi request
- **Keyboard**: `Ctrl+Alt+R` (Windows/Linux) hoặc `Cmd+Alt+R` (Mac)
- **Response**: Hiển thị trong tab mới

### 3. Ví dụ cơ bản
```http
### Health Check
GET http://localhost:5001/api/health
Accept-Language: vi

###

### Login
POST http://localhost:5001/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

---

## 🔧 Variables và Environment

### 1. Variables cơ bản
```http
@baseUrl = http://localhost:5001/api
@email = <EMAIL>
@password = password123

### Sử dụng variables
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "{{email}}",
  "password": "{{password}}"
}
```

### 2. Response Variables
```http
### Login và lưu response
# @name login
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "{{email}}",
  "password": "{{password}}"
}

### Sử dụng token từ response trước
GET {{baseUrl}}/auth/me
Authorization: Bearer {{login.response.body.data.token}}
```

### 3. Environment Switching
```http
@dev = http://localhost:5001/api
@prod = https://api.example.com/api

### Chọn environment
# @baseUrl = {{dev}}
@baseUrl = {{prod}}

### Test request
GET {{baseUrl}}/health
```

---

## 📁 File test-api.http

### Cấu trúc file
File `server/test-api.http` đã được chuẩn bị sẵn với:

1. **Health Check**
   - Kiểm tra server có hoạt động không
   - Test với tiếng Việt và tiếng Anh

2. **Authentication**
   - Register (đăng ký)
   - Login (đăng nhập)
   - Profile (thông tin tài khoản)
   - Update profile (cập nhật thông tin)
   - Change password (đổi mật khẩu)

3. **Payment**
   - Create deposit (tạo lệnh nạp tiền)
   - Payment history (lịch sử thanh toán)
   - Payment details (chi tiết thanh toán)

4. **Chat**
   - Send message (gửi tin nhắn)
   - Chat history (lịch sử chat)
   - Chat details (chi tiết chat)

### Cách sử dụng
1. **Mở file**: `server/test-api.http`
2. **Chạy từng request**: Click "Send Request"
3. **Thay đổi dữ liệu**: Sửa email, password, amount
4. **Lưu token**: Copy token từ response login

---

## 💡 Tips và Tricks

### 1. Debugging Validation Errors
```http
### Test với dữ liệu không hợp lệ
POST {{baseUrl}}/auth/register
Content-Type: application/json
Accept-Language: vi

{
  "username": "ab",
  "email": "invalid-email",
  "password": "123",
  "fullName": "Test User"
}
```

### 2. Test Internationalization
```http
### Tiếng Việt
GET {{baseUrl}}/chat/history
Accept-Language: vi
Authorization: Bearer {{token}}

###

### English
GET {{baseUrl}}/chat/history
Accept-Language: en
Authorization: Bearer {{token}}
```

### 3. Query Parameters
```http
### Pagination
GET {{baseUrl}}/chat/history?page=1&limit=10
Authorization: Bearer {{token}}

###

### Language via query
GET {{baseUrl}}/chat/history?lang=en
Authorization: Bearer {{token}}
```

### 4. Custom Headers
```http
### Custom language header
GET {{baseUrl}}/chat/history
X-Language: en
Authorization: Bearer {{token}}
```

---

## 🔍 Troubleshooting

### Lỗi thường gặp

#### 1. Connection Refused
```
Error: connect ECONNREFUSED 127.0.0.1:5001
```
**Giải pháp:**
- Kiểm tra server có đang chạy không: `npm run dev`
- Kiểm tra port 5001 có đúng không
- Kiểm tra firewall

#### 2. 401 Unauthorized
```
Response: {"success":false,"message":"Token không hợp lệ"}
```
**Giải pháp:**
- Chạy lại request login để lấy token mới
- Kiểm tra token có đúng format không
- Kiểm tra token có hết hạn không

#### 3. 400 Bad Request
```
Response: {"success":false,"message":"Dữ liệu đăng ký không hợp lệ"}
```
**Giải pháp:**
- Kiểm tra dữ liệu gửi lên có đúng format JSON không
- Kiểm tra các trường bắt buộc có đầy đủ không
- Xem response.errors để biết lỗi cụ thể

#### 4. 500 Internal Server Error
```
Response: {"success":false,"message":"Đã xảy ra lỗi nội bộ"}
```
**Giải pháp:**
- Kiểm tra console logs của server
- Kiểm tra database connection
- Kiểm tra environment variables

### Debug Steps
1. **Health Check**: Chạy `GET {{baseUrl}}/health` trước
2. **Login**: Test login để lấy token mới
3. **Headers**: Kiểm tra headers có đúng không
4. **Body**: Kiểm tra request body có đúng format không
5. **Logs**: Xem console logs của server

---

## 📚 Tham khảo

### REST Client Extension
- **Extension ID**: `humao.rest-client`
- **Author**: Huachao Mao
- **Documentation**: [GitHub](https://github.com/Huachao/vscode-restclient)

### API Documentation
- **File**: `server/API_DOCUMENTATION.md`
- **Base URL**: `http://localhost:5001/api`
- **Health Check**: `GET /health`

### Environment Variables
- **JWT_SECRET**: Secret key cho JWT
- **MONGODB_URI**: MongoDB connection string
- **LANGFLOW_API_URL**: Langflow API URL
- **LANGFLOW_API_KEY**: Langflow API key

---

## 🎉 Kết luận

REST Client Extension là công cụ mạnh mẽ để test API một cách nhanh chóng và hiệu quả. Với file `test-api.http` đã được chuẩn bị sẵn, bạn có thể:

- ✅ Test tất cả endpoints một cách dễ dàng
- ✅ Debug validation errors chi tiết
- ✅ Test internationalization (i18n)
- ✅ Sử dụng variables để tái sử dụng
- ✅ Chuyển đổi giữa các environments

Hãy bắt đầu với health check và dần dần test các endpoints khác! 