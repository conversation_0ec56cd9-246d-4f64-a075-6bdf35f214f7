# 🎯 ĐẶC TẢ DỰ ÁN CHATBOT

## 📋 THÔNG TIN DỰ ÁN
- **Tên dự án**: Imta AI
- **Mô tả ngắn**: chatbot agent hỗ trợ học viên: chă<PERSON> sóc hỏi về imta (kh<PERSON><PERSON> học, ch<PERSON><PERSON> sách...), phân tích file chạy ads, viết bài quảng cáo, phân tích avatar khách hàng
- **Ngày bắt đầu**: 12/6/2025
- **Người phát triển**: Bình Vũ

## 🎯 MỤC TIÊU DỰ ÁN
### Mục tiêu chính
- [ ] Tạo chatbot cho học viên sử dụng
- [ ] Quản lý credit 
- [ ] Chất lượng phản hồi tốt nhất

## 🏗️ KIẾN TRÚC HỆ THỐNG
### Frontend (React)
- **Framework**: React với JavaScript
- **UI/UX**: t<PERSON><PERSON><PERSON> lợ<PERSON>, <PERSON><PERSON> dù<PERSON>, chuy<PERSON><PERSON> nghiệp và hiện đại
- **Responsive**: có hỗ trợ mobile tốt

### Backend (Express)
- **Framework**: Express.js
- **Database**: MongoDB
- **API**: RESTful API
- **Authentication**: Có đăng nhập

### AI Integration
- **Langflow**: langflow.mecode.pro
- **Model**: tùy chỉnh
- **Features**: trả lời một số tình huống được training

## 📊 DATABASE DESIGN
### Collections
- **users**: [Mô tả thông tin user]
- **chats**: [Mô tả phiên chat]
- **messages**: [Mô tả tin nhắn]

### Relationships
- [Mô tả mối quan hệ giữa các collection]

## 🚀 TÍNH NĂNG CHÍNH
### Core Features
- [ ] Chat với AI
- [ ] Lưu lịch sử chat
- [ ] Hiển thị được chart
- [ ] Hiển thị được hình ảnh

## 🎨 UI/UX REQUIREMENTS
### Design Style
- **Theme**: có được thì tốt
- **Colors**: 
- **Typography**: hiện đại

## 🔧 TECHNICAL REQUIREMENTS
### Performance
- **Response Time**: nhanh chóng
- **Concurrent Users**: 20 - 50 người

### Security

### Scalability


## 📱 DEPLOYMENT
### Environment
- **Development**: [Cấu hình dev]
- **Production**: [Cấu hình production]

### Hosting
- **Frontend**: [Nơi host frontend]
- **Backend**: [Nơi host backend]
- **Database**: [Nơi host database]

### Code Quality
- **Linting**: [ESLint config]
- **Formatting**: [Prettier config]

## 📚 DOCUMENTATION
### Required Docs
- [ ] API Documentation
- [ ] User Manual
- [ ] Developer Guide

## 🎯 SUCCESS CRITERIA
### MVP (Minimum Viable Product)
- chat được cơ bản, kết nối được langflow

### Full Release
- tất cả tính năng
