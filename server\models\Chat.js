const mongoose = require('mongoose');

const chatSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  type: {
    type: String,
    enum: ['general', 'course_inquiry', 'ads_analysis', 'content_gen', 'avatar_analysis'],
    default: 'general'
  },
  status: {
    type: String,
    enum: ['active', 'completed', 'archived'],
    default: 'active'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  lastMessageAt: {
    type: Date,
    default: Date.now
  },
  metadata: {
    courseId: {
      type: mongoose.Schema.Types.Mixed,
      default: null,
      validate: {
        validator: function(value) {
          if (value === null || value === undefined) return true;
          if (typeof value === 'string') return true;
          if (mongoose.Types.ObjectId.isValid(value)) return true;
          return false;
        },
        message: 'CourseId must be a string or valid ObjectId'
      }
    },
    policyId: {
      type: mongoose.Schema.Types.Mixed,
      default: null,
      validate: {
        validator: function(value) {
          if (value === null || value === undefined) return true;
          if (typeof value === 'string') return true;
          if (mongoose.Types.ObjectId.isValid(value)) return true;
          return false;
        },
        message: 'PolicyId must be a string or valid ObjectId'
      }
    },
    tags: [{
      type: String,
      trim: true
    }],
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    }
  },
  analytics: {
    messageCount: {
      type: Number,
      default: 0,
      min: [0, 'Message count cannot be negative']
    },
    duration: {
      type: Number,
      default: 0,
      min: [0, 'Duration cannot be negative']
    },
    creditUsed: {
      type: Number,
      default: 0,
      min: [0, 'Credit used cannot be negative']
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for chat duration in minutes
chatSchema.virtual('durationMinutes').get(function() {
  if (this.status === 'active') {
    return Math.floor((Date.now() - this.createdAt) / (1000 * 60));
  }
  return this.analytics.duration;
});

// Virtual for is active
chatSchema.virtual('isActive').get(function() {
  return this.status === 'active';
});

// Indexes
// chatSchema.index({ userId: 1 }); // ❌ Dòng này đã comment để tránh Duplicate Index warning
chatSchema.index({ type: 1 });
chatSchema.index({ status: 1 });
chatSchema.index({ createdAt: -1 });
chatSchema.index({ lastMessageAt: -1 });

// Pre-save middleware to update timestamps
chatSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Static methods
chatSchema.statics.findByUserId = function(userId, options = {}) {
  const query = this.find({ userId })
    .populate('userId', 'username email fullName')
    .sort({ lastMessageAt: -1 });

  if (options.limit) {
    query.limit(options.limit);
  }
  if (options.skip) {
    query.skip(options.skip);
  }
  if (options.status) {
    query.where('status', options.status);
  }

  return query;
};

chatSchema.statics.findActiveChats = function() {
  return this.find({ status: 'active' })
    .populate('userId', 'username email')
    .sort({ lastMessageAt: -1 });
};

chatSchema.statics.findByType = function(type, options = {}) {
  const query = this.find({ type })
    .populate('userId', 'username email')
    .sort({ createdAt: -1 });

  if (options.limit) {
    query.limit(options.limit);
  }

  return query;
};

chatSchema.statics.getChatStats = function(userId) {
  return this.aggregate([
    { $match: { userId: mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: null,
        totalChats: { $sum: 1 },
        activeChats: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        totalCreditUsed: { $sum: '$analytics.creditUsed' },
        totalMessages: { $sum: '$analytics.messageCount' },
        avgDuration: { $avg: '$analytics.duration' }
      }
    }
  ]);
};

// Instance methods
chatSchema.methods.updateLastMessage = function() {
  this.lastMessageAt = new Date();
  this.updatedAt = new Date();
  return this.save();
};

chatSchema.methods.incrementMessageCount = function() {
  this.analytics.messageCount += 1;
  this.updateLastMessage();
  return this.save();
};

chatSchema.methods.addCreditUsed = function(amount) {
  if (amount > 0) {
    this.analytics.creditUsed += amount;
    return this.save();
  }
  return Promise.resolve(this);
};

chatSchema.methods.complete = function() {
  this.status = 'completed';
  this.analytics.duration = Math.floor((Date.now() - this.createdAt) / (1000 * 60));
  this.updatedAt = new Date();
  return this.save();
};

chatSchema.methods.archive = function() {
  this.status = 'archived';
  this.updatedAt = new Date();
  return this.save();
};

chatSchema.methods.addTag = function(tag) {
  if (!this.metadata.tags.includes(tag)) {
    this.metadata.tags.push(tag);
    return this.save();
  }
  return Promise.resolve(this);
};

chatSchema.methods.removeTag = function(tag) {
  this.metadata.tags = this.metadata.tags.filter(t => t !== tag);
  return this.save();
};

chatSchema.methods.setPriority = function(priority) {
  if (['low', 'medium', 'high'].includes(priority)) {
    this.metadata.priority = priority;
    return this.save();
  }
  throw new Error('Invalid priority level');
};

module.exports = mongoose.model('Chat', chatSchema); 