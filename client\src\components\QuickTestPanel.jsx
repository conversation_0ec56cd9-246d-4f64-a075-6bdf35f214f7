import React, { useState } from 'react';
import { useChat } from '../context/ChatContext';
import { TestTube, Send } from 'lucide-react';

const QuickTestPanel = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { sendMessage } = useChat();

  const testCases = {
    chart: [
      "Hãy cho tôi xem một chart",
      "Show me a graph", 
      "Tôi muốn xem biểu đồ doanh thu",
      "Hiển thị đồ thị",
      "Cho tôi xem visualization",
      "Báo cáo thống kê",
      "Phân tích dữ liệu"
    ],
    text: [
      "Hello",
      "How are you?",
      "What is your name?",
      "Tell me a joke",
      "Explain AI"
    ]
  };

  const handleTestMessage = async (message) => {
    await sendMessage(message);
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-16 sm:bottom-20 right-2 sm:right-4 bg-green-500 text-white p-2 rounded-full shadow-lg hover:bg-green-600 z-30"
        title="Quick Test Panel"
      >
        <TestTube className="w-4 h-4 sm:w-5 sm:h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-16 sm:bottom-20 right-2 sm:right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-3 sm:p-4 max-w-xs sm:max-w-sm max-h-80 sm:max-h-96 overflow-auto z-30">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-bold text-gray-800 flex items-center gap-2">
          <TestTube className="w-4 h-4" />
          Quick Test
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ×
        </button>
      </div>
      
      <div className="space-y-3">
        <div>
          <h4 className="font-semibold text-green-600 text-sm mb-2">
            📊 Chart Keywords (Should show charts):
          </h4>
          <div className="space-y-1">
            {testCases.chart.map((message, index) => (
              <button
                key={index}
                onClick={() => handleTestMessage(message)}
                className="w-full text-left text-xs bg-green-50 hover:bg-green-100 p-2 rounded border flex items-center justify-between group"
              >
                <span className="truncate">{message}</span>
                <Send className="w-3 h-3 opacity-0 group-hover:opacity-100 text-green-600" />
              </button>
            ))}
          </div>
        </div>

        <div>
          <h4 className="font-semibold text-blue-600 text-sm mb-2">
            💬 Text Keywords (Should show text):
          </h4>
          <div className="space-y-1">
            {testCases.text.map((message, index) => (
              <button
                key={index}
                onClick={() => handleTestMessage(message)}
                className="w-full text-left text-xs bg-blue-50 hover:bg-blue-100 p-2 rounded border flex items-center justify-between group"
              >
                <span className="truncate">{message}</span>
                <Send className="w-3 h-3 opacity-0 group-hover:opacity-100 text-blue-600" />
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickTestPanel;
