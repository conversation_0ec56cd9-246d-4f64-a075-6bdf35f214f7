import React, { useState, useEffect, useRef } from 'react';

// Debug mode - set to false to disable console logs
const DEBUG_MODE = false;
const debugLog = (...args) => {
  if (DEBUG_MODE) {
    console.log(...args);
  }
};

const StreamingText = ({
  text,
  duration = 2000, // Total duration for animation in ms (instead of speed)
  delay = 0,
  showCursor = true,
  onComplete,
  onStart,
  className = '',
  skipAnimation = false,
  enableSkip = true,
  forceComplete = false,
  isStreaming: parentIsStreaming = true
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  const [userSkipped, setUserSkipped] = useState(false);

  // Debug: Log displayText changes
  useEffect(() => {
    debugLog('🎨 displayText changed:', displayText?.substring(0, 100));
  }, [displayText]);

  const animationRef = useRef(null);
  const targetTextRef = useRef('');
  const displayLengthRef = useRef(0);
  const isAnimatingRef = useRef(false);

  // Main effect to handle text changes and streaming state
  useEffect(() => {
    debugLog('🔍 StreamingText Effect - Props:', { 
      text: text?.substring(0, 100), 
      textLength: text?.length,
      parentIsStreaming,
      isComplete,
      userSkipped,
      currentDisplayLength: displayLengthRef.current 
    });
    
    const currentText = text || '';
    const wasEmpty = !targetTextRef.current;
    const prevTargetLength = targetTextRef.current.length;
    
    // Update target text
    targetTextRef.current = currentText;
    
    // Reset if text got shorter (new message)
    if (currentText.length < displayLengthRef.current) {
      debugLog('📏 Text shorter, resetting display');
      displayLengthRef.current = 0;
      setDisplayText('');
      setIsComplete(false);
      setUserSkipped(false);
      isAnimatingRef.current = false;
    }
    
    // SIMPLIFIED LOGIC: Start animation if we have text and conditions are met
    if (currentText && !userSkipped && !isComplete) {
      debugLog('🎯 Checking if should start animation:', {
        hasText: !!currentText,
        isAnimating: isAnimatingRef.current,
        userSkipped,
        isComplete,
        displayLength: displayLengthRef.current,
        targetLength: currentText.length
      });
      
      // Start animation if not already running and we have text to show
      if (!isAnimatingRef.current && displayLengthRef.current < currentText.length) {
        debugLog('▶️ Starting animation - wasEmpty:', wasEmpty);
        if (wasEmpty) {
          onStart?.();
        }
        startAnimation();
      }
    }
    
    // If streaming ended and we still have more text to show, finish quickly
    if (!parentIsStreaming && currentText && displayLengthRef.current < currentText.length && !isComplete) {
      debugLog('⚡ Finishing quickly - streaming ended but more text to show');
      finishQuickly();
    }
    
    // If streaming ended and we're caught up, mark as complete
    if (!parentIsStreaming && displayLengthRef.current >= currentText.length && currentText && !isComplete) {
      debugLog('✅ Marking as complete - streaming ended and caught up');
      setIsComplete(true);
      onComplete?.();
    }
    
  }, [text, parentIsStreaming, userSkipped, isComplete, onStart, onComplete]);

  const startAnimation = () => {
    if (isAnimatingRef.current || userSkipped) {
      debugLog('❌ Cannot start animation - already animating or user skipped');
      return;
    }
    
    debugLog('🎬 Starting animation with target:', targetTextRef.current?.substring(0, 100));
    isAnimatingRef.current = true;
    
    const animate = () => {
      if (userSkipped) {
        debugLog('⏭️ Animation stopped - user skipped');
        isAnimatingRef.current = false;
        return;
      }
      
      const currentTarget = targetTextRef.current;
      const currentDisplayLength = displayLengthRef.current;
      
      debugLog('🎞️ Animation frame:', { 
        currentDisplayLength, 
        targetLength: currentTarget.length,
        parentIsStreaming,
        currentTarget: currentTarget.substring(0, 50) + '...'
      });
      
      if (currentDisplayLength < currentTarget.length) {
        // Show more characters ONE BY ONE
        displayLengthRef.current = currentDisplayLength + 1;
        const newDisplayText = currentTarget.slice(0, displayLengthRef.current);
        debugLog('📝 Updating display text (char by char):', {
          newLength: displayLengthRef.current,
          newChar: newDisplayText.slice(-1),
          newText: newDisplayText.substring(Math.max(0, newDisplayText.length - 20))
        });
        
        // Force synchronous update to ensure immediate rendering
        // Try without flushSync first to see if it's the issue
        setDisplayText(newDisplayText);
        
        // Force a DOM update
        if (typeof window !== 'undefined') {
          // Force reflow to ensure update
          document.body.offsetHeight;
        }
          // Continue animation with proper timing - add extra delay for smoother effect
        const baseInterval = 1000 / speed;
        const extraDelay = Math.random() * 30 + 20; // Random delay 20-50ms for natural typing
        animationRef.current = setTimeout(animate, baseInterval + extraDelay);
      } else {
        // We've caught up with current target text
        debugLog('⏸️ Caught up with target text - parentIsStreaming:', parentIsStreaming);
        if (parentIsStreaming) {
          // Still streaming, wait a bit for more text
          debugLog('⏳ Waiting for more text...');
          animationRef.current = setTimeout(animate, 100);
        } else {
          // Streaming complete and we're caught up
          debugLog('✅ Animation complete - marking as done');
          isAnimatingRef.current = false;
          setIsComplete(true);
          onComplete?.();
        }
      }
    };
    
    // Start with minimal delay
    debugLog('⏰ Starting animation with delay:', delay);
    animationRef.current = setTimeout(animate, delay);
  };
  
  const finishQuickly = () => {
    if (userSkipped || isComplete) return;
    
    // Clear any ongoing animation
    if (animationRef.current) {
      clearTimeout(animationRef.current);
    }
    
    isAnimatingRef.current = true;
    
    const quickFinish = () => {
      const currentTarget = targetTextRef.current;
      const currentDisplayLength = displayLengthRef.current;
      
      if (currentDisplayLength < currentTarget.length) {
        // Show 3-5 characters at a time for quick finish
        displayLengthRef.current = Math.min(currentDisplayLength + 3, currentTarget.length);
        const newDisplayText = currentTarget.slice(0, displayLengthRef.current);
        
        // Force synchronous update
        setDisplayText(newDisplayText);
        
        if (displayLengthRef.current < currentTarget.length) {
          animationRef.current = setTimeout(quickFinish, 30);
        } else {
          isAnimatingRef.current = false;
          setIsComplete(true);
          onComplete?.();
        }
      }
    };
    
    quickFinish();
  };

  // Handle user click to skip animation
  const handleSkip = () => {
    if (enableSkip && !isComplete && !userSkipped && targetTextRef.current) {
      setUserSkipped(true);
      setDisplayText(targetTextRef.current);
      displayLengthRef.current = targetTextRef.current.length;
      setIsComplete(true);

      // Clear any ongoing animation
      if (animationRef.current) {
        clearTimeout(animationRef.current);
        animationRef.current = null;
      }
      
      isAnimatingRef.current = false;
      onComplete?.();
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    };
  }, []);

  const isCurrentlyStreaming = !isComplete && !userSkipped && (
    parentIsStreaming || 
    (displayLengthRef.current < (targetTextRef.current?.length || 0))
  );

  debugLog('🎭 Cursor logic:', {
    isComplete,
    userSkipped, 
    parentIsStreaming,
    displayLength: displayLengthRef.current,
    targetLength: targetTextRef.current?.length,
    isCurrentlyStreaming
  });

  return (
    <div
      className={`relative ${className}`}
      onClick={handleSkip}
      style={{ cursor: enableSkip && isCurrentlyStreaming ? 'pointer' : 'default' }}
      title={enableSkip && isCurrentlyStreaming ? 'Click để bỏ qua streaming' : ''}
    >
      {/* Main text */}
      <span className="whitespace-pre-wrap break-words">
        {displayText}
        {/* DEBUG: Always show target text as fallback */}
        {DEBUG_MODE && !displayText && targetTextRef.current && (
          <span style={{background: 'yellow', color: 'red'}}>
            [DEBUG] Target: {targetTextRef.current.substring(0, 100)}
          </span>
        )}
        {/* DEBUG: Show current state */}
        {DEBUG_MODE && (
          <div style={{fontSize: '10px', color: 'gray', marginTop: '5px'}}>
            Display: {displayText.length} chars | Target: {targetTextRef.current?.length} chars | 
            Complete: {isComplete ? 'YES' : 'NO'} | Streaming: {parentIsStreaming ? 'YES' : 'NO'}
          </div>
        )}
      </span>

      {/* Blinking cursor - show when streaming or when there's more text to come */}
      {showCursor && isCurrentlyStreaming && (
        <span className="streaming-cursor text-blue-500 ml-1 font-bold">
          {cursorChar}
        </span>
      )}
    </div>
  );
};

export default StreamingText;
