const mongoose = require('mongoose');

// Import models
const User = require('./User');
const Credit = require('./Credit');
const Chat = require('./Chat');
const Message = require('./Message');
const Payment = require('./Payment');

// Database connection with retry logic
const connectDB = async () => {
  const maxRetries = 10;
  const retryDelay = 5000; // 5 seconds
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`MongoDB connection attempt ${attempt}/${maxRetries}...`);
      
      // Clean connection - chỉ dùng URI string, không options
      const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/imta-ai');

      console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
      
      // Create indexes
      await Promise.all([
        User.createIndexes(),
        Credit.createIndexes(),
        Chat.createIndexes(),
        Message.createIndexes(),
        Payment.createIndexes()
      ]);

      console.log('✅ Database indexes created successfully');
      
      return conn;
    } catch (error) {
      console.error(`❌ MongoDB connection attempt ${attempt} failed:`, error.message);
      
      if (attempt === maxRetries) {
        console.error('❌ All MongoDB connection attempts failed. Exiting...');
        process.exit(1);
      }
      
      console.log(`⏳ Retrying in ${retryDelay/1000} seconds...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }
};

// Disconnect database
const disconnectDB = async () => {
  try {
    await mongoose.disconnect();
    console.log('✅ MongoDB Disconnected');
  } catch (error) {
    console.error('❌ Database disconnection error:', error);
  }
};

// Export models and connection functions
module.exports = {
  User,
  Credit,
  Chat,
  Message,
  Payment,
  connectDB,
  disconnectDB
}; 