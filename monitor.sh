#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
HEALTH_URL="https://imta.ai/api/health"
ALERT_EMAIL="<EMAIL>"
LOG_FILE="/var/log/imta-monitor.log"

# Function to log messages
log_message() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# Function to log errors
log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" >> $LOG_FILE
}

# Function to send alert
send_alert() {
    local message="$1"
    echo -e "${RED}🚨 ALERT: $message${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ALERT: $message" >> $LOG_FILE
    
    # Send email alert
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "IMTA AI Alert" $ALERT_EMAIL
    fi
    
    # Send webhook notification
    if [ -n "$NOTIFICATION_WEBHOOK" ]; then
        curl -X POST -H "Content-Type: application/json" \
             -d "{\"text\":\"🚨 IMTA AI Alert: $message\"}" \
             $NOTIFICATION_WEBHOOK
    fi
}

# Function to check service health
check_service() {
    local service_name="$1"
    local container_name="$2"
    
    if docker ps --format "table {{.Names}}" | grep -q "$container_name"; then
        local status=$(docker inspect --format='{{.State.Status}}' "$container_name")
        if [ "$status" = "running" ]; then
            log_message "✅ $service_name is running"
            return 0
        else
            log_error "❌ $service_name is not running (status: $status)"
            send_alert "$service_name is not running (status: $status)"
            return 1
        fi
    else
        log_error "❌ $service_name container not found"
        send_alert "$service_name container not found"
        return 1
    fi
}

# Function to check API health
check_api_health() {
    if curl -f -s "$HEALTH_URL" > /dev/null; then
        log_message "✅ API health check passed"
        return 0
    else
        log_error "❌ API health check failed"
        send_alert "API health check failed"
        return 1
    fi
}

# Function to check disk space
check_disk_space() {
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$usage" -gt 80 ]; then
        log_error "⚠️  Disk usage is high: ${usage}%"
        send_alert "Disk usage is high: ${usage}%"
        return 1
    else
        log_message "✅ Disk usage is normal: ${usage}%"
        return 0
    fi
}

# Function to check memory usage
check_memory_usage() {
    local usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$usage" -gt 80 ]; then
        log_error "⚠️  Memory usage is high: ${usage}%"
        send_alert "Memory usage is high: ${usage}%"
        return 1
    else
        log_message "✅ Memory usage is normal: ${usage}%"
        return 0
    fi
}

# Function to check Docker resources
check_docker_resources() {
    local containers=$(docker ps -q | wc -l)
    local images=$(docker images -q | wc -l)
    
    log_message "📊 Docker Status:"
    log_message "  - Running containers: $containers"
    log_message "  - Images: $images"
    
    # Check for unused resources
    local unused_containers=$(docker ps -a --filter "status=exited" -q | wc -l)
    local unused_images=$(docker images --filter "dangling=true" -q | wc -l)
    
    if [ "$unused_containers" -gt 5 ]; then
        log_error "⚠️  Many unused containers: $unused_containers"
    fi
    
    if [ "$unused_images" -gt 10 ]; then
        log_error "⚠️  Many unused images: $unused_images"
    fi
}

# Function to check database connections
check_database() {
    # Check MongoDB
    if docker exec imta-mongodb-prod mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
        log_message "✅ MongoDB is responding"
    else
        log_error "❌ MongoDB is not responding"
        send_alert "MongoDB is not responding"
        return 1
    fi
    
    # Check Redis
    if docker exec imta-redis-prod redis-cli ping > /dev/null 2>&1; then
        log_message "✅ Redis is responding"
    else
        log_error "❌ Redis is not responding"
        send_alert "Redis is not responding"
        return 1
    fi
    
    # Check MinIO
    if curl -f -s http://localhost:9000/minio/health/live > /dev/null; then
        log_message "✅ MinIO is responding"
    else
        log_error "❌ MinIO is not responding"
        send_alert "MinIO is not responding"
        return 1
    fi
}

# Function to check SSL certificate
check_ssl_certificate() {
    local domain="imta.ai"
    local expiry=$(echo | openssl s_client -servername $domain -connect $domain:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    
    if [ -n "$expiry" ]; then
        local expiry_date=$(date -d "$expiry" +%s)
        local current_date=$(date +%s)
        local days_left=$(( (expiry_date - current_date) / 86400 ))
        
        if [ "$days_left" -lt 30 ]; then
            log_error "⚠️  SSL certificate expires in $days_left days"
            send_alert "SSL certificate expires in $days_left days"
        else
            log_message "✅ SSL certificate is valid for $days_left days"
        fi
    else
        log_error "❌ Could not check SSL certificate"
    fi
}

# Function to generate report
generate_report() {
    local report_file="/tmp/imta-monitor-report-$(date +%Y%m%d).txt"
    
    cat > "$report_file" << EOF
IMTA AI Monitoring Report
=========================
Date: $(date)
Host: $(hostname)

System Information:
- CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%
- Memory Usage: $(free | awk 'NR==2{printf "%.1f%%", $3*100/$2}')
- Disk Usage: $(df / | awk 'NR==2 {print $5}')
- Load Average: $(uptime | awk -F'load average:' '{print $2}')

Docker Status:
$(docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}")

Container Health:
$(docker ps --format "{{.Names}}: {{.Status}}" | while read line; do echo "  $line"; done)

Recent Logs:
$(docker-compose -f docker-compose.prod.yml logs --tail=20)

EOF
    
    log_message "📋 Report generated: $report_file"
}

# Main monitoring function
main() {
    log_message "🔍 Starting IMTA AI monitoring..."
    
    local failed_checks=0
    
    # Check all services
    check_service "Application" "imta-app-prod" || ((failed_checks++))
    check_service "MongoDB" "imta-mongodb-prod" || ((failed_checks++))
    check_service "Redis" "imta-redis-prod" || ((failed_checks++))
    check_service "MinIO" "imta-minio-prod" || ((failed_checks++))
    check_service "Nginx" "imta-nginx-prod" || ((failed_checks++))
    
    # Check API health
    check_api_health || ((failed_checks++))
    
    # Check system resources
    check_disk_space || ((failed_checks++))
    check_memory_usage || ((failed_checks++))
    
    # Check Docker resources
    check_docker_resources
    
    # Check database connections
    check_database || ((failed_checks++))
    
    # Check SSL certificate
    check_ssl_certificate
    
    # Generate report
    generate_report
    
    # Summary
    if [ "$failed_checks" -eq 0 ]; then
        log_message "🎉 All checks passed! System is healthy."
    else
        log_error "❌ $failed_checks check(s) failed. System needs attention."
        send_alert "$failed_checks check(s) failed. System needs attention."
    fi
    
    log_message "🔍 Monitoring completed."
}

# Run main function
main "$@" 