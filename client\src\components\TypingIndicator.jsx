import React from 'react';
import { Bot } from 'lucide-react';

const TypingIndicator = ({ show = true, message = "Đang soạn phản hồi..." }) => {
  if (!show) return null;

  return (
    <div className="flex gap-2 sm:gap-3 p-2 sm:p-4 animate-fade-in-up">
      {/* Bot Avatar */}
      <div className="flex-shrink-0">
        <div className="w-6 h-6 sm:w-8 sm:h-8 bg-blue-500 rounded-full flex items-center justify-center">
          <Bot className="w-3 h-3 sm:w-5 sm:h-5 text-white" />
        </div>
      </div>

      {/* Typing Animation */}
      <div className="bg-gray-100 rounded-lg px-3 py-2 sm:px-4 sm:py-2 max-w-[85%] sm:max-w-[75%] lg:max-w-[70%]">
        <div className="flex items-center gap-2 text-gray-600">
          {/* Animated dots */}
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
          
          {/* Typing message */}
          <span className="text-sm sm:text-base">{message}</span>
        </div>
      </div>
    </div>
  );
};

export default TypingIndicator;
