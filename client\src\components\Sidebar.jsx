import React, { useState } from 'react';
import { Plus, MessageSquare, Trash2, Edit3, Check, X, PanelLeftClose, PanelLeftOpen, Bot } from 'lucide-react';
import { useChat } from '../context/ChatContext';
import { useSidebar } from '../context/SidebarContext';
import { getChatTypeDisplayName, getChatTypeColor } from '../utils/chatAnalyzer';

const Sidebar = () => {
  const {
    conversations,
    currentConversationId,
    addConversation,
    setCurrentConversation,
    updateConversation,
    deleteConversation
  } = useChat();

  const {
    isMobile,
    isMobileMenuOpen,
    isSidebarCollapsed,
    closeMobileMenu,
    toggleSidebar
  } = useSidebar();

  const [editingId, setEditingId] = useState(null);
  const [editTitle, setEditTitle] = useState('');
  const [isCreatingChat, setIsCreatingChat] = useState(false);
  const [isUpdatingChat, setIsUpdatingChat] = useState(false);
  const [isDeletingChat, setIsDeletingChat] = useState(null);

  const handleNewConversation = async () => {
    if (isCreatingChat) return; // Prevent multiple clicks
    
    setIsCreatingChat(true);
    try {
      console.log('🆕 Creating new conversation from Sidebar...');
      await addConversation('Cuộc trò chuyện mới');
      console.log('✅ New conversation created successfully');
    } catch (error) {
      console.error('❌ Error creating new conversation:', error);
    } finally {
      setIsCreatingChat(false);
    }
  };

  const handleSelectConversation = async (id) => {
    await setCurrentConversation(id);
    // Close mobile menu when selecting conversation
    if (isMobile) {
      closeMobileMenu();
    }
  };

  const handleDeleteConversation = async (e, id) => {
    e.stopPropagation();
    
    if (isDeletingChat === id) return; // Prevent multiple calls
    
    const confirmed = window.confirm('Bạn có chắc chắn muốn xóa cuộc trò chuyện này? Hành động này không thể hoàn tác.');
    if (!confirmed) return;
    
    setIsDeletingChat(id);
    try {
      console.log('🗑️ Deleting conversation:', id);
      
      await deleteConversation(id);
      
      console.log('✅ Conversation deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting conversation:', error);
      alert('Không thể xóa cuộc trò chuyện');
    } finally {
      setIsDeletingChat(null);
    }
  };

  const handleEditStart = (e, conversation) => {
    e.stopPropagation();
    setEditingId(conversation.id);
    setEditTitle(conversation.title);
  };

  const handleEditSave = async (e) => {
    e.stopPropagation();
    
    if (!editTitle.trim()) {
      alert('Tiêu đề không thể để trống');
      return;
    }
    
    if (isUpdatingChat) return; // Prevent multiple calls
    
    setIsUpdatingChat(true);
    try {
      console.log('💾 Saving conversation title:', editingId, editTitle);
      
      await updateConversation(editingId, { title: editTitle.trim() });
      
      console.log('✅ Conversation title updated successfully');
      
      // Exit edit mode
      setEditingId(null);
      setEditTitle('');
    } catch (error) {
      console.error('❌ Error updating conversation title:', error);
      alert('Không thể cập nhật tiêu đề cuộc trò chuyện');
    } finally {
      setIsUpdatingChat(false);
    }
  };

  const handleEditCancel = (e) => {
    e.stopPropagation();
    setEditingId(null);
    setEditTitle('');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Không xác định';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
    const diffMinutes = Math.ceil(diffTime / (1000 * 60));

    if (diffMinutes < 60) {
      return `${diffMinutes} phút trước`;
    } else if (diffHours < 24) {
      return `${diffHours} giờ trước`;
    } else if (diffDays === 1) {
      return 'Hôm qua';
    } else if (diffDays < 7) {
      return `${diffDays} ngày trước`;
    } else {
      return date.toLocaleDateString('vi-VN');
    }
  };

  return (
    <>
      {/* Mobile Backdrop */}
      {isMobile && isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={closeMobileMenu}
        />
      )}

      {/* Floating Toggle Button (when sidebar is collapsed on desktop) */}
      {!isMobile && isSidebarCollapsed && (
        <button
          onClick={toggleSidebar}
          className="fixed top-4 left-4 z-50 p-3 bg-gray-900 hover:bg-gray-800 text-white rounded-lg shadow-lg transition-all duration-200 hover:scale-105"
          title="Mở sidebar"
        >
          <PanelLeftOpen className="w-5 h-5" />
        </button>
      )}

      {/* Sidebar */}
      <div className={`
        fixed left-0 top-0 h-screen bg-gray-900 text-white flex flex-col
        transition-transform duration-300 ease-in-out z-50
        ${isMobile ? 'w-80 shadow-2xl' : 'w-64'}
        ${isMobile
          ? (isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full')
          : (isSidebarCollapsed ? '-translate-x-full' : 'translate-x-0')
        }
      `}>
        {/* Header */}
        <div className="flex-shrink-0 p-4 border-b border-gray-700">
          {/* Mobile Close Button */}
          {isMobile && (
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold">Cuộc trò chuyện</h2>
              <button
                onClick={closeMobileMenu}
                className="p-2 rounded-lg hover:bg-gray-800 transition-colors"
                aria-label="Close menu"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          )}

          {/* Sidebar Toggle Button - moved to header for better accessibility */}
          <button
            onClick={toggleSidebar}
            className={`
              w-full flex items-center justify-center gap-2 px-4 py-3
              bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white
              rounded-lg transition-colors font-medium
              ${isMobile ? 'min-h-[48px]' : 'min-h-[44px]'}
            `}
            title={isSidebarCollapsed ? "Mở sidebar" : "Thu gọn sidebar"}
          >
            {isSidebarCollapsed ? (
              <>
                <PanelLeftOpen className="w-5 h-5" />
                <span>Mở sidebar</span>
              </>
            ) : (
              <>
                <PanelLeftClose className="w-5 h-5" />
                <span>Thu gọn</span>
              </>
            )}
          </button>
        </div>

      {/* Conversations list */}
      <div className="flex-1 overflow-y-auto sidebar-scrollbar">
        {conversations.length === 0 ? (
          <div className="p-4 text-gray-400 text-center">
            <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Chưa có cuộc trò chuyện nào</p>
          </div>
        ) : (
          <div className="p-2">
            {conversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => handleSelectConversation(conversation.id)}
                className={`group relative p-3 mb-2 rounded-lg cursor-pointer transition-colors ${
                  currentConversationId === conversation.id
                    ? 'bg-gray-700'
                    : 'hover:bg-gray-800'
                }`}
                title={conversation.title}
              >
                {editingId === conversation.id ? (
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      className="flex-1 bg-gray-600 text-white px-2 py-1 rounded text-sm"
                      autoFocus
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleEditSave(e);
                        if (e.key === 'Escape') handleEditCancel(e);
                      }}
                    />
                    <button
                      onClick={handleEditSave}
                      disabled={isUpdatingChat}
                      className={`
                        ${isUpdatingChat 
                          ? 'text-gray-500 cursor-not-allowed' 
                          : 'text-green-400 hover:text-green-300'
                        }
                      `}
                      title={isUpdatingChat ? "Đang lưu..." : "Lưu"}
                    >
                      {isUpdatingChat ? (
                        <div className="w-4 h-4 border border-gray-500 border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <Check className="w-4 h-4" />
                      )}
                    </button>
                    <button
                      onClick={handleEditCancel}
                      className="text-red-400 hover:text-red-300"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <>
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium truncate">
                          {conversation.title}
                        </h3>
                        <div className="flex items-center gap-2 mt-1">
                          <p className="text-xs text-gray-400">
                            {formatDate(conversation.lastMessageAt || conversation.createdAt)}
                          </p>
                          {conversation.type && conversation.type !== 'general' && (
                            <span className={`text-xs px-2 py-0.5 rounded-full ${getChatTypeColor(conversation.type)}`}>
                              {getChatTypeDisplayName(conversation.type)}
                            </span>
                          )}
                          {conversation.status && conversation.status !== 'active' && (
                            <span className="text-xs text-gray-500 bg-gray-700 px-2 py-0.5 rounded-full">
                              {conversation.status}
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {conversation.messageCount || conversation.messages.length} tin nhắn
                          {conversation.backendChatId && (
                            <span className="ml-2 text-green-500">• Đã đồng bộ</span>
                          )}
                        </p>
                      </div>

                      {/* Action buttons */}
                      <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={(e) => handleEditStart(e, conversation)}
                          className="text-gray-400 hover:text-white p-1"
                          title="Đổi tên"
                        >
                          <Edit3 className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => handleDeleteConversation(e, conversation.id)}
                          disabled={isDeletingChat === conversation.id}
                          className={`
                            p-1
                            ${isDeletingChat === conversation.id
                              ? 'text-gray-500 cursor-not-allowed'
                              : 'text-gray-400 hover:text-red-400'
                            }
                          `}
                          title={isDeletingChat === conversation.id ? "Đang xóa..." : "Xóa"}
                        >
                          {isDeletingChat === conversation.id ? (
                            <div className="w-3 h-3 border border-gray-500 border-t-transparent rounded-full animate-spin"></div>
                          ) : (
                            <Trash2 className="w-3 h-3" />
                          )}
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

        {/* Enhanced Footer with Logo and New Conversation */}
        <div className="flex-shrink-0 border-t border-gray-700">
          {/* Logo Section */}
          <div className="p-4 text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <div className="w-8 h-8 rounded-lg overflow-hidden bg-white p-1">
                <img
                  src="/src/assets/imta.png"
                  alt="IMTA"
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="text-left">
                <div className="text-sm font-semibold text-white">IMTA AI</div>
                <div className="text-xs text-gray-400">v2.0</div>
              </div>
            </div>
          </div>

          {/* New Conversation Button */}
          <div className="px-4 pb-4">
            <button
              onClick={handleNewConversation}
              disabled={isCreatingChat}
              className={`
                w-full flex items-center justify-center gap-2 px-4 py-3
                ${isCreatingChat 
                  ? 'bg-blue-500 cursor-not-allowed' 
                  : 'bg-blue-600 hover:bg-blue-700'
                }
                text-white rounded-lg transition-colors font-medium 
                shadow-sm hover:shadow-md
                ${isMobile ? 'min-h-[48px]' : 'min-h-[44px]'}
              `}
              title={isCreatingChat ? "Đang tạo cuộc trò chuyện..." : "Tạo cuộc trò chuyện mới"}
            >
              {isCreatingChat ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Đang tạo...</span>
                </>
              ) : (
                <>
                  <Plus className="w-5 h-5" />
                  <span>Cuộc trò chuyện mới</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
