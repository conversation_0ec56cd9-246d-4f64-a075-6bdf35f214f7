const axios = require('axios');

async function testStreamAPI() {
  try {
    console.log('Testing stream API...');
    
    // Replace with your actual JWT token
    const token = 'your-jwt-token-here';
    
    const response = await axios({
      method: 'POST',
      url: 'http://localhost:5001/api/chat/stream',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      data: {
        message: '<PERSON>n chào, hôm nay ngày mấy?',
        chatId: null // Create new chat
      },
      responseType: 'stream'
    });

    console.log('Stream response started...');

    response.data.on('data', (chunk) => {
      const data = chunk.toString();
      console.log('Received chunk:', data);
    });

    response.data.on('end', () => {
      console.log('Stream ended');
    });

    response.data.on('error', (error) => {
      console.error('Stream error:', error);
    });

  } catch (error) {
    console.error('Test error:', error.message);
  }
}

// Run test
testStreamAPI();
