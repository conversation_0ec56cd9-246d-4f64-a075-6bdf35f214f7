import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { User, Settings, LogOut, ChevronDown, Shield, Menu, Coins } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useSidebar } from '../context/SidebarContext';
import CreditBalance from './CreditBalance';

const TopBar = ({ onTopUpClick }) => {
  const { user, logout } = useAuth();
  const { isMobile, toggleMobileMenu } = useSidebar();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close dropdown on escape key
  useEffect(() => {
    const handleEscapeKey = (event) => {
      if (event.key === 'Escape') {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, []);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleLogout = () => {
    setIsDropdownOpen(false);
    logout();
  };
  const formatDate = (dateString) => {
    if (!dateString) return 'Chưa xác định';
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Chưa xác định';
      
      return date.toLocaleDateString('vi-VN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return 'Chưa xác định';
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className={`
      bg-gray-900 text-white border-b border-gray-700 flex items-center justify-between
      ${isMobile ? 'px-3 py-2' : 'px-4 py-3'}
    `}>
      {/* Left side - Hamburger menu (mobile) + App title */}
      <div className="flex items-center gap-3">
        {/* Hamburger Menu - Mobile only */}
        {isMobile && (
          <button
            onClick={toggleMobileMenu}
            className="p-1 text-white hover:bg-gray-800 rounded transition-colors"
            aria-label="Toggle menu"
          >
            <Menu className="w-5 h-5" />
          </button>
        )}

        {/* ChatBot AI Logo/Brand - Clickable navigation to /chat */}
        <Link
          to="/chat"
          className={`
            flex items-center gap-3 rounded-lg transition-all duration-200
            hover:bg-gray-800/50 focus:outline-none focus:ring-2 focus:ring-blue-500
            ${isMobile ? 'px-2 py-1 min-h-[48px]' : 'px-3 py-2 min-h-[48px]'}
          `}
          title="Về trang chính"
        >
          <img 
            src="/src/assets/imta.png" 
            alt="Imta AI" 
            className={`object-contain transition-transform duration-200 hover:scale-105 ${isMobile ? 'w-8 h-8' : 'w-17 h-10'}`}
          />
          <h1 className={`
            font-semibold transition-colors duration-200 hover:text-blue-200
            ${isMobile ? 'text-base' : 'text-lg hidden sm:block'}
          `}>
            {isMobile ? 'Chat' : 'Imta AI'}
          </h1>
        </Link>
      </div>

      {/* Right side - Credit balance and user info */}
      <div className="flex items-center gap-3">
        {/* Credit Balance - Enhanced for mobile touch */}
        <CreditBalance
          size="normal"
          showDetails={false}
          showTopUpButton={true}
          onTopUpClick={onTopUpClick}
        />

        {/* User dropdown */}
        <div className="relative" ref={dropdownRef}>
        <button
          onClick={toggleDropdown}
          className={`
            flex items-center rounded-lg hover:bg-gray-800 transition-colors
            focus:outline-none focus:ring-2 focus:ring-blue-500
            ${isMobile ? 'gap-2 px-2 py-1' : 'gap-3 px-3 py-2'}
          `}
          aria-expanded={isDropdownOpen}
          aria-haspopup="true"
        >
          {/* User Avatar */}
          <div className={`bg-blue-500 rounded-full flex items-center justify-center ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}`}>
            <User className={`text-white ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} />
          </div>

          {/* User Info - Desktop only */}
          {!isMobile && (
            <div className="text-left">
              <p className="text-sm font-medium">{user.name}</p>
              <p className="text-xs text-gray-400">{user.email}</p>
            </div>
          )}

          {/* Dropdown Arrow */}
          <ChevronDown
            className={`text-gray-400 transition-transform duration-200 ${
              isDropdownOpen ? 'rotate-180' : ''
            } ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`}
          />
        </button>

        {/* Dropdown Menu */}
        {isDropdownOpen && (
          <div className={`
            absolute right-0 top-full mt-2 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50
            ${isMobile ? 'w-72' : 'w-80'}
          `}>
            {/* User Info Header */}
            <div className="px-4 py-3 border-b border-gray-100">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900">{user.name}</h3>
                  <p className="text-sm text-gray-600">{user.email}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Shield className="w-3 h-3" />
                      <span className="capitalize">{user.role}</span>
                    </div>
                    <span className="text-gray-300">•</span>                    <span className="text-xs text-gray-500">
                      Tham gia {formatDate(user.registrationDate || user.createdAt)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="py-2">
              <Link
                to="/profile"
                onClick={() => setIsDropdownOpen(false)}
                className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <Settings className="w-4 h-4" />
                <span>Thông tin tài khoản</span>
              </Link>

              <Link
                to="/credits"
                onClick={() => setIsDropdownOpen(false)}
                className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <Coins className="w-4 h-4" />
                <span>Quản lý Credit</span>
              </Link>

              <Link
                to="/change-password"
                onClick={() => setIsDropdownOpen(false)}
                className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <Shield className="w-4 h-4" />
                <span>Đổi mật khẩu</span>
              </Link>
            </div>

            {/* Logout */}
            <div className="border-t border-gray-100 pt-2">
              <button
                onClick={handleLogout}
                className="w-full flex items-center gap-3 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors"
              >
                <LogOut className="w-4 h-4" />
                <span>Đăng xuất</span>
              </button>
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  );
};

export default TopBar;
