import React, { useState, useEffect } from 'react';
import { X, Co<PERSON>, Star, Check, QrCode, Clock, AlertCircle, ArrowLeft, CreditCard, Smartphone } from 'lucide-react';
import { useCredit } from '../context/CreditContext';

const PaymentModal = ({ isOpen, onClose, onSuccess }) => {
  const { CREDIT_PACKAGES, addCredits, isLoading } = useCredit();
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [paymentStep, setPaymentStep] = useState('select'); // 'select', 'payment', 'processing', 'success'
  const [qrCode, setQrCode] = useState(null);
  const [paymentTimer, setPaymentTimer] = useState(300); // 5 minutes
  const [error, setError] = useState(null);

  // Reset modal state when opened
  useEffect(() => {
    if (isOpen) {
      setSelectedPackage(null);
      setPaymentStep('select');
      setQrCode(null);
      setPaymentTimer(300);
      setError(null);
    }
  }, [isOpen]);

  // Payment timer countdown
  useEffect(() => {
    let interval;
    if (paymentStep === 'payment' && paymentTimer > 0) {
      interval = setInterval(() => {
        setPaymentTimer(prev => {
          if (prev <= 1) {
            setPaymentStep('select');
            setError('Phiên thanh toán đã hết hạn. Vui lòng thử lại.');
            return 300;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [paymentStep, paymentTimer]);

  // Format timer display
  const formatTimer = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  // Generate mock QR code
  const generateQRCode = (packageInfo) => {
    // Mock QR code data
    const qrData = {
      amount: packageInfo.price,
      description: `Mua ${packageInfo.name}`,
      orderId: `ORDER_${Date.now()}`,
      bankCode: 'VIETCOMBANK',
      accountNumber: '**********',
      accountName: 'IMTA AI SERVICE'
    };
    
    // Mock QR code URL (in real app, this would be generated by payment gateway)
    const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(JSON.stringify(qrData))}`;
    
    setQrCode({
      url: qrUrl,
      data: qrData
    });
  };

  // Handle package selection
  const handleSelectPackage = (pkg) => {
    setSelectedPackage(pkg);
    setPaymentStep('payment');
    generateQRCode(pkg);
  };

  // Simulate payment processing
  const handlePaymentConfirm = async () => {
    try {
      setPaymentStep('processing');
      setError(null);

      // Simulate payment verification delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock payment success (90% success rate)
      const isSuccess = Math.random() > 0.1;
      
      if (isSuccess) {
        // Add credits to user account
        const result = await addCredits(
          selectedPackage.credits,
          selectedPackage,
          { method: 'qr_code', orderId: qrCode.data.orderId }
        );

        setPaymentStep('success');
        
        // Call success callback after delay
        setTimeout(() => {
          onSuccess?.(result);
          onClose();
        }, 2000);
        
      } else {
        throw new Error('Thanh toán thất bại. Vui lòng thử lại.');
      }
      
    } catch (err) {
      setError(err.message);
      setPaymentStep('payment');
    }
  };

  // Add animation state
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Handle mount/unmount with animation
  useEffect(() => {
    if (isOpen) {
      setIsMounted(true);
      // Small delay to allow render before starting animation
      const timer = setTimeout(() => setIsVisible(true), 10);
      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
      // Wait for exit animation to complete before unmounting
      const timer = setTimeout(() => setIsMounted(false), 200);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  if (!isMounted && !isOpen) return null;

  return (
    <div 
      className={`fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 backdrop-blur-sm transition-opacity duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
    >
      <div 
        className={`bg-white rounded-2xl shadow-2xl max-w-3xl w-full max-h-[95vh] overflow-y-auto overflow-x-hidden transform transition-all duration-300 ${
          isVisible ? 'translate-y-0 opacity-100' : '-translate-y-4 opacity-0'
        }`}
      >
        {/* Custom scrollbar styling */}
        <style jsx global>{`
          /* For Webkit browsers (Chrome, Safari) */
          ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }
          ::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 3px;
            margin: 4px 0;
          }
          ::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
          }
          ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
          }
          /* For Firefox */
          * {
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
          }
        `}</style>
        {/* Header */}
        <div className="relative bg-gradient-to-r from-blue-600 to-blue-400 text-white p-6 rounded-t-2xl sticky top-0 z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {paymentStep !== 'select' && (
                <button
                  onClick={() => setPaymentStep('select')}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                  title="Quay về"
                >
                  <ArrowLeft className="w-5 h-5" />
                </button>
              )}
              <div>
                <h2 className="text-2xl font-bold">
                  {paymentStep === 'select' && 'Nạp Credit'}
                  {paymentStep === 'payment' && 'Thanh toán'}
                  {paymentStep === 'processing' && 'Đang xử lý'}
                  {paymentStep === 'success' && 'Thành công'}
                </h2>
                <p className="text-blue-100 text-sm mt-1">
                  {paymentStep === 'select' && 'Chọn gói credit phù hợp với bạn'}
                  {paymentStep === 'payment' && 'Quét mã QR để thanh toán'}
                  {paymentStep === 'processing' && 'Vui lòng đợi trong giây lát'}
                  {paymentStep === 'success' && 'Credit đã được nạp thành công'}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Progress indicator */}
          <div className="mt-6 flex items-center justify-center">
            <div className="flex items-center space-x-4">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                paymentStep === 'select' ? 'bg-white text-blue-600 border-white' :
                'bg-blue-500 text-white border-blue-300'
              }`}>
                <span className="text-sm font-semibold">1</span>
              </div>
              <div className={`h-1 w-12 ${
                ['payment', 'processing', 'success'].includes(paymentStep) ? 'bg-white' : 'bg-blue-300'
              }`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                paymentStep === 'payment' ? 'bg-white text-blue-600 border-white' :
                ['processing', 'success'].includes(paymentStep) ? 'bg-blue-500 text-white border-blue-300' :
                'bg-transparent text-blue-200 border-blue-300'
              }`}>
                <span className="text-sm font-semibold">2</span>
              </div>
              <div className={`h-1 w-12 ${
                ['processing', 'success'].includes(paymentStep) ? 'bg-white' : 'bg-blue-300'
              }`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                paymentStep === 'success' ? 'bg-white text-blue-600 border-white' :
                'bg-transparent text-blue-200 border-blue-300'
              }`}>
                <Check className="w-4 h-4" />
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-8">
          {/* Package Selection */}
          {paymentStep === 'select' && (
            <div>
              {/* Custom Credit Amount Input */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium text-gray-700">Nhập số credit cần nạp</label>
                  <div className="text-sm text-gray-500">(Tối thiểu 100 credits)</div>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 sm:items-center">
                  <input
                    type="number"
                    min="100"
                    className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Nhập số credit"
                    onChange={(e) => {
                      const amount = parseInt(e.target.value);
                      if (amount >= 100) {
                        setSelectedPackage({
                          id: 'custom',
                          credits: amount,
                          price: amount * 500, // 500 VND per credit
                          description: `${amount} tin nhắn`
                        });
                      }
                    }}
                  />
                  <button
                    onClick={() => {
                      const input = document.querySelector('input[type="number"]');
                      const amount = parseInt(input.value);
                      if (amount >= 100) {
                        setSelectedPackage({
                          id: 'custom',
                          credits: amount,
                          price: amount * 500, // 500 VND per credit
                          description: `${amount} tin nhắn`
                        });
                        setPaymentStep('payment');
                      }
                    }}
                    className="w-32 sm:w-48 px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    Nạp
                  </button>
                </div>
              </div>

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Chọn gói Credit phù hợp
                </h3>
                <p className="text-gray-600">
                  Tất cả gói đều không có thời hạn sử dụng
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {CREDIT_PACKAGES.map((pkg) => (
                  <div
                    key={pkg.id}
                    className={`
                      relative border-2 rounded-2xl p-6 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg
                      ${pkg.popular
                        ? 'border-gradient-to-r from-blue-500 to-blue-400 bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg'
                        : 'border-gray-200 hover:border-blue-300 bg-white hover:bg-gray-50'
                      }
                    `}
                    onClick={() => handleSelectPackage(pkg)}
                  >
                    {/* Popular badge */}
                    {pkg.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <div className="bg-gradient-to-r from-blue-500 to-blue-400 text-white text-xs px-4 py-2 rounded-full shadow-lg">
                          <Star className="w-3 h-3 inline mr-1" />
                          Phổ biến nhất
                        </div>
                      </div>
                    )}

                    {/* Package info */}
                    <div className="text-center pt-2">
                      <div className="flex items-center justify-center gap-2 mb-4">
                        <div className={`p-2 rounded-full ${pkg.popular ? 'bg-white bg-opacity-20' : 'bg-yellow-100'}`}>
                          <Coins className={`w-6 h-6 ${pkg.popular ? 'text-white' : 'text-yellow-600'}`} />
                        </div>
                        <div>
                          <span className={`text-3xl font-bold ${pkg.popular ? 'text-white' : 'text-gray-900'}`}>
                            {pkg.credits.toLocaleString()}
                          </span>
                          {pkg.bonus && (
                            <span className={`text-lg font-semibold ml-1 ${pkg.popular ? 'text-blue-100' : 'text-green-600'}`}>
                              +{pkg.bonus}
                            </span>
                          )}
                          <div className={`text-sm ${pkg.popular ? 'text-blue-100' : 'text-gray-500'}`}>credits</div>
                        </div>
                      </div>

                      <div className={`rounded-lg p-3 mb-4 ${pkg.popular ? 'bg-white bg-opacity-10' : 'bg-gray-50'}`}>
                        <p className={`text-sm font-medium ${pkg.popular ? 'text-white' : 'text-gray-700'}`}>
                          {pkg.description}
                        </p>
                        {pkg.bonus && (
                          <p className={`text-xs mt-1 ${pkg.popular ? 'text-blue-100' : 'text-green-600'}`}>
                            🎁 Tặng thêm {pkg.bonus} credits
                          </p>
                        )}
                      </div>
                      
                      <div className={`text-2xl font-bold ${pkg.popular ? 'text-white' : 'text-black'} mb-2`}>
                        {formatCurrency(pkg.price)}
                      </div>

                      {/* Select button */}
                      <button className={`
                        w-full mt-4 py-3 px-4 rounded-xl font-semibold transition-all duration-200
                        ${pkg.popular
                          ? 'bg-gradient-to-r from-blue-500 to-blue-400 text-white hover:from-blue-600 hover:to-blue-500 shadow-lg'
                          : 'bg-gray-100 text-gray-700 hover:bg-blue-500 hover:text-white'
                        }
                      `}>
                        Chọn gói này
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Features */}
              <div className="mt-8 bg-gray-50 rounded-2xl p-6">
                <h4 className="font-semibold text-gray-900 mb-4 text-center">
                  ✨ Tính năng của tất cả gói
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2 text-gray-700">
                    <Check className="w-4 h-4 text-green-500" />
                    <span>Không thời hạn sử dụng</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700">
                    <Check className="w-4 h-4 text-green-500" />
                    <span>Hỗ trợ 24/7</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700">
                    <Check className="w-4 h-4 text-green-500" />
                    <span>Thanh toán an toàn</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* QR Payment */}
          {paymentStep === 'payment' && selectedPackage && (
            <div>
              {/* Package summary */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 mb-8 border border-blue-200">
                <div className="text-center">
                  <div className="flex items-center justify-center gap-3 mb-3">
                    <div className="p-2 bg-blue-100 rounded-full">
                      <Coins className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">
                        {selectedPackage.name}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {selectedPackage.credits} credits
                        {selectedPackage.bonus && ` + ${selectedPackage.bonus} bonus`}
                      </p>
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {formatCurrency(selectedPackage.price)}
                  </div>
                  <div className="inline-flex items-center gap-2 bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm">
                    <Check className="w-4 h-4" />
                    <span>Thanh toán một lần, sử dụng vĩnh viễn</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* QR Code */}
                <div className="text-center">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center justify-center gap-2">
                    <Smartphone className="w-5 h-5" />
                    Quét mã QR
                  </h4>

                  {qrCode && (
                    <div className="bg-white rounded-2xl p-6 shadow-lg border-2 border-dashed border-blue-300">
                      <img
                        src={qrCode.url}
                        alt="QR Code"
                        className="w-56 h-56 mx-auto rounded-xl"
                      />
                      <p className="text-sm text-gray-600 mt-4">
                        Sử dụng app ngân hàng để quét mã QR
                      </p>
                    </div>
                  )}
                </div>

                {/* Bank Transfer Info */}
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <CreditCard className="w-5 h-5" />
                    Chuyển khoản ngân hàng
                  </h4>

                  {qrCode && (
                    <div className="bg-gray-50 rounded-2xl p-6 space-y-4">
                      <div className="grid grid-cols-1 gap-4">
                        <div className="bg-white rounded-lg p-4 border">
                          <label className="text-xs text-gray-500 uppercase tracking-wide">Số tài khoản</label>
                          <p className="text-lg font-mono font-bold text-gray-900">{qrCode.data.accountNumber}</p>
                        </div>

                        <div className="bg-white rounded-lg p-4 border">
                          <label className="text-xs text-gray-500 uppercase tracking-wide">Tên tài khoản</label>
                          <p className="font-semibold text-gray-900">{qrCode.data.accountName}</p>
                        </div>

                        <div className="bg-white rounded-lg p-4 border">
                          <label className="text-xs text-gray-500 uppercase tracking-wide">Ngân hàng</label>
                          <p className="font-semibold text-gray-900">{qrCode.data.bankCode}</p>
                        </div>

                        <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                          <label className="text-xs text-yellow-700 uppercase tracking-wide">Nội dung chuyển khoản</label>
                          <p className="font-mono font-bold text-yellow-800">{qrCode.data.orderId}</p>
                          <p className="text-xs text-yellow-600 mt-1">⚠️ Vui lòng ghi chính xác nội dung</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Timer */}
              <div className="mt-8 text-center">
                <div className="inline-flex items-center gap-3 bg-orange-50 text-orange-700 px-6 py-3 rounded-full border border-orange-200">
                  <Clock className="w-5 h-5" />
                  <span className="font-semibold">Thời gian còn lại:</span>
                  <span className="font-mono text-xl font-bold">
                    {formatTimer(paymentTimer)}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  Phiên thanh toán sẽ hết hạn sau thời gian trên
                </p>
              </div>

              {/* Action buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                <button
                  onClick={() => setPaymentStep('select')}
                  className="flex items-center justify-center gap-2 px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors font-semibold"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Chọn gói khác
                </button>
                <button
                  onClick={handlePaymentConfirm}
                  disabled={isLoading}
                  className="flex items-center justify-center gap-2 px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all disabled:opacity-50 font-semibold shadow-lg"
                >
                  <Check className="w-4 h-4" />
                  {isLoading ? 'Đang xử lý...' : 'Tôi đã thanh toán'}
                </button>
              </div>

              <div className="mt-6 text-center text-sm text-gray-500">
                <p>💡 Sau khi chuyển khoản thành công, vui lòng nhấn "Tôi đã thanh toán"</p>
              </div>
            </div>
          )}

          {/* Processing */}
          {paymentStep === 'processing' && (
            <div className="text-center py-12">
              <div className="relative mb-8">
                <div className="animate-spin w-16 h-16 border-4 border-blue-200 border-t-blue-500 rounded-full mx-auto"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <CreditCard className="w-6 h-6 text-blue-500" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">
                Đang xử lý thanh toán
              </h3>
              <p className="text-gray-600 mb-6">
                Chúng tôi đang xác minh giao dịch của bạn
              </p>
              <div className="bg-blue-50 rounded-xl p-4 max-w-md mx-auto">
                <p className="text-blue-700 text-sm">
                  ⏱️ Quá trình này thường mất 1-2 phút
                </p>
              </div>
            </div>
          )}

          {/* Success */}
          {paymentStep === 'success' && (
            <div className="text-center py-12">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto shadow-lg">
                  <Check className="w-10 h-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                  <span className="text-lg">🎉</span>
                </div>
              </div>

              <h3 className="text-3xl font-bold text-gray-900 mb-3">
                Thanh toán thành công!
              </h3>
              <p className="text-gray-600 mb-8 text-lg">
                Credit đã được nạp vào tài khoản của bạn
              </p>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 max-w-md mx-auto border border-green-200">
                <div className="flex items-center justify-center gap-3 mb-4">
                  <div className="p-2 bg-yellow-100 rounded-full">
                    <Coins className="w-6 h-6 text-yellow-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900">
                      +{selectedPackage?.credits + (selectedPackage?.bonus || 0)}
                    </div>
                    <div className="text-sm text-gray-600">credits đã được thêm</div>
                  </div>
                </div>

                {selectedPackage?.bonus && (
                  <div className="bg-green-100 rounded-lg p-3 mt-4">
                    <p className="text-green-700 text-sm font-medium">
                      🎁 Bao gồm {selectedPackage.bonus} bonus credits
                    </p>
                  </div>
                )}
              </div>

              <div className="mt-8 text-sm text-gray-500">
                <p>✨ Bạn có thể bắt đầu sử dụng credits ngay bây giờ</p>
              </div>
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-xl">
              <div className="flex items-center gap-3 text-red-700">
                <div className="p-1 bg-red-100 rounded-full">
                  <AlertCircle className="w-5 h-5" />
                </div>
                <div>
                  <p className="font-semibold">Có lỗi xảy ra</p>
                  <p className="text-sm">{error}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
