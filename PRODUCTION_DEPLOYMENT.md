# 🚀 Production Deployment Guide

## 📋 Prerequisites

- Docker & Docker Compose
- Server with at least 4GB RAM
- Domain name (optional)

## 🎯 Quick Deploy (1 Command)

```bash
chmod +x deploy-prod.sh && ./deploy-prod.sh
```

## 📁 Production Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │   React App     │    │  Express API    │
│   (Port 80)     │───▶│   (Static)      │    │   (Port 5001)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   MongoDB       │    │   Redis         │
                       │   (Port 27017)  │    │   (Port 6379)   │
                       └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   MinIO         │
                       │   (Port 9000)   │
                       └─────────────────┘
```

## 🔧 Manual Deployment Steps

### 1. Setup Environment

```bash
# Clone repository
git clone https://github.com/dangngocbinh/imta-ai.git
cd imta-ai

# Setup environment files
cp server/env.example server/.env
cp client/env.example client/.env
```

### 2. Configure Environment Variables

Edit `server/.env`:
```env
NODE_ENV=production
PORT=5001
MONGODB_URI=****************************************************
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=imta123456
MINIO_ENDPOINT=minio
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=imta123456
LANGFLOW_API_URL=https://your-langflow-instance.com/api/v1/run/your-flow-id
LANGFLOW_API_KEY=your-api-key
JWT_SECRET=your-super-secret-jwt-key
CORS_ORIGIN=*
```

Edit `client/.env`:
```env
REACT_APP_API_URL=http://your-domain.com/api
REACT_APP_ENV=production
```

### 3. Deploy with Docker Compose

```bash
# Build and start all services
docker-compose -f docker-compose.prod.yml up -d --build

# Check status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f
```

## 🌐 Access URLs

After deployment, you can access:

- **Main Application**: http://your-server-ip
- **API Health Check**: http://your-server-ip/api/health
- **Mongo Express**: http://your-server-ip:8081 (admin/admin123)
- **Redis Commander**: http://your-server-ip:8082
- **MinIO Console**: http://your-server-ip:9001 (minioadmin/imta123456)

## 🔄 Management Commands

### Start Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Stop Production
```bash
docker-compose -f docker-compose.prod.yml down
```

### Restart Production
```bash
docker-compose -f docker-compose.prod.yml restart
```

### Update Application
```bash
git pull
docker-compose -f docker-compose.prod.yml up -d --build
```

### View Logs
```bash
# All services
docker-compose -f docker-compose.prod.yml logs -f

# Specific service
docker-compose -f docker-compose.prod.yml logs -f app
docker-compose -f docker-compose.prod.yml logs -f nginx
```

### Backup Database
```bash
# Backup MongoDB
docker exec imta-mongodb-prod mongodump --out /data/backup-$(date +%Y%m%d)

# Copy backup to host
docker cp imta-mongodb-prod:/data/backup-$(date +%Y%m%d) ./backups/
```

## 🔒 Security Considerations

### 1. Change Default Passwords
- MongoDB: `imta_user` / `imta123456`
- Redis: `imta123456`
- MinIO: `minioadmin` / `imta123456`
- Mongo Express: `admin` / `admin123`

### 2. Environment Variables
- Use strong JWT secrets
- Use strong database passwords
- Configure proper CORS origins
- Set up proper API keys

### 3. Network Security
- Use firewall to restrict access
- Consider using VPN for admin access
- Monitor logs for suspicious activity

## 📊 Monitoring

### Health Checks
```bash
# Application health
curl http://your-server-ip/api/health

# Nginx health
curl http://your-server-ip/health
```

### Resource Monitoring
```bash
# Container resource usage
docker stats

# Disk usage
docker system df
```

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using the ports
   lsof -i :80 -i :5001 -i :27017 -i :6379 -i :9000
   ```

2. **Container not starting**
   ```bash
   # Check logs
   docker-compose -f docker-compose.prod.yml logs app
   ```

3. **Database connection issues**
   ```bash
   # Check MongoDB logs
   docker-compose -f docker-compose.prod.yml logs mongodb
   ```

4. **Memory issues**
   ```bash
   # Check memory usage
   docker stats
   ```

### Reset Everything
```bash
# Stop and remove everything
docker-compose -f docker-compose.prod.yml down -v

# Remove all images
docker rmi $(docker images -q)

# Start fresh
./deploy-prod.sh
```

## 📈 Scaling

### Horizontal Scaling
```bash
# Scale API instances
docker-compose -f docker-compose.prod.yml up -d --scale app=3
```

### Load Balancer
Consider using a load balancer (HAProxy, Traefik) for multiple API instances.

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
name: Deploy to Production
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to server
        run: |
          ssh user@server "cd /path/to/app && git pull && ./deploy-prod.sh"
```

## 📞 Support

For issues and questions:
- Check logs: `docker-compose -f docker-compose.prod.yml logs`
- Review this documentation
- Check GitHub issues 