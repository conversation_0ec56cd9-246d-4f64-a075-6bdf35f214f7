import React, { useEffect, useState } from 'react';
import { useChat } from '../context/ChatContext';

const DataFlowDebugger = () => {
  const { getCurrentConversation } = useChat();
  const [debugData, setDebugData] = useState([]);
  
  const currentConversation = getCurrentConversation();
  
  useEffect(() => {
    console.log('🔍 DataFlowDebugger: Conversation changed');
    console.log('📊 Current conversation:', currentConversation);
    console.log('💬 Messages count:', currentConversation?.messages?.length || 0);
    
    if (currentConversation?.messages) {
      const botMessages = currentConversation.messages
        .filter(msg => msg.role === 'assistant')
        .map(msg => ({
          id: msg.id,
          content: msg.content?.substring(0, 100) + '...',
          contentLength: msg.content?.length || 0,
          isStreaming: msg.isStreaming,
          hasContent: !!msg.content,
          timestamp: msg.timestamp
        }));
      
      console.log('🤖 Bot messages:', botMessages);
      setDebugData(botMessages);
    }
  }, [currentConversation]);
  
  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      width: '400px',
      background: 'rgba(0,0,0,0.9)',
      color: 'lime',
      padding: '15px',
      borderRadius: '8px',
      fontFamily: 'monospace',
      fontSize: '12px',
      zIndex: 9999,
      maxHeight: '500px',
      overflow: 'auto'
    }}>
      <div style={{ color: 'yellow', fontWeight: 'bold', marginBottom: '10px' }}>
        🔍 DATA FLOW DEBUG
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        Conversation ID: {currentConversation?.id || 'None'}
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        Total Messages: {currentConversation?.messages?.length || 0}
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        Bot Messages: {debugData.length}
      </div>
      
      {debugData.map((msg, idx) => (
        <div key={msg.id} style={{
          border: '1px solid #333',
          padding: '8px',
          margin: '5px 0',
          borderRadius: '4px',
          background: msg.isStreaming ? 'rgba(255,165,0,0.2)' : 'rgba(0,255,0,0.1)'
        }}>
          <div style={{ color: 'cyan' }}>Message #{idx + 1}</div>
          <div>ID: {msg.id}</div>
          <div>Content Length: {msg.contentLength}</div>
          <div>Is Streaming: {msg.isStreaming ? 'YES' : 'NO'}</div>
          <div>Has Content: {msg.hasContent ? 'YES' : 'NO'}</div>
          <div style={{ marginTop: '5px', color: 'white', fontSize: '10px' }}>
            Content: {msg.content}
          </div>
        </div>
      ))}
      
      <button 
        onClick={() => {
          console.log('🎯 Manual debug trigger');
          console.log('Current conversation:', currentConversation);
        }}
        style={{
          background: '#333',
          color: 'lime',
          border: '1px solid lime',
          padding: '5px 10px',
          borderRadius: '4px',
          cursor: 'pointer',
          marginTop: '10px'
        }}
      >
        Log to Console
      </button>
    </div>
  );
};

export default DataFlowDebugger;
