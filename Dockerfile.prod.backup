# Multi-stage build for production
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY client/package*.json ./client/
COPY server/package*.json ./server/

# Install dependencies
RUN npm run install-all

# Copy source code
COPY . .

# Build client with error handling
RUN cd client && npm run build || (echo "Client build failed" && exit 1)

# Verify build output exists
RUN ls -la /app/client/dist/ || (echo "Build output not found" && exit 1)

# Production stage
FROM node:20-alpine AS production

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY server/package*.json ./server/

# Install only production dependencies
RUN npm ci --only=production --prefix server

# Copy built client from builder stage
COPY --from=builder /app/client/dist /app/public

# Copy server source
COPY server ./server

# Copy necessary files
COPY docker ./docker

# Create necessary directories
RUN mkdir -p /app/uploads /app/logs

# Verify public directory exists and has content
RUN ls -la /app/public/ || (echo "Public directory not found" && exit 1)

# Set ownership
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# Start the application
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server/app.js"] 