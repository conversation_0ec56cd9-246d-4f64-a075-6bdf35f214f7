import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Eye, EyeOff, Mail, Lock, Loader2, AlertCircle, Bot, Sparkles } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import Swal from 'sweetalert2';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});

  const { login, loginLoading, error, clearError } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Clear errors when form data changes
  useEffect(() => {
    if (Object.keys(formData).some(key => formData[key])) {
      clearError();
      setValidationErrors({});
    }
  }, [formData, clearError]);

  // Show SweetAlert2 notification when error occurs
  useEffect(() => {
    if (error) {
      Swal.fire({
        icon: 'error',
        title: '<PERSON>ăng nhập thất bại',
        text: error,
        confirmButtonText: 'Thử lại',
        confirmButtonColor: '#3b82f6',
        background: '#ffffff',
        color: '#374151',
        showClass: {
          popup: 'animate__animated animate__fadeInDown animate__faster'
        },
        hideClass: {
          popup: 'animate__animated animate__fadeOutUp animate__faster'
        }
      });
    }
  }, [error]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!formData.email) {
      errors.email = 'Email là bắt buộc';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email không hợp lệ';
    }

    if (!formData.password) {
      errors.password = 'Mật khẩu là bắt buộc';
    } else if (formData.password.length < 6) {
      errors.password = 'Mật khẩu phải có ít nhất 6 ký tự';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await login(formData.email, formData.password);
      
      // Redirect to the page they were trying to access or chat page
      const from = location.state?.from?.pathname || '/chat';
      navigate(from, { replace: true });
    } catch (error) {
      // Error is handled by AuthContext
      console.error('Login error:', error);
    }
  };


  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-400/20 to-pink-600/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-cyan-400/10 to-blue-600/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          {/* Main Card */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-8 space-y-8">
            {/* Header */}
            <div className="text-center">
              {/* Logo */}
              <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-300">
                <img src="/src/assets/imta.png" alt="Imta AI" className="w-10 h-10 object-contain" />
              </div>

              {/* Brand Name */}
              <div className="mt-4 mb-2">
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  Imta AI
                </h1>
                <div className="flex items-center justify-center gap-1 mt-1">
                  <Sparkles className="w-4 h-4 text-blue-500" />
                  <span className="text-sm text-gray-500 font-medium">v2.0</span>
                </div>
              </div>

              {/* Welcome Message */}
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Chào mừng trở lại!
              </h2>
              <p className="text-sm text-gray-600">
                Đăng nhập để tiếp tục trò chuyện với AI
              </p>

              {/* Register Link */}
              <p className="mt-4 text-sm text-gray-600">
                Chưa có tài khoản?{' '}
                <button
                  onClick={() => {
                    window.location.href = '/register';
                    setTimeout(() => window.location.reload(), 100);
                  }}
                  className="font-semibold text-blue-600 hover:text-blue-700 transition-colors duration-200 hover:underline"
                >
                  Đăng ký ngay
                </button>
              </p>
            </div>


            {/* Login Form */}
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-5">
                {/* Email Field */}
                <div className="space-y-2">
                  <label htmlFor="email" className="block text-sm font-semibold text-gray-700">
                    Địa chỉ Email
                  </label>
                  <div className="relative group">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <Mail className={`h-5 w-5 transition-colors duration-200 ${
                        formData.email ? 'text-blue-500' : 'text-gray-400'
                      }`} />
                    </div>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className={`
                        block w-full pl-12 pr-4 py-3.5 border-2 rounded-xl
                        bg-white/50 backdrop-blur-sm
                        focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500
                        transition-all duration-200 placeholder-gray-400
                        ${validationErrors.email
                          ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
                          : 'border-gray-200 hover:border-gray-300'
                        }
                      `}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  {validationErrors.email && (
                    <div className="flex items-center gap-2 mt-2">
                      <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                      <p className="text-sm text-red-600 font-medium">{validationErrors.email}</p>
                    </div>
                  )}
                </div>

                {/* Password Field */}
                <div className="space-y-2">
                  <label htmlFor="password" className="block text-sm font-semibold text-gray-700">
                    Mật khẩu
                  </label>
                  <div className="relative group">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <Lock className={`h-5 w-5 transition-colors duration-200 ${
                        formData.password ? 'text-blue-500' : 'text-gray-400'
                      }`} />
                    </div>
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      autoComplete="current-password"
                      value={formData.password}
                      onChange={handleInputChange}
                      className={`
                        block w-full pl-12 pr-12 py-3.5 border-2 rounded-xl
                        bg-white/50 backdrop-blur-sm
                        focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500
                        transition-all duration-200 placeholder-gray-400
                        ${validationErrors.password
                          ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
                          : 'border-gray-200 hover:border-gray-300'
                        }
                      `}
                      placeholder="••••••••"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-4 flex items-center group"
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors duration-200" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors duration-200" />
                      )}
                    </button>
                  </div>
                  {validationErrors.password && (
                    <div className="flex items-center gap-2 mt-2">
                      <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                      <p className="text-sm text-red-600 font-medium">{validationErrors.password}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={loginLoading}
                className="
                  group relative w-full flex justify-center items-center py-4 px-6
                  text-sm font-semibold rounded-xl text-white
                  bg-gradient-to-r from-blue-600 to-indigo-600
                  hover:from-blue-700 hover:to-indigo-700
                  focus:outline-none focus:ring-4 focus:ring-blue-500/30
                  disabled:opacity-50 disabled:cursor-not-allowed
                  transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]
                  shadow-lg hover:shadow-xl
                "
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                {loginLoading ? (
                  <>
                    <Loader2 className="h-5 w-5 mr-3 animate-spin" />
                    <span>Đang đăng nhập...</span>
                  </>
                ) : (
                  <>
                    <span>Đăng nhập</span>
                    <div className="ml-2 transform group-hover:translate-x-1 transition-transform duration-200">
                      →
                    </div>
                  </>
                )}
              </button>

              {/* Footer Links */}
              <div className="text-center pt-4">
                <Link
                  to="/forgot-password"
                  className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200 hover:underline"
                >
                  Quên mật khẩu?
                </Link>
              </div>
            </form>
          </div>

          {/* Footer */}
          <div className="text-center mt-8">
            <p className="text-xs text-gray-500">
              © 2024 Imta AI. Powered by advanced AI technology.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
