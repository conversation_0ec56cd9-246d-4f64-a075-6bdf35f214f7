const fs = require('fs');
const path = require('path');

// Supported languages
const SUPPORTED_LANGUAGES = ['vi', 'en'];
const DEFAULT_LANGUAGE = 'vi';

// Cache for loaded translations
const translations = {};

// Load translation files
function loadTranslations() {
  SUPPORTED_LANGUAGES.forEach(lang => {
    try {
      const filePath = path.join(__dirname, '..', 'locales', `${lang}.json`);
      const content = fs.readFileSync(filePath, 'utf8');
      translations[lang] = JSON.parse(content);
    } catch (error) {
      console.error(`Failed to load translation file for ${lang}:`, error);
      translations[lang] = {};
    }
  });
}

// Get language from request headers
function getLanguageFromRequest(req) {
  // Check Accept-Language header
  const acceptLanguage = req.headers['accept-language'];
  if (acceptLanguage) {
    const lang = acceptLanguage.split(',')[0].split('-')[0].toLowerCase();
    if (SUPPORTED_LANGUAGES.includes(lang)) {
      return lang;
    }
  }

  // Check custom header
  const customLang = req.headers['x-language'] || req.headers['x-lang'];
  if (customLang && SUPPORTED_LANGUAGES.includes(customLang.toLowerCase())) {
    return customLang.toLowerCase();
  }

  // Check query parameter
  const queryLang = req.query.lang || req.query.language;
  if (queryLang && SUPPORTED_LANGUAGES.includes(queryLang.toLowerCase())) {
    return queryLang.toLowerCase();
  }

  // Check user preferences if authenticated
  if (req.user && req.user.preferences && req.user.preferences.language) {
    const userLang = req.user.preferences.language;
    if (SUPPORTED_LANGUAGES.includes(userLang)) {
      return userLang;
    }
  }

  return DEFAULT_LANGUAGE;
}

// Get nested object value using dot notation
function getNestedValue(obj, path) {
  if (!path || typeof path !== 'string') {
    return null;
  }
  
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : null;
  }, obj);
}

// Replace placeholders in message
function replacePlaceholders(message, params = {}) {
  return message.replace(/\{(\w+)\}/g, (match, key) => {
    return params[key] !== undefined ? params[key] : match;
  });
}

// Main translation function
function t(key, params = {}, language = null) {
  const lang = language || DEFAULT_LANGUAGE;
  const langTranslations = translations[lang] || translations[DEFAULT_LANGUAGE];
  
  let message = getNestedValue(langTranslations, key);
  
  // Fallback to default language if message not found
  if (!message && lang !== DEFAULT_LANGUAGE) {
    message = getNestedValue(translations[DEFAULT_LANGUAGE], key);
  }
  
  // Fallback to key if no translation found
  if (!message) {
    console.warn(`Translation key not found: ${key} for language: ${lang}`);
    return key;
  }
  
  // Replace placeholders
  return replacePlaceholders(message, params);
}

// Middleware to add i18n to request
function i18nMiddleware(req, res, next) {
  req.language = getLanguageFromRequest(req);
  req.t = (key, params = {}) => t(key, params, req.language);
  next();
}

// Helper function to send localized response
function sendLocalizedResponse(res, statusCode, success, messageKey, data = null, params = {}) {
  const response = {
    success,
    message: res.req.t(messageKey, params)
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  res.status(statusCode).json(response);
}

// Helper function to send localized error response
function sendLocalizedError(res, statusCode, messageKey, params = {}) {
  sendLocalizedResponse(res, statusCode, false, messageKey, null, params);
}

// Helper function to send localized success response
function sendLocalizedSuccess(res, statusCode, messageKey, data = null, params = {}) {
  sendLocalizedResponse(res, statusCode, true, messageKey, data, params);
}

// Helper function to send localized validation error response
function sendLocalizedValidationError(res, statusCode, messageKey, fieldErrors = {}, fieldParams = {}) {
  const response = {
    success: false,
    message: t(messageKey, {}, res.req.language)
  };
  
  if (Object.keys(fieldErrors).length > 0) {
    response.errors = {};
    Object.keys(fieldErrors).forEach(field => {
      const params = fieldParams[field] || {};
      response.errors[field] = t(fieldErrors[field], params, res.req.language);
    });
  }
  
  res.status(statusCode).json(response);
}

// Initialize translations on module load
loadTranslations();

module.exports = {
  t,
  i18nMiddleware,
  sendLocalizedResponse,
  sendLocalizedError,
  sendLocalizedSuccess,
  sendLocalizedValidationError,
  getLanguageFromRequest,
  SUPPORTED_LANGUAGES,
  DEFAULT_LANGUAGE
}; 