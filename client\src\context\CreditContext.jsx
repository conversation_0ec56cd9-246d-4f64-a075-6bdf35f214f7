import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';

const CreditContext = createContext();

export const useCredit = () => {
  const context = useContext(CreditContext);
  if (!context) {
    throw new Error('useCredit must be used within a CreditProvider');
  }
  return context;
};

// Mock credit packages for purchase
export const CREDIT_PACKAGES = [
  {
    id: '50k',
    credits: 100,
    price: 50000, // VND
    description: '100 tin nhắn',
    popular: false
  },
  {
    id: '100k',
    credits: 250,
    price: 100000, // VND
    description: '250 tin nhắn',
    popular: true
  },
  {
    id: '150k',
    credits: 500,
    price: 150000, // VND
    description: '500 tin nhắn',
    popular: false
  },
  {
    id: '200k',
    credits: 1000,
    price: 200000, // VND
    description: '1000 tin nhắn',
    popular: false
  }
];

// Credit cost per action
export const CREDIT_COSTS = {
  message: 1,           // 1 credit per message
  fileUpload: 2,        // 2 credits per file upload
  imageGeneration: 5,   // 5 credits per image generation
  chartGeneration: 3    // 3 credits per chart generation
};

export const CreditProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const [creditBalance, setCreditBalance] = useState(0);
  const [transactions, setTransactions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Initialize user credits when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserCredits();
      loadTransactionHistory();
    } else {
      // Reset when logged out
      setCreditBalance(0);
      setTransactions([]);
    }
  }, [isAuthenticated, user?.id]); // Only depend on user.id instead of entire user object

  // Mock API: Load user credits
  const loadUserCredits = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock credit balance based on user role
      const mockBalance = user?.role === 'admin' ? 1000 : 50;
      setCreditBalance(mockBalance);
      
    } catch (err) {
      setError('Không thể tải thông tin credit');
      console.error('Error loading credits:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Mock API: Load transaction history
  const loadTransactionHistory = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Mock transaction history
      const mockTransactions = [
        {
          id: 'tx_001',
          type: 'purchase',
          amount: 100,
          cost: 50000,
          description: 'Mua gói Cơ Bản',
          timestamp: new Date(Date.now() - 86400000), // 1 day ago
          status: 'completed',
          paymentMethod: 'qr_code'
        },
        {
          id: 'tx_002',
          type: 'usage',
          amount: -5,
          description: 'Gửi 5 tin nhắn',
          timestamp: new Date(Date.now() - 3600000), // 1 hour ago
          status: 'completed'
        },
        {
          id: 'tx_003',
          type: 'usage',
          amount: -2,
          description: 'Upload file',
          timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
          status: 'completed'
        }
      ];
      
      setTransactions(mockTransactions);
      
    } catch (err) {
      console.error('Error loading transaction history:', err);
    }
  };

  // Check if user has enough credits for an action
  const hasEnoughCredits = (action) => {
    const cost = CREDIT_COSTS[action] || 1;
    return creditBalance >= cost;
  };

  // Consume credits for an action
  const consumeCredits = async (action, description = '') => {
    const cost = CREDIT_COSTS[action] || 1;
    
    if (!hasEnoughCredits(action)) {
      throw new Error('Không đủ credit để thực hiện hành động này');
    }

    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Update balance
      const newBalance = creditBalance - cost;
      setCreditBalance(newBalance);
      
      // Add transaction record
      const transaction = {
        id: `tx_${Date.now()}`,
        type: 'usage',
        amount: -cost,
        description: description || `Sử dụng ${action}`,
        timestamp: new Date(),
        status: 'completed'
      };
      
      setTransactions(prev => [transaction, ...prev]);
      
      return { success: true, newBalance, cost };
      
    } catch (err) {
      setError('Không thể trừ credit');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Add credits (from purchase)
  const addCredits = async (amount, packageInfo, paymentInfo) => {
    try {
      setIsLoading(true);
      
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Calculate total credits (including bonus)
      const totalCredits = amount + (packageInfo.bonus || 0);
      
      // Update balance
      const newBalance = creditBalance + totalCredits;
      setCreditBalance(newBalance);
      
      // Add transaction record
      const transaction = {
        id: `tx_${Date.now()}`,
        type: 'purchase',
        amount: totalCredits,
        cost: packageInfo.price,
        description: `Mua ${packageInfo.name}`,
        timestamp: new Date(),
        status: 'completed',
        paymentMethod: paymentInfo.method,
        packageId: packageInfo.id
      };
      
      setTransactions(prev => [transaction, ...prev]);
      
      return { success: true, newBalance, transaction };
      
    } catch (err) {
      setError('Không thể thêm credit');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Get credit usage statistics
  const getCreditStats = () => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    
    const todayUsage = transactions
      .filter(t => t.type === 'usage' && new Date(t.timestamp) >= today)
      .reduce((sum, t) => sum + Math.abs(t.amount), 0);
    
    const monthlyUsage = transactions
      .filter(t => t.type === 'usage' && new Date(t.timestamp) >= thisMonth)
      .reduce((sum, t) => sum + Math.abs(t.amount), 0);
    
    const totalPurchased = transactions
      .filter(t => t.type === 'purchase')
      .reduce((sum, t) => sum + t.amount, 0);
    
    return {
      todayUsage,
      monthlyUsage,
      totalPurchased,
      currentBalance: creditBalance
    };
  };

  // Clear error
  const clearError = () => setError(null);

  const value = {
    // State
    creditBalance,
    transactions,
    isLoading,
    error,
    
    // Actions
    hasEnoughCredits,
    consumeCredits,
    addCredits,
    loadUserCredits,
    loadTransactionHistory,
    getCreditStats,
    clearError,
    
    // Constants
    CREDIT_PACKAGES,
    CREDIT_COSTS
  };

  return (
    <CreditContext.Provider value={value}>
      {children}
    </CreditContext.Provider>
  );
};

export default CreditProvider;
