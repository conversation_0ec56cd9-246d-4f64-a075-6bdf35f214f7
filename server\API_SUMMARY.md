# API Summary - IMTA AI Chat System

## Authentication APIs (`/api/auth`)

### POST `/api/auth/register` - Đăng ký tài khoản
- **Description**: Đăng ký tài khoản mới
- **Body**: `{ username, email, password, fullName, phone? }`
- **Response**: `{ user, token }`

### POST `/api/auth/login` - Đăng nhập
- **Description**: Đăng nhập vào hệ thống
- **Body**: `{ email, password }`
- **Response**: `{ user, token }`

### POST `/api/auth/logout` - Đăng xuất ⭐ NEW
- **Description**: Đăng xuất khỏi hệ thống
- **Headers**: `Authorization: Bearer <token>`
- **Response**: Success message

### GET `/api/auth/me` - Thông tin tài khoản
- **Description**: L<PERSON>y thông tin tài khoản hiện tại
- **Headers**: `Authorization: Bearer <token>`
- **Response**: `{ user }`

### PUT `/api/auth/me` - <PERSON><PERSON><PERSON> nhật thông tin
- **Description**: Cập nhật thông tin tài khoản
- **Headers**: `Authorization: Bearer <token>`
- **Body**: `{ fullName?, phone?, avatar?, preferences? }`
- **Response**: `{ user }`

### POST `/api/auth/change-password` - Đổi mật khẩu
- **Description**: Thay đổi mật khẩu
- **Headers**: `Authorization: Bearer <token>`
- **Body**: `{ currentPassword, newPassword }`
- **Response**: Success message

## Chat APIs (`/api/chat`)

### POST `/api/chat` - Gửi tin nhắn (AI thật)
- **Description**: Gửi tin nhắn và nhận phản hồi từ Langflow AI
- **Headers**: `Authorization: Bearer <token>`
- **Body**: `{ message, chatId? }`
- **Response**: `{ chatId, response, messageId }`

### POST `/api/chat/message` - Gửi tin nhắn (Mock AI với format) ⭐ UPDATED
- **Description**: Gửi tin nhắn với phản hồi giả lập từ AI (có markdown formatting)
- **Headers**: `Authorization: Bearer <token>`
- **Body**: `{ message, chatId? }`
- **Response**: `{ chatId, response, messageId, isMock: true, format: 'markdown' }`
- **Features**: 
  - Markdown formatting (headers, lists, tables, code blocks)
  - Emojis và styling
  - Contextual responses based on keywords
  - Rich text formatting cho frontend rendering

### GET `/api/chat/history` - Lịch sử chat
- **Description**: Lấy danh sách các cuộc hội thoại
- **Headers**: `Authorization: Bearer <token>`
- **Query**: `{ page?, limit?, status? }`
- **Response**: `{ chats, pagination }`

### GET `/api/chat/:chatId` - Chi tiết chat
- **Description**: Lấy chi tiết cuộc hội thoại và các tin nhắn
- **Headers**: `Authorization: Bearer <token>`
- **Query**: `{ page?, limit? }`
- **Response**: `{ chat, messages, pagination }`

### POST `/api/chat/create` - Tạo chat mới
- **Description**: Tạo cuộc hội thoại mới
- **Headers**: `Authorization: Bearer <token>`
- **Body**: `{ title, type?, metadata? }`
- **Response**: `{ chat }`

### PUT `/api/chat/:chatId` - Cập nhật chat
- **Description**: Cập nhật thông tin cuộc hội thoại
- **Headers**: `Authorization: Bearer <token>`
- **Body**: `{ title?, status?, metadata? }`
- **Response**: `{ chat }`

### DELETE `/api/chat/:chatId` - Xóa chat
- **Description**: Xóa cuộc hội thoại và tất cả tin nhắn
- **Headers**: `Authorization: Bearer <token>`
- **Response**: Success message

### POST `/api/chat/:chatId/messages` - Gửi tin nhắn vào chat cụ thể
- **Description**: Gửi tin nhắn vào cuộc hội thoại đã tồn tại
- **Headers**: `Authorization: Bearer <token>`
- **Body**: `{ message }`
- **Response**: `{ userMessage, botMessage, response }`

## Payment APIs (`/api/payment`)

### POST `/api/payment/create` - Tạo lệnh thanh toán
- **Description**: Tạo lệnh thanh toán mới
- **Headers**: `Authorization: Bearer <token>`
- **Body**: `{ amount, method }`
- **Response**: `{ payment, paymentUrl }`

### POST `/api/payment/callback` - Callback thanh toán
- **Description**: Xử lý callback từ cổng thanh toán
- **Body**: Payment callback data
- **Response**: Success message

### GET `/api/payment/history` - Lịch sử thanh toán
- **Description**: Lấy lịch sử thanh toán của người dùng
- **Headers**: `Authorization: Bearer <token>`
- **Query**: `{ page?, limit?, status? }`
- **Response**: `{ payments, pagination }`

### GET `/api/payment/:paymentId` - Chi tiết thanh toán
- **Description**: Lấy chi tiết giao dịch thanh toán
- **Headers**: `Authorization: Bearer <token>`
- **Response**: `{ payment }`

## User APIs (`/api/user`)

### GET `/api/user/list` - Danh sách người dùng
- **Description**: Lấy danh sách người dùng (Admin only)
- **Headers**: `Authorization: Bearer <token>`
- **Query**: `{ page?, limit?, role? }`
- **Response**: `{ users, pagination }`

## Mock AI Response Features (Enhanced)

The mock message API (`/api/chat/message`) now includes rich formatting:

### 🎨 **Markdown Formatting:**
- **Headers**: `#`, `##`, `###`
- **Bold/Italic**: `**text**`, `*text*`
- **Lists**: Bullet points và numbered lists
- **Code blocks**: `\`\`\`` cho code
- **Tables**: Markdown table format
- **Blockquotes**: `>` cho quotes
- **Emojis**: 🚀💡📚🎯

### 🧠 **Smart Contextual Responses:**
- **Greetings**: "Xin chào", "Hello", "Hi" → Rich welcome message
- **Thanks**: "Cảm ơn", "Thank" → Appreciation with tips
- **Goodbyes**: "Tạm biệt", "Goodbye", "Bye" → Farewell summary
- **AI Topics**: "AI", "artificial intelligence" → Detailed AI explanation
- **Machine Learning**: "machine learning", "ML" → Comprehensive ML guide
- **Chatbots**: "chatbot", "bot" → Chatbot architecture & applications
- **General Responses**: Random formatted responses with structure

### 💡 **Frontend Integration Benefits:**
- **Rich Text Rendering**: Frontend có thể render markdown thành HTML đẹp
- **Streaming Compatible**: Markdown có thể được stream từng phần
- **Consistent Styling**: Formatting nhất quán across responses
- **Interactive Elements**: Tables, lists, code blocks tương tác được

### 📱 **Example Response Format:**
```json
{
  "success": true,
  "message": "Message sent successfully",
  "data": {
    "chatId": "chat_id",
    "response": "# 🤖 AI Response\n\n## Features:\n- **Bold text**\n- *Italic text*\n- `Code snippets`\n\n> Quote block",
    "messageId": "message_id",
    "isMock": true,
    "format": "markdown"
  }
}
```

## Authentication

All protected endpoints require:
```
Authorization: Bearer <jwt_token>
```

## Error Handling

All APIs return consistent error responses:
```json
{
  "success": false,
  "message": "Error message",
  "code": "error_code"
}
```

## Success Responses

All APIs return consistent success responses:
```json
{
  "success": true,
  "message": "Success message",
  "data": { ... }
}
```

## Pagination

List endpoints support pagination:
```json
{
  "pagination": {
    "current": 1,
    "total": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
``` 