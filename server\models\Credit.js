const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['earn', 'spend', 'refund'],
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: [0, 'Amount cannot be negative']
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  service: {
    type: String,
    enum: ['chat', 'ads_analysis', 'content_gen', 'avatar_analysis', 'bonus', 'purchase'],
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
});

const creditSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  balance: {
    type: Number,
    required: true,
    default: 0,
    min: [0, 'Balance cannot be negative']
  },
  totalEarned: {
    type: Number,
    required: true,
    default: 0,
    min: [0, 'Total earned cannot be negative']
  },
  totalSpent: {
    type: Number,
    required: true,
    default: 0,
    min: [0, 'Total spent cannot be negative']
  },
  transactions: [transactionSchema],
  lastUpdated: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for available balance
creditSchema.virtual('availableBalance').get(function() {
  return Math.max(0, this.balance);
});

// Virtual for transaction count
creditSchema.virtual('transactionCount').get(function() {
  return this.transactions.length;
});

// Indexes
// creditSchema.index({ userId: 1 }, { unique: true }); // ❌ Dòng này đã xóa để tránh Duplicate Index warning
creditSchema.index({ balance: 1 });
creditSchema.index({ 'transactions.timestamp': -1 });

// Pre-save middleware to update lastUpdated
creditSchema.pre('save', function(next) {
  this.lastUpdated = new Date();
  next();
});

// Static methods
creditSchema.statics.findByUserId = function(userId) {
  return this.findOne({ userId }).populate('userId', 'username email fullName');
};

creditSchema.statics.findLowBalanceUsers = function(threshold = 10) {
  return this.find({ balance: { $lt: threshold } }).populate('userId', 'username email');
};

// Instance methods
creditSchema.methods.addCredits = function(amount, description, service, metadata = {}) {
  if (amount <= 0) {
    throw new Error('Amount must be positive');
  }

  this.balance += amount;
  this.totalEarned += amount;
  
  this.transactions.push({
    type: 'earn',
    amount,
    description,
    service,
    metadata
  });

  return this.save();
};

creditSchema.methods.spendCredits = function(amount, description, service, metadata = {}) {
  if (amount <= 0) {
    throw new Error('Amount must be positive');
  }

  if (this.balance < amount) {
    throw new Error('Insufficient credits');
  }

  this.balance -= amount;
  this.totalSpent += amount;
  
  this.transactions.push({
    type: 'spend',
    amount,
    description,
    service,
    metadata
  });

  return this.save();
};

creditSchema.methods.refundCredits = function(amount, description, service, metadata = {}) {
  if (amount <= 0) {
    throw new Error('Amount must be positive');
  }

  this.balance += amount;
  this.totalEarned += amount;
  
  this.transactions.push({
    type: 'refund',
    amount,
    description,
    service,
    metadata
  });

  return this.save();
};

creditSchema.methods.getTransactionHistory = function(limit = 50, offset = 0) {
  return this.transactions
    .sort({ timestamp: -1 })
    .slice(offset, offset + limit);
};

creditSchema.methods.getTransactionsByService = function(service) {
  return this.transactions.filter(t => t.service === service);
};

creditSchema.methods.getTransactionsByType = function(type) {
  return this.transactions.filter(t => t.type === type);
};

creditSchema.methods.getMonthlyStats = function(month, year) {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0, 23, 59, 59);
  
  const monthlyTransactions = this.transactions.filter(t => 
    t.timestamp >= startDate && t.timestamp <= endDate
  );

  return {
    totalEarned: monthlyTransactions
      .filter(t => t.type === 'earn')
      .reduce((sum, t) => sum + t.amount, 0),
    totalSpent: monthlyTransactions
      .filter(t => t.type === 'spend')
      .reduce((sum, t) => sum + t.amount, 0),
    transactionCount: monthlyTransactions.length
  };
};

module.exports = mongoose.model('Credit', creditSchema); 