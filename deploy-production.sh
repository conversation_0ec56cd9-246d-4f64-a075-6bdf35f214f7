#!/bin/bash

echo "🚀 Starting IMTA-AI Production Deployment..."

# Stop and remove existing containers
echo "📦 Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down

# Remove old images to force rebuild
echo "🗑️ Cleaning up old images..."
docker rmi imta-ai_app || true

# Build with no cache to ensure fresh build
echo "🔨 Building application..."
docker-compose -f docker-compose.prod.yml build --no-cache app

# Start all services
echo "🌟 Starting all services..."
docker-compose -f docker-compose.prod.yml up -d

# Sync assets to CloudPanel
echo "🔄 Syncing assets to CloudPanel..."
./sync-assets.sh

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 15

# Check health
echo "🏥 Checking service health..."
echo "API Health:"
curl -s http://localhost:5001/api/health | head -2

echo -e "\n🌐 Frontend Status:"
curl -I http://localhost:5001/ 2>/dev/null | head -1

echo -e "\n📊 Container Status:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo -e "\n✅ Deployment Complete!"
echo "🔗 Access your application at:"
echo "   Frontend: https://imta.ai"
echo "   API: https://imta.ai/api/health"
echo "   Local Frontend: http://localhost:5001"
echo "   Local API: http://localhost:5001/api/health"
echo "   MongoDB Admin: http://localhost:8081"
echo "   Redis Admin: http://localhost:8082"
echo "   MinIO Console: http://localhost:9001"

