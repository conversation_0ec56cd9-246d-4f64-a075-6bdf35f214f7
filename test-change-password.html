<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Change Password API</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 600px;
            margin: 0 auto;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h2 { color: #333; margin-top: 0; }
        label { display: block; margin: 10px 0 5px; font-weight: bold; }
        input, button { 
            padding: 8px; 
            margin: 5px 0; 
            width: 100%; 
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button { 
            background: #007bff; 
            color: white; 
            cursor: pointer;
            font-weight: bold;
        }
        button:hover { background: #0056b3; }
        .result { 
            margin-top: 15px; 
            padding: 10px; 
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Change Password API</h1>
        
        <div class="section">
            <h2>1. Login First (Required for Change Password)</h2>
            <label>Email:</label>
            <input type="email" id="loginEmail" placeholder="Enter email" value="<EMAIL>">
            
            <label>Password:</label>
            <input type="password" id="loginPassword" placeholder="Enter password" value="123456">
            
            <button onclick="testLogin()">🔑 Login</button>
            <div id="loginResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h2>2. Change Password</h2>
            <label>Current Password:</label>
            <input type="password" id="currentPassword" placeholder="Enter current password" value="123456">
            
            <label>New Password:</label>
            <input type="password" id="newPassword" placeholder="Enter new password" value="654321">
            
            <button onclick="testChangePassword()">🔄 Change Password</button>
            <div id="changePasswordResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h2>3. Token Info</h2>
            <p id="tokenInfo">No token available</p>
            <button onclick="clearToken()">🗑️ Clear Token</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let authToken = localStorage.getItem('authToken');

        function updateTokenInfo() {
            const tokenElement = document.getElementById('tokenInfo');
            if (authToken) {
                const tokenParts = authToken.split('.');
                if (tokenParts.length === 3) {
                    try {
                        const payload = JSON.parse(atob(tokenParts[1]));
                        tokenElement.innerHTML = `
                            <strong>Token:</strong> ${authToken.substring(0, 20)}...<br>
                            <strong>User ID:</strong> ${payload.userId}<br>
                            <strong>Email:</strong> ${payload.email}<br>
                            <strong>Role:</strong> ${payload.role}<br>
                            <strong>Expires:</strong> ${new Date(payload.exp * 1000).toLocaleString()}
                        `;
                    } catch (e) {
                        tokenElement.textContent = `Token: ${authToken.substring(0, 50)}...`;
                    }
                } else {
                    tokenElement.textContent = `Invalid token format: ${authToken.substring(0, 50)}...`;
                }
            } else {
                tokenElement.textContent = 'No token available';
            }
        }

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        function showError(elementId, error) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result error';
            element.textContent = `Error: ${error.message || error}`;
        }

        async function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                showError('loginResult', 'Please enter email and password');
                return;
            }

            try {
                console.log('🔄 Testing login...');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                console.log('Login response:', data);

                if (response.ok && data.success) {
                    authToken = data.data.token;
                    localStorage.setItem('authToken', authToken);
                    showResult('loginResult', data);
                    updateTokenInfo();
                } else {
                    showError('loginResult', data.message || 'Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                showError('loginResult', error);
            }
        }

        async function testChangePassword() {
            if (!authToken) {
                showError('changePasswordResult', 'Please login first to get auth token');
                return;
            }

            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            
            if (!currentPassword || !newPassword) {
                showError('changePasswordResult', 'Please enter current and new password');
                return;
            }

            try {
                console.log('🔄 Testing change password...');
                console.log('Current Password:', currentPassword);
                console.log('New Password:', newPassword);
                console.log('Auth Token:', authToken.substring(0, 20) + '...');
                
                const response = await fetch(`${API_BASE}/auth/change-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ 
                        currentPassword, 
                        newPassword 
                    })
                });

                const data = await response.json();
                console.log('Change password response:', data);
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);

                if (response.ok && data.success) {
                    showResult('changePasswordResult', {
                        success: true,
                        message: data.message,
                        status: response.status,
                        note: 'Password changed successfully!'
                    });
                } else {
                    showError('changePasswordResult', {
                        message: data.message || 'Change password failed',
                        status: response.status,
                        success: data.success,
                        fullResponse: data
                    });
                }
            } catch (error) {
                console.error('Change password error:', error);
                showError('changePasswordResult', error);
            }
        }

        function clearToken() {
            authToken = null;
            localStorage.removeItem('authToken');
            updateTokenInfo();
            
            // Clear results
            document.getElementById('loginResult').style.display = 'none';
            document.getElementById('changePasswordResult').style.display = 'none';
        }

        // Initialize
        updateTokenInfo();
    </script>
</body>
</html>
